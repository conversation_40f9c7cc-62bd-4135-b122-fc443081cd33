# Seiri-chan 系统设计文档

## 1. 系统架构

### 1.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  React Components │ Next.js Pages │ 状态管理 │ 国际化支持    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                        API层 (API Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  Next.js API Routes │ 数据验证 │ 错误处理 │ 中间件          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     业务逻辑层 (Business Logic)               │
├─────────────────────────────────────────────────────────────┤
│  任务管理器 │ 配置管理器 │ 传统识别引擎 │ AI识别引擎        │
│  文件操作模块 │ TMDB客户端 │ AI客户端 │ 缓存管理           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       数据层 (Data Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  Prisma ORM │ SQLite/PostgreSQL │ 文件系统 │ 缓存存储       │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

#### 前端技术栈
- **Next.js 14**: 全栈 React 框架，支持 SSR 和 API Routes
- **TypeScript**: 类型安全的 JavaScript 超集
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Radix UI**: 无样式的可访问 UI 组件库
- **next-intl**: Next.js 国际化解决方案
- **Lucide React**: 现代图标库

#### 后端技术栈
- **Node.js**: JavaScript 运行时
- **Prisma**: 现代数据库 ORM
- **SQLite/PostgreSQL**: 关系型数据库
- **Server-Sent Events**: 实时通信

#### 外部服务
- **TMDB API**: 电影和电视剧元数据
- **OpenAI API**: AI 文本生成和分析
- **Google Gemini API**: 替代 AI 服务

## 2. 数据库设计

### 2.1 数据模型

#### Config 表 - 配置管理
```sql
CREATE TABLE configs (
    id TEXT PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value JSON NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### TaskBatch 表 - 批量任务
```sql
CREATE TABLE task_batches (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    total_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    failed_tasks INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### Task 表 - 任务管理
```sql
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- tv|movie|anime|anime_movie
    status TEXT DEFAULT 'pending',
    source_path TEXT NOT NULL,
    source_files JSON,
    metadata JSON,
    mapping JSON,
    result JSON,
    progress REAL DEFAULT 0,
    error_message TEXT,
    batch_id TEXT REFERENCES task_batches(id),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
);
```

#### TaskLog 表 - 任务日志
```sql
CREATE TABLE task_logs (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL REFERENCES tasks(id),
    level TEXT NOT NULL, -- info|warn|error|debug
    message TEXT NOT NULL,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### TMDBCache 表 - TMDB 缓存
```sql
CREATE TABLE tmdb_cache (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL, -- tv|movie
    tmdb_id INTEGER NOT NULL,
    language TEXT DEFAULT 'zh-CN',
    data JSON NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    UNIQUE(type, tmdb_id, language)
);
```

#### AITestCase 表 - AI 测试用例
```sql
CREATE TABLE ai_test_cases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    input_data JSON NOT NULL,
    expected_output JSON NOT NULL,
    actual_output JSON,
    score REAL,
    status TEXT DEFAULT 'pending',
    task_id TEXT REFERENCES tasks(id),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 数据关系

- TaskBatch 1:N Task (一个批次包含多个任务)
- Task 1:N TaskLog (一个任务有多条日志)
- Task 1:N AITestCase (一个任务可生成多个测试用例)

## 3. API 设计

### 3.1 RESTful API 端点

#### 任务管理 API
```typescript
// 创建单个任务
POST /api/tasks
Body: {
  name: string;
  type: 'tv' | 'movie' | 'anime' | 'anime_movie';
  sourcePath: string;
  options?: TaskOptions;
}

// 创建批量任务
POST /api/tasks/batch
Body: {
  name: string;
  paths: string[];
  type: 'tv' | 'movie' | 'anime' | 'anime_movie';
  depth?: number;
  options?: TaskOptions;
}

// 获取任务列表
GET /api/tasks?page=1&limit=20&status=all&type=all

// 获取任务详情
GET /api/tasks/:id

// 更新任务
PUT /api/tasks/:id
Body: Partial<Task>

// 删除任务
DELETE /api/tasks/:id

// 重试任务
POST /api/tasks/:id/retry

// 取消任务
POST /api/tasks/:id/cancel
```

#### 配置管理 API
```typescript
// 获取所有配置
GET /api/config

// 更新配置
PUT /api/config
Body: Record<string, any>

// 测试配置
POST /api/config/test
Body: {
  type: 'tmdb' | 'openai' | 'gemini';
  config: Record<string, any>;
}
```

#### 文件系统 API
```typescript
// 浏览文件系统
GET /api/files?path=/&type=directory

// 扫描路径
POST /api/files/scan
Body: {
  path: string;
  depth?: number;
}
```

#### AI 相关 API
```typescript
// 测试 AI 功能
POST /api/ai/test
Body: {
  provider: 'openai' | 'gemini';
  testCase: AITestCase;
}

// 获取测试用例
GET /api/ai/test-cases?taskId=xxx

// 创建测试用例
POST /api/ai/test-cases
Body: AITestCase
```

### 3.2 实时通信

#### Server-Sent Events
```typescript
// 订阅任务状态更新
GET /api/events?types=task_update,task_log

// 事件格式
interface SSEEvent {
  type: 'task_update' | 'task_log' | 'system_status';
  data: any;
  timestamp: string;
}
```

## 4. 核心模块设计

### 4.1 任务管理器 (TaskManager)

```typescript
class TaskManager {
  // 创建任务
  async createTask(params: CreateTaskParams): Promise<Task>
  
  // 创建批量任务
  async createBatchTasks(params: CreateBatchTasksParams): Promise<TaskBatch>
  
  // 执行任务
  async executeTask(taskId: string): Promise<void>
  
  // 取消任务
  async cancelTask(taskId: string): Promise<void>
  
  // 重试任务
  async retryTask(taskId: string): Promise<void>
  
  // 获取任务状态
  async getTaskStatus(taskId: string): Promise<TaskStatus>
}
```

### 4.2 识别引擎 (RecognitionEngine)

#### 传统识别引擎
```typescript
class TraditionalRecognitionEngine {
  // 清洗文件名
  cleanFileName(fileName: string): string
  
  // 提取季度信息
  extractSeasonInfo(fileName: string): SeasonInfo
  
  // 提取集数信息
  extractEpisodeInfo(fileName: string): EpisodeInfo
  
  // 识别媒体类型
  recognizeMediaType(path: string): MediaType
  
  // 生成文件映射
  generateFileMapping(files: string[], metadata: TMDBMetadata): FileMapping[]
}
```

#### AI 识别引擎
```typescript
class AIRecognitionEngine {
  // AI 分析
  async analyzeFiles(
    files: VideoFile[], 
    metadata: TMDBMetadata
  ): Promise<AIAnalysisResult>
  
  // 应用 AI 映射
  applyAIMapping(
    traditionalMapping: FileMapping[], 
    aiResult: AIAnalysisResult
  ): FileMapping[]
  
  // 生成测试用例
  generateTestCase(
    input: AIInput, 
    output: AIAnalysisResult
  ): AITestCase
}
```

### 4.3 文件操作模块 (FileOperations)

```typescript
class FileOperations {
  // 创建硬链接
  async createHardLink(source: string, target: string): Promise<void>
  
  // 创建软链接
  async createSymLink(source: string, target: string): Promise<void>
  
  // 复制文件
  async copyFile(source: string, target: string): Promise<void>
  
  // 移动文件
  async moveFile(source: string, target: string): Promise<void>
  
  // 批量操作
  async batchOperation(
    operations: FileOperation[], 
    mode: OperationMode
  ): Promise<OperationResult[]>
  
  // 回退操作
  async rollbackOperations(operations: FileOperation[]): Promise<void>
}
```

### 4.4 TMDB 客户端 (TMDBClient)

```typescript
class TMDBClient {
  // 搜索电视剧
  async searchTV(query: string, language?: string): Promise<TVSearchResult[]>
  
  // 搜索电影
  async searchMovie(query: string, language?: string): Promise<MovieSearchResult[]>
  
  // 获取电视剧详情
  async getTVDetails(id: number, language?: string): Promise<TVDetails>
  
  // 获取电影详情
  async getMovieDetails(id: number, language?: string): Promise<MovieDetails>
  
  // 获取季度信息
  async getSeasonDetails(tvId: number, seasonNumber: number): Promise<SeasonDetails>
}
```

### 4.5 AI 客户端 (AIClient)

```typescript
abstract class AIClient {
  abstract async analyze(
    prompt: string, 
    data: any, 
    schema?: any
  ): Promise<AIResponse>
}

class OpenAIClient extends AIClient {
  async analyze(prompt: string, data: any, schema?: any): Promise<AIResponse>
}

class GeminiClient extends AIClient {
  async analyze(prompt: string, data: any, schema?: any): Promise<AIResponse>
}
```

## 5. 前端组件设计

### 5.1 页面组件

```typescript
// 主页
HomePage: React.FC

// 任务管理页面
TasksPage: React.FC
├── TaskList: React.FC<{ tasks: Task[] }>
├── TaskDetail: React.FC<{ task: Task }>
├── CreateTaskDialog: React.FC
└── BatchCreateDialog: React.FC

// 配置页面
ConfigPage: React.FC
├── TMDBConfig: React.FC
├── AIConfig: React.FC
├── PathConfig: React.FC
└── GeneralConfig: React.FC
```

### 5.2 通用组件

```typescript
// 文件浏览器
FileBrowser: React.FC<{
  onSelect: (paths: string[]) => void;
  multiple?: boolean;
  type?: 'file' | 'directory';
}>

// 任务状态指示器
TaskStatusIndicator: React.FC<{ status: TaskStatus }>

// 进度条
ProgressBar: React.FC<{ progress: number }>

// 实时日志查看器
LogViewer: React.FC<{ taskId: string }>
```

## 6. 安全设计

### 6.1 输入验证
- 所有用户输入进行严格验证
- 路径参数防止目录遍历攻击
- API 参数类型和格式验证

### 6.2 权限控制
- 文件系统访问权限检查
- API 密钥安全存储
- 敏感配置加密存储

### 6.3 错误处理
- 统一的错误处理机制
- 敏感信息不暴露给前端
- 详细的错误日志记录

## 7. 性能优化

### 7.1 数据库优化
- 合理的索引设计
- 查询优化
- 连接池管理

### 7.2 缓存策略
- TMDB API 响应缓存
- 静态资源缓存
- 计算结果缓存

### 7.3 并发处理
- 任务队列管理
- 并发限制
- 资源竞争处理

## 8. 监控和日志

### 8.1 日志系统
- 结构化日志格式
- 日志级别管理
- 日志轮转和清理

### 8.2 性能监控
- API 响应时间监控
- 资源使用监控
- 错误率统计

### 8.3 健康检查
- 系统健康状态检查
- 外部服务可用性检查
- 自动故障恢复
