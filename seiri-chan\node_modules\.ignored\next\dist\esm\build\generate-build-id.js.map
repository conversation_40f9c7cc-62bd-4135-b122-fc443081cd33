{"version": 3, "sources": ["../../src/build/generate-build-id.ts"], "sourcesContent": ["export async function generateBuildId(\n  generate: () => string | null | Promise<string | null>,\n  fallback: () => string\n): Promise<string> {\n  let buildId = await generate()\n  // If there's no buildId defined we'll fall back\n  if (buildId === null) {\n    // We also create a new buildId if it contains the word `ad` to avoid false\n    // positives with ad blockers\n    while (!buildId || /ad/i.test(buildId)) {\n      buildId = fallback()\n    }\n  }\n\n  if (typeof buildId !== 'string') {\n    throw new Error(\n      'generateBuildId did not return a string. https://nextjs.org/docs/messages/generatebuildid-not-a-string'\n    )\n  }\n\n  return buildId.trim()\n}\n"], "names": ["generateBuildId", "generate", "fallback", "buildId", "test", "Error", "trim"], "mappings": "AAAA,OAAO,eAAeA,gBACpBC,QAAsD,EACtDC,QAAsB;IAEtB,IAAIC,UAAU,MAAMF;IACpB,gDAAgD;IAChD,IAAIE,YAAY,MAAM;QACpB,2EAA2E;QAC3E,6BAA6B;QAC7B,MAAO,CAACA,WAAW,MAAMC,IAAI,CAACD,SAAU;YACtCA,UAAUD;QACZ;IACF;IAEA,IAAI,OAAOC,YAAY,UAAU;QAC/B,MAAM,qBAEL,CAFK,IAAIE,MACR,2GADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOF,QAAQG,IAAI;AACrB", "ignoreList": [0]}