{"version": 3, "sources": ["../../../../src/build/webpack/loaders/error-loader.ts"], "sourcesContent": ["import { cyan } from '../../../lib/picocolors'\nimport path from 'path'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nconst ErrorLoader: webpack.LoaderDefinitionFunction = function () {\n  // @ts-ignore exists\n  const options = this.getOptions() || ({} as any)\n\n  const { reason = 'An unknown error has occurred' } = options\n\n  // @ts-expect-error\n  const resource = this._module?.issuer?.resource ?? null\n  const context = this.rootContext ?? this._compiler?.context\n\n  const issuer = resource\n    ? context\n      ? path.relative(context, resource)\n      : resource\n    : null\n\n  const err = new Error(reason + (issuer ? `\\nLocation: ${cyan(issuer)}` : ''))\n  this.emitError(err)\n}\n\nexport default ErrorLoader\n"], "names": ["cyan", "path", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "getOptions", "reason", "resource", "_module", "issuer", "context", "rootContext", "_compiler", "relative", "err", "Error", "emitError"], "mappings": "AAAA,SAASA,IAAI,QAAQ,0BAAyB;AAC9C,OAAOC,UAAU,OAAM;AAGvB,MAAMC,cAAgD;QAOnC,sBAAA,eACmB;IAPpC,oBAAoB;IACpB,MAAMC,UAAU,IAAI,CAACC,UAAU,MAAO,CAAC;IAEvC,MAAM,EAAEC,SAAS,+BAA+B,EAAE,GAAGF;IAErD,mBAAmB;IACnB,MAAMG,WAAW,EAAA,gBAAA,IAAI,CAACC,OAAO,sBAAZ,uBAAA,cAAcC,MAAM,qBAApB,qBAAsBF,QAAQ,KAAI;IACnD,MAAMG,UAAU,IAAI,CAACC,WAAW,MAAI,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBF,OAAO;IAE3D,MAAMD,SAASF,WACXG,UACER,KAAKW,QAAQ,CAACH,SAASH,YACvBA,WACF;IAEJ,MAAMO,MAAM,qBAAiE,CAAjE,IAAIC,MAAMT,SAAUG,CAAAA,SAAS,CAAC,YAAY,EAAER,KAAKQ,SAAS,GAAG,EAAC,IAA9D,qBAAA;eAAA;oBAAA;sBAAA;IAAgE;IAC5E,IAAI,CAACO,SAAS,CAACF;AACjB;AAEA,eAAeX,YAAW", "ignoreList": [0]}