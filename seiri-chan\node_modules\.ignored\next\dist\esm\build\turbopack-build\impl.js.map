{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport {\n  rawEntrypointsToEntrypoints,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\nimport { isCI } from '../../server/ci-info'\nimport { backgroundLogCompilationEvents } from '../../shared/lib/turbopack/compilation-events'\nimport { getSupportedBrowsers } from '../utils'\nimport { normalizePath } from '../../lib/normalize-path'\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n  const currentNodeJsVersion = process.versions.node\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const rootPath = config.turbopack?.root || config.outputFileTracingRoot || dir\n  const project = await bindings.turbo.createProject(\n    {\n      rootPath: config.turbopack?.root || config.outputFileTracingRoot || dir,\n      projectPath: normalizePath(path.relative(rootPath, dir) || '.'),\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        projectPath: dir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n        rewrites,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n      currentNodeJsVersion,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental?.turbopackMemoryLimit,\n      dependencyTracking: persistentCaching,\n      isCi: isCI,\n    }\n  )\n  try {\n    backgroundLogCompilationEvents(project)\n\n    // Write an empty file in a known location to signal this was built with Turbopack\n    await fs.writeFile(path.join(distDir, 'turbopack'), '')\n\n    await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n    await fs.mkdir(path.join(distDir, 'static', buildId), {\n      recursive: true,\n    })\n    await fs.writeFile(\n      path.join(distDir, 'package.json'),\n      JSON.stringify(\n        {\n          type: 'commonjs',\n        },\n        null,\n        2\n      )\n    )\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const entrypoints = await project.writeAllEntrypointsToDisk(appDirOnly)\n\n    const manifestLoader = new TurbopackManifestLoader({\n      buildId,\n      distDir,\n      encryptionKey,\n    })\n\n    const topLevelErrors = []\n    const topLevelWarnings = []\n    for (const issue of entrypoints.issues) {\n      if (issue.severity === 'error' || issue.severity === 'fatal') {\n        topLevelErrors.push(formatIssue(issue))\n      } else if (isRelevantWarning(issue)) {\n        topLevelWarnings.push(formatIssue(issue))\n      }\n    }\n\n    if (topLevelWarnings.length > 0) {\n      console.warn(\n        `Turbopack build encountered ${\n          topLevelWarnings.length\n        } warnings:\\n${topLevelWarnings.join('\\n')}`\n      )\n    }\n\n    if (topLevelErrors.length > 0) {\n      throw new Error(\n        `Turbopack build failed with ${\n          topLevelErrors.length\n        } errors:\\n${topLevelErrors.join('\\n')}`\n      )\n    }\n\n    const currentEntrypoints = await rawEntrypointsToEntrypoints(entrypoints)\n\n    const promises: Promise<any>[] = []\n\n    if (!appDirOnly) {\n      for (const [page, route] of currentEntrypoints.page) {\n        promises.push(\n          handleRouteType({\n            page,\n            route,\n            manifestLoader,\n          })\n        )\n      }\n    }\n\n    for (const [page, route] of currentEntrypoints.app) {\n      promises.push(\n        handleRouteType({\n          page,\n          route,\n          manifestLoader,\n        })\n      )\n    }\n\n    await Promise.all(promises)\n\n    await Promise.all([\n      manifestLoader.loadBuildManifest('_app'),\n      manifestLoader.loadPagesManifest('_app'),\n      manifestLoader.loadFontManifest('_app'),\n      manifestLoader.loadPagesManifest('_document'),\n      manifestLoader.loadBuildManifest('_error'),\n      manifestLoader.loadPagesManifest('_error'),\n      manifestLoader.loadFontManifest('_error'),\n      entrypoints.instrumentation &&\n        manifestLoader.loadMiddlewareManifest(\n          'instrumentation',\n          'instrumentation'\n        ),\n      entrypoints.middleware &&\n        (await manifestLoader.loadMiddlewareManifest(\n          'middleware',\n          'middleware'\n        )),\n    ])\n\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites: rewrites,\n      entrypoints: currentEntrypoints,\n    })\n\n    const shutdownPromise = project.shutdown()\n\n    const time = process.hrtime(startTime)\n    return {\n      duration: time[0] + time[1] / 1e9,\n      buildTraceContext: undefined,\n      shutdownPromise,\n    }\n  } catch (err) {\n    await project.shutdown()\n    throw err\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!,\n    { debugPrerender: NextBuildContext.debugPrerender }\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["path", "validateTurboNextConfig", "formatIssue", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isRelevantWarning", "NextBuildContext", "createDefineEnv", "loadBindings", "rawEntrypointsToEntrypoints", "handleRouteType", "TurbopackManifestLoader", "promises", "fs", "PHASE_PRODUCTION_BUILD", "loadConfig", "hasCustomExportOutput", "Telemetry", "setGlobal", "isCI", "backgroundLogCompilationEvents", "getSupportedBrowsers", "normalizePath", "turbopackBuild", "config", "dir", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "currentNodeJsVersion", "process", "versions", "node", "startTime", "hrtime", "bindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "persistentCaching", "rootPath", "turbopack", "root", "outputFileTracingRoot", "project", "turbo", "createProject", "projectPath", "relative", "nextConfig", "jsConfig", "watch", "enable", "env", "defineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "turbopackMemoryLimit", "dependencyTracking", "isCi", "writeFile", "mkdir", "recursive", "JSON", "stringify", "type", "entrypoints", "writeAllEntrypointsToDisk", "manifest<PERSON><PERSON>der", "topLevelErrors", "topLevelWarnings", "issue", "issues", "severity", "push", "length", "console", "warn", "Error", "currentEntrypoints", "page", "route", "app", "Promise", "all", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "instrumentation", "loadMiddlewareManifest", "middleware", "writeManifests", "devRewrites", "productionRewrites", "shutdownPromise", "shutdown", "time", "duration", "buildTraceContext", "err", "worker<PERSON>ain", "workerData", "Object", "assign", "buildContext", "debugPrerender", "telemetry", "result", "waitForShutdown"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SACEC,WAAW,EACXC,oBAAoB,EACpBC,0BAA0B,EAC1BC,iBAAiB,QACZ,mCAAkC;AACzC,SAASC,gBAAgB,QAAQ,mBAAkB;AACnD,SAASC,eAAe,EAAEC,YAAY,QAAQ,SAAQ;AACtD,SACEC,2BAA2B,EAC3BC,eAAe,QACV,wBAAuB;AAC9B,SAASC,uBAAuB,QAAQ,6CAA4C;AACpF,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,sBAAsB,QAAQ,6BAA4B;AACnE,OAAOC,gBAAgB,sBAAqB;AAC5C,SAASC,qBAAqB,QAAQ,qBAAoB;AAC1D,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,8BAA8B,QAAQ,gDAA+C;AAC9F,SAASC,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,aAAa,QAAQ,2BAA0B;AAExD,OAAO,eAAeC;QAuBgBC,sBAMnBA,mBAGHA,oBAgCGA;IA3DjB,MAAMvB,wBAAwB;QAC5BwB,KAAKnB,iBAAiBmB,GAAG;QACzBC,OAAO;IACT;IAEA,MAAMF,SAASlB,iBAAiBkB,MAAM;IACtC,MAAMC,MAAMnB,iBAAiBmB,GAAG;IAChC,MAAME,UAAUrB,iBAAiBqB,OAAO;IACxC,MAAMC,UAAUtB,iBAAiBsB,OAAO;IACxC,MAAMC,gBAAgBvB,iBAAiBuB,aAAa;IACpD,MAAMC,eAAexB,iBAAiBwB,YAAY;IAClD,MAAMC,cAAczB,iBAAiByB,WAAW;IAChD,MAAMC,WAAW1B,iBAAiB0B,QAAQ;IAC1C,MAAMC,aAAa3B,iBAAiB2B,UAAU;IAC9C,MAAMC,aAAa5B,iBAAiB4B,UAAU;IAC9C,MAAMC,uBAAuBC,QAAQC,QAAQ,CAACC,IAAI;IAElD,MAAMC,YAAYH,QAAQI,MAAM;IAChC,MAAMC,WAAW,MAAMjC,aAAagB,2BAAAA,uBAAAA,OAAQkB,YAAY,qBAApBlB,qBAAsBmB,aAAa;IACvE,MAAMC,MAAM;IAEZ,MAAMC,oBAAoB,MAAMxB,qBAAqBI,KAAKmB;IAE1D,MAAME,oBAAoB1C,2BAA2BoB;IACrD,MAAMuB,WAAWvB,EAAAA,oBAAAA,OAAOwB,SAAS,qBAAhBxB,kBAAkByB,IAAI,KAAIzB,OAAO0B,qBAAqB,IAAIzB;IAC3E,MAAM0B,UAAU,MAAMV,SAASW,KAAK,CAACC,aAAa,CAChD;QACEN,UAAUvB,EAAAA,qBAAAA,OAAOwB,SAAS,qBAAhBxB,mBAAkByB,IAAI,KAAIzB,OAAO0B,qBAAqB,IAAIzB;QACpE6B,aAAahC,cAActB,KAAKuD,QAAQ,CAACR,UAAUtB,QAAQ;QAC3DE;QACA6B,YAAYhC;QACZiC,UAAU,MAAMtD,qBAAqBsB,KAAKD;QAC1CkC,OAAO;YACLC,QAAQ;QACV;QACAf;QACAgB,KAAKxB,QAAQwB,GAAG;QAChBC,WAAWtD,gBAAgB;YACzBuD,aAAa;YACbC,qBAAqBzD,iBAAiByD,mBAAmB;YACzDvC;YACAoB;YACAjB;YACA2B,aAAa7B;YACbuC,qBAAqBxC,OAAOkB,YAAY,CAACsB,mBAAmB;YAC5DjC;YACA,uEAAuE;YACvEkC,oBAAoBC;YACpBlC;QACF;QACAJ;QACAC;QACAC;QACAqC,mBAAmBtB,kBAAkBuB,IAAI,CAAC;QAC1ClC;QACAC;IACF,GACA;QACEW;QACAuB,WAAW,GAAE7C,wBAAAA,OAAOkB,YAAY,qBAAnBlB,sBAAqB8C,oBAAoB;QACtDC,oBAAoBzB;QACpB0B,MAAMrD;IACR;IAEF,IAAI;QACFC,+BAA+B+B;QAE/B,kFAAkF;QAClF,MAAMtC,GAAG4D,SAAS,CAACzE,KAAKoE,IAAI,CAACzC,SAAS,cAAc;QAEpD,MAAMd,GAAG6D,KAAK,CAAC1E,KAAKoE,IAAI,CAACzC,SAAS,WAAW;YAAEgD,WAAW;QAAK;QAC/D,MAAM9D,GAAG6D,KAAK,CAAC1E,KAAKoE,IAAI,CAACzC,SAAS,UAAUC,UAAU;YACpD+C,WAAW;QACb;QACA,MAAM9D,GAAG4D,SAAS,CAChBzE,KAAKoE,IAAI,CAACzC,SAAS,iBACnBiD,KAAKC,SAAS,CACZ;YACEC,MAAM;QACR,GACA,MACA;QAIJ,6DAA6D;QAC7D,MAAMC,cAAc,MAAM5B,QAAQ6B,yBAAyB,CAAC/C;QAE5D,MAAMgD,iBAAiB,IAAItE,wBAAwB;YACjDiB;YACAD;YACAE;QACF;QAEA,MAAMqD,iBAAiB,EAAE;QACzB,MAAMC,mBAAmB,EAAE;QAC3B,KAAK,MAAMC,SAASL,YAAYM,MAAM,CAAE;YACtC,IAAID,MAAME,QAAQ,KAAK,WAAWF,MAAME,QAAQ,KAAK,SAAS;gBAC5DJ,eAAeK,IAAI,CAACrF,YAAYkF;YAClC,OAAO,IAAI/E,kBAAkB+E,QAAQ;gBACnCD,iBAAiBI,IAAI,CAACrF,YAAYkF;YACpC;QACF;QAEA,IAAID,iBAAiBK,MAAM,GAAG,GAAG;YAC/BC,QAAQC,IAAI,CACV,CAAC,4BAA4B,EAC3BP,iBAAiBK,MAAM,CACxB,YAAY,EAAEL,iBAAiBf,IAAI,CAAC,OAAO;QAEhD;QAEA,IAAIc,eAAeM,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAIL,CAJK,IAAIG,MACR,CAAC,4BAA4B,EAC3BT,eAAeM,MAAM,CACtB,UAAU,EAAEN,eAAed,IAAI,CAAC,OAAO,GAHpC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QAEA,MAAMwB,qBAAqB,MAAMnF,4BAA4BsE;QAE7D,MAAMnE,WAA2B,EAAE;QAEnC,IAAI,CAACqB,YAAY;YACf,KAAK,MAAM,CAAC4D,MAAMC,MAAM,IAAIF,mBAAmBC,IAAI,CAAE;gBACnDjF,SAAS2E,IAAI,CACX7E,gBAAgB;oBACdmF;oBACAC;oBACAb;gBACF;YAEJ;QACF;QAEA,KAAK,MAAM,CAACY,MAAMC,MAAM,IAAIF,mBAAmBG,GAAG,CAAE;YAClDnF,SAAS2E,IAAI,CACX7E,gBAAgB;gBACdmF;gBACAC;gBACAb;YACF;QAEJ;QAEA,MAAMe,QAAQC,GAAG,CAACrF;QAElB,MAAMoF,QAAQC,GAAG,CAAC;YAChBhB,eAAeiB,iBAAiB,CAAC;YACjCjB,eAAekB,iBAAiB,CAAC;YACjClB,eAAemB,gBAAgB,CAAC;YAChCnB,eAAekB,iBAAiB,CAAC;YACjClB,eAAeiB,iBAAiB,CAAC;YACjCjB,eAAekB,iBAAiB,CAAC;YACjClB,eAAemB,gBAAgB,CAAC;YAChCrB,YAAYsB,eAAe,IACzBpB,eAAeqB,sBAAsB,CACnC,mBACA;YAEJvB,YAAYwB,UAAU,IACnB,MAAMtB,eAAeqB,sBAAsB,CAC1C,cACA;SAEL;QAED,MAAMrB,eAAeuB,cAAc,CAAC;YAClCC,aAAavC;YACbwC,oBAAoB1E;YACpB+C,aAAaa;QACf;QAEA,MAAMe,kBAAkBxD,QAAQyD,QAAQ;QAExC,MAAMC,OAAOzE,QAAQI,MAAM,CAACD;QAC5B,OAAO;YACLuE,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;YAC9BE,mBAAmB7C;YACnByC;QACF;IACF,EAAE,OAAOK,KAAK;QACZ,MAAM7D,QAAQyD,QAAQ;QACtB,MAAMI;IACR;AACF;AAEA,IAAIL;AACJ,OAAO,eAAeM,WAAWC,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAC9G,kBAAkB4G,WAAWG,YAAY;IAEvD,iDAAiD;IACjD/G,iBAAiBkB,MAAM,GAAG,MAAMT,WAC9BD,wBACAR,iBAAiBmB,GAAG,EACpB;QAAE6F,gBAAgBhH,iBAAiBgH,cAAc;IAAC;IAGpD,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAItG,sBAAsBV,iBAAiBkB,MAAM,GAAG;QAClDlB,iBAAiBkB,MAAM,CAACG,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAM4F,YAAY,IAAItG,UAAU;QAC9BU,SAASrB,iBAAiBkB,MAAM,CAACG,OAAO;IAC1C;IACAT,UAAU,aAAaqG;IAEvB,MAAMC,SAAS,MAAMjG;IACrBoF,kBAAkBa,OAAOb,eAAe;IACxC,OAAOa;AACT;AAEA,OAAO,eAAeC;IACpB,IAAId,iBAAiB;QACnB,MAAMA;IACR;AACF", "ignoreList": [0]}