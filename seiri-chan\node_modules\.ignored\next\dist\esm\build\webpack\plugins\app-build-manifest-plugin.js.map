{"version": 3, "sources": ["../../../../src/build/webpack/plugins/app-build-manifest-plugin.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  APP_BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport { getEntrypointFiles } from './build-manifest-plugin'\nimport getAppRouteFromEntrypoint from '../../../server/get-app-route-from-entrypoint'\n\ntype Options = {\n  dev: boolean\n}\n\nexport type AppBuildManifest = {\n  pages: Record<string, string[]>\n}\n\nconst PLUGIN_NAME = 'AppBuildManifestPlugin'\n\nexport class AppBuildManifestPlugin {\n  private readonly dev: boolean\n\n  constructor(options: Options) {\n    this.dev = options.dev\n  }\n\n  public apply(compiler: any) {\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation: any) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => this.createAsset(compilation)\n      )\n    })\n  }\n\n  private createAsset(compilation: webpack.Compilation) {\n    const manifest: AppBuildManifest = {\n      pages: {},\n    }\n\n    const mainFiles = new Set(\n      getEntrypointFiles(\n        compilation.entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n      )\n    )\n\n    for (const entrypoint of compilation.entrypoints.values()) {\n      if (!entrypoint.name) {\n        continue\n      }\n\n      if (SYSTEM_ENTRYPOINTS.has(entrypoint.name)) {\n        continue\n      }\n\n      const pagePath = getAppRouteFromEntrypoint(entrypoint.name)\n      if (!pagePath) {\n        continue\n      }\n\n      const filesForPage = getEntrypointFiles(entrypoint)\n      manifest.pages[pagePath] = [...new Set([...mainFiles, ...filesForPage])]\n    }\n\n    const json = JSON.stringify(manifest, null, 2)\n\n    compilation.emitAsset(\n      APP_BUILD_MANIFEST,\n      new sources.RawSource(json) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n"], "names": ["webpack", "sources", "APP_BUILD_MANIFEST", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "SYSTEM_ENTRYPOINTS", "getEntrypointFiles", "getAppRouteFromEntrypoint", "PLUGIN_NAME", "AppBuildManifestPlugin", "constructor", "options", "dev", "apply", "compiler", "hooks", "make", "tap", "compilation", "processAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "createAsset", "manifest", "pages", "mainFiles", "Set", "entrypoints", "get", "entrypoint", "values", "has", "pagePath", "filesForPage", "json", "JSON", "stringify", "emitAsset", "RawSource"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,kBAAkB,EAClBC,oCAAoC,EACpCC,kBAAkB,QACb,gCAA+B;AACtC,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,OAAOC,+BAA+B,gDAA+C;AAUrF,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAGXC,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;IACxB;IAEOC,MAAMC,QAAa,EAAE;QAC1BA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACT,aAAa,CAACU;YACpCA,YAAYH,KAAK,CAACI,aAAa,CAACF,GAAG,CACjC;gBACEG,MAAMZ;gBACNa,OAAOpB,QAAQqB,WAAW,CAACC,8BAA8B;YAC3D,GACA,IAAM,IAAI,CAACC,WAAW,CAACN;QAE3B;IACF;IAEQM,YAAYN,WAAgC,EAAE;QACpD,MAAMO,WAA6B;YACjCC,OAAO,CAAC;QACV;QAEA,MAAMC,YAAY,IAAIC,IACpBtB,mBACEY,YAAYW,WAAW,CAACC,GAAG,CAAC1B;QAIhC,KAAK,MAAM2B,cAAcb,YAAYW,WAAW,CAACG,MAAM,GAAI;YACzD,IAAI,CAACD,WAAWX,IAAI,EAAE;gBACpB;YACF;YAEA,IAAIf,mBAAmB4B,GAAG,CAACF,WAAWX,IAAI,GAAG;gBAC3C;YACF;YAEA,MAAMc,WAAW3B,0BAA0BwB,WAAWX,IAAI;YAC1D,IAAI,CAACc,UAAU;gBACb;YACF;YAEA,MAAMC,eAAe7B,mBAAmByB;YACxCN,SAASC,KAAK,CAACQ,SAAS,GAAG;mBAAI,IAAIN,IAAI;uBAAID;uBAAcQ;iBAAa;aAAE;QAC1E;QAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACb,UAAU,MAAM;QAE5CP,YAAYqB,SAAS,CACnBpC,oBACA,IAAID,QAAQsC,SAAS,CAACJ;IAE1B;AACF", "ignoreList": [0]}