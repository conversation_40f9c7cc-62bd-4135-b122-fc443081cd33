(function(){"use strict";var e={};!function(){e.d=function(c,h){for(var C in h){if(e.o(h,C)&&!e.o(c,C)){Object.defineProperty(c,C,{enumerable:true,get:h[C]})}}}}();!function(){e.o=function(e,c){return Object.prototype.hasOwnProperty.call(e,c)}}();!function(){e.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})}}();if(typeof e!=="undefined")e.ab=__dirname+"/";var c={};e.r(c);e.d(c,{CLSThresholds:function(){return O},FCPThresholds:function(){return A},FIDThresholds:function(){return bt},INPThresholds:function(){return G},LCPThresholds:function(){return ft},TTFBThresholds:function(){return gt},onCLS:function(){return w},onFCP:function(){return x},onFID:function(){return Tt},onINP:function(){return nt},onLCP:function(){return at},onTTFB:function(){return st}});var h,C,D,r=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},i=function(e){if("loading"===document.readyState)return"loading";var c=r();if(c){if(e<c.domInteractive)return"loading";if(0===c.domContentLoadedEventStart||e<c.domContentLoadedEventStart)return"dom-interactive";if(0===c.domComplete||e<c.domComplete)return"dom-content-loaded"}return"complete"},a=function(e){var c=e.nodeName;return 1===e.nodeType?c.toLowerCase():c.toUpperCase().replace(/^#/,"")},o=function(e,c){var h="";try{for(;e&&9!==e.nodeType;){var C=e,D=C.id?"#"+C.id:a(C)+(C.classList&&C.classList.value&&C.classList.value.trim()&&C.classList.value.trim().length?"."+C.classList.value.trim().replace(/\s+/g,"."):"");if(h.length+D.length>(c||100)-1)return h||D;if(h=h?D+">"+h:D,C.id)break;e=C.parentNode}}catch(e){}return h},I=-1,u=function(){return I},s=function(e){addEventListener("pageshow",(function(c){c.persisted&&(I=c.timeStamp,e(c))}),!0)},f=function(){var e=r();return e&&e.activationStart||0},d=function(e,c){var h=r(),C="navigate";u()>=0?C="back-forward-cache":h&&(document.prerendering||f()>0?C="prerender":document.wasDiscarded?C="restore":h.type&&(C=h.type.replace(/_/g,"-")));return{name:e,value:void 0===c?-1:c,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:C}},l=function(e,c,h){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var C=new PerformanceObserver((function(e){Promise.resolve().then((function(){c(e.getEntries())}))}));return C.observe(Object.assign({type:e,buffered:!0},h||{})),C}}catch(e){}},m=function(e,c,h,C){var D,I;return function(k){c.value>=0&&(k||C)&&((I=c.value-(D||0))||void 0===D)&&(D=c.value,c.delta=I,c.rating=function(e,c){return e>c[1]?"poor":e>c[0]?"needs-improvement":"good"}(c.value,h),e(c))}},p=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},v=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},g=function(e){var c=!1;return function(){c||(e(),c=!0)}},k=-1,T=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},y=function(e){"hidden"===document.visibilityState&&k>-1&&(k="visibilitychange"===e.type?e.timeStamp:0,S())},E=function(){addEventListener("visibilitychange",y,!0),addEventListener("prerenderingchange",y,!0)},S=function(){removeEventListener("visibilitychange",y,!0),removeEventListener("prerenderingchange",y,!0)},b=function(){return k<0&&(k=T(),E(),s((function(){setTimeout((function(){k=T(),E()}),0)}))),{get firstHiddenTime(){return k}}},L=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},A=[1800,3e3],M=function(e,c){c=c||{},L((function(){var h,C=b(),D=d("FCP"),I=l("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(I.disconnect(),e.startTime<C.firstHiddenTime&&(D.value=Math.max(e.startTime-f(),0),D.entries.push(e),h(!0)))}))}));I&&(h=m(e,D,A,c.reportAllChanges),s((function(C){D=d("FCP"),h=m(e,D,A,c.reportAllChanges),p((function(){D.value=performance.now()-C.timeStamp,h(!0)}))})))}))},O=[.1,.25],w=function(e,c){!function(e,c){c=c||{},M(g((function(){var h,C=d("CLS",0),D=0,I=[],o=function(e){e.forEach((function(e){if(!e.hadRecentInput){var c=I[0],h=I[I.length-1];D&&e.startTime-h.startTime<1e3&&e.startTime-c.startTime<5e3?(D+=e.value,I.push(e)):(D=e.value,I=[e])}})),D>C.value&&(C.value=D,C.entries=I,h())},k=l("layout-shift",o);k&&(h=m(e,C,O,c.reportAllChanges),v((function(){o(k.takeRecords()),h(!0)})),s((function(){D=0,C=d("CLS",0),h=m(e,C,O,c.reportAllChanges),p((function(){return h()}))})),setTimeout(h,0))})))}((function(c){var h=function(e){var c,h={};if(e.entries.length){var C=e.entries.reduce((function(e,c){return e&&e.value>c.value?e:c}));if(C&&C.sources&&C.sources.length){var D=(c=C.sources).find((function(e){return e.node&&1===e.node.nodeType}))||c[0];D&&(h={largestShiftTarget:o(D.node),largestShiftTime:C.startTime,largestShiftValue:C.value,largestShiftSource:D,largestShiftEntry:C,loadState:i(C.startTime)})}}return Object.assign(e,{attribution:h})}(c);e(h)}),c)},x=function(e,c){M((function(c){var h=function(e){var c={timeToFirstByte:0,firstByteToFCP:e.value,loadState:i(u())};if(e.entries.length){var h=r(),C=e.entries[e.entries.length-1];if(h){var D=h.activationStart||0,I=Math.max(0,h.responseStart-D);c={timeToFirstByte:I,firstByteToFCP:e.value-I,loadState:i(e.entries[0].startTime),navigationEntry:h,fcpEntry:C}}}return Object.assign(e,{attribution:c})}(c);e(h)}),c)},B=0,R=1/0,q=0,F=function(e){e.forEach((function(e){e.interactionId&&(R=Math.min(R,e.interactionId),q=Math.max(q,e.interactionId),B=q?(q-R)/7+1:0)}))},P=function(){"interactionCount"in performance||h||(h=l("event",F,{type:"event",buffered:!0,durationThreshold:0}))},_=[],W=new Map,U=0,j=function(){return(h?B:performance.interactionCount||0)-U},V=[],H=function(e){if(V.forEach((function(c){return c(e)})),e.interactionId||"first-input"===e.entryType){var c=_[_.length-1],h=W.get(e.interactionId);if(h||_.length<10||e.duration>c.latency){if(h)e.duration>h.latency?(h.entries=[e],h.latency=e.duration):e.duration===h.latency&&e.startTime===h.entries[0].startTime&&h.entries.push(e);else{var C={id:e.interactionId,latency:e.duration,entries:[e]};W.set(C.id,C),_.push(C)}_.sort((function(e,c){return c.latency-e.latency})),_.length>10&&_.splice(10).forEach((function(e){return W.delete(e.id)}))}}},N=function(e){var c=self.requestIdleCallback||self.setTimeout,h=-1;return e=g(e),"hidden"===document.visibilityState?e():(h=c(e),v(e)),h},G=[200,500],z=function(e,c){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(c=c||{},L((function(){var h;P();var C,D=d("INP"),a=function(e){N((function(){e.forEach(H);var c,h=(c=Math.min(_.length-1,Math.floor(j()/50)),_[c]);h&&h.latency!==D.value&&(D.value=h.latency,D.entries=h.entries,C())}))},I=l("event",a,{durationThreshold:null!==(h=c.durationThreshold)&&void 0!==h?h:40});C=m(e,D,G,c.reportAllChanges),I&&(I.observe({type:"first-input",buffered:!0}),v((function(){a(I.takeRecords()),C(!0)})),s((function(){U=0,_.length=0,W.clear(),D=d("INP"),C=m(e,D,G,c.reportAllChanges)})))})))},J=[],Y=[],Z=new WeakMap,$=new Map,tt=-1,K=function(e){J=J.concat(e),Q()},Q=function(){tt<0&&(tt=N(X))},X=function(){$.size>10&&$.forEach((function(e,c){W.has(c)||$.delete(c)}));var e=_.map((function(e){return Z.get(e.entries[0])})),c=Y.length-50;Y=Y.filter((function(h,C){return C>=c||e.includes(h)}));for(var h=new Set,C=0;C<Y.length;C++){var I=Y[C];et(I.startTime,I.processingEnd).forEach((function(e){h.add(e)}))}for(var k=0;k<50;k++){var A=J[J.length-1-k];if(!A||A.startTime<D)break;h.add(A)}J=Array.from(h),tt=-1};V.push((function(e){e.interactionId&&e.target&&!$.has(e.interactionId)&&$.set(e.interactionId,e.target)}),(function(e){var c,h=e.startTime+e.duration;D=Math.max(D,e.processingEnd);for(var C=Y.length-1;C>=0;C--){var I=Y[C];if(Math.abs(h-I.renderTime)<=8){(c=I).startTime=Math.min(e.startTime,c.startTime),c.processingStart=Math.min(e.processingStart,c.processingStart),c.processingEnd=Math.max(e.processingEnd,c.processingEnd),c.entries.push(e);break}}c||(c={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:h,entries:[e]},Y.push(c)),(e.interactionId||"first-input"===e.entryType)&&Z.set(e,c),Q()}));var rt,it,ot,ct,et=function(e,c){for(var h,C=[],D=0;h=J[D];D++)if(!(h.startTime+h.duration<e)){if(h.startTime>c)break;C.push(h)}return C},nt=function(e,c){C||(C=l("long-animation-frame",K)),z((function(c){var h=function(e){var c=e.entries[0],h=Z.get(c),C=c.processingStart,D=h.processingEnd,I=h.entries.sort((function(e,c){return e.processingStart-c.processingStart})),k=et(c.startTime,D),A=e.entries.find((function(e){return e.target})),O=A&&A.target||$.get(c.interactionId),B=[c.startTime+c.duration,D].concat(k.map((function(e){return e.startTime+e.duration}))),R=Math.max.apply(Math,B),q={interactionTarget:o(O),interactionTargetElement:O,interactionType:c.name.startsWith("key")?"keyboard":"pointer",interactionTime:c.startTime,nextPaintTime:R,processedEventEntries:I,longAnimationFrameEntries:k,inputDelay:C-c.startTime,processingDuration:D-C,presentationDelay:Math.max(R-D,0),loadState:i(c.startTime)};return Object.assign(e,{attribution:q})}(c);e(h)}),c)},ft=[2500,4e3],dt={},at=function(e,c){!function(e,c){c=c||{},L((function(){var h,C=b(),D=d("LCP"),a=function(e){c.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<C.firstHiddenTime&&(D.value=Math.max(e.startTime-f(),0),D.entries=[e],h())}))},I=l("largest-contentful-paint",a);if(I){h=m(e,D,ft,c.reportAllChanges);var k=g((function(){dt[D.id]||(a(I.takeRecords()),I.disconnect(),dt[D.id]=!0,h(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return N(k)}),!0)})),v(k),s((function(C){D=d("LCP"),h=m(e,D,ft,c.reportAllChanges),p((function(){D.value=performance.now()-C.timeStamp,dt[D.id]=!0,h(!0)}))}))}}))}((function(c){var h=function(e){var c={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var h=r();if(h){var C=h.activationStart||0,D=e.entries[e.entries.length-1],I=D.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===D.url}))[0],k=Math.max(0,h.responseStart-C),A=Math.max(k,I?(I.requestStart||I.startTime)-C:0),O=Math.max(A,I?I.responseEnd-C:0),B=Math.max(O,D.startTime-C);c={element:o(D.element),timeToFirstByte:k,resourceLoadDelay:A-k,resourceLoadDuration:O-A,elementRenderDelay:B-O,navigationEntry:h,lcpEntry:D},D.url&&(c.url=D.url),I&&(c.lcpResourceEntry=I)}}return Object.assign(e,{attribution:c})}(c);e(h)}),c)},gt=[800,1800],yt=function t(e){document.prerendering?L((function(){return t(e)})):"complete"!==document.readyState?addEventListener("load",(function(){return t(e)}),!0):setTimeout(e,0)},ut=function(e,c){c=c||{};var h=d("TTFB"),C=m(e,h,gt,c.reportAllChanges);yt((function(){var D=r();D&&(h.value=Math.max(D.responseStart-f(),0),h.entries=[D],C(!0),s((function(){h=d("TTFB",0),(C=m(e,h,gt,c.reportAllChanges))(!0)})))}))},st=function(e,c){ut((function(c){var h=function(e){var c={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){var h=e.entries[0],C=h.activationStart||0,D=Math.max((h.workerStart||h.fetchStart)-C,0),I=Math.max(h.domainLookupStart-C,0),k=Math.max(h.connectStart-C,0),A=Math.max(h.connectEnd-C,0);c={waitingDuration:D,cacheDuration:I-D,dnsDuration:k-I,connectionDuration:A-k,requestDuration:e.value-A,navigationEntry:h}}return Object.assign(e,{attribution:c})}(c);e(h)}),c)},St={passive:!0,capture:!0},Et=new Date,lt=function(e,c){rt||(rt=c,it=e,ot=new Date,vt(removeEventListener),mt())},mt=function(){if(it>=0&&it<ot-Et){var e={entryType:"first-input",name:rt.type,target:rt.target,cancelable:rt.cancelable,startTime:rt.timeStamp,processingStart:rt.timeStamp+it};ct.forEach((function(c){c(e)})),ct=[]}},pt=function(e){if(e.cancelable){var c=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,c){var n=function(){lt(e,c),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,St),removeEventListener("pointercancel",r,St)};addEventListener("pointerup",n,St),addEventListener("pointercancel",r,St)}(c,e):lt(c,e)}},vt=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(c){return e(c,pt,St)}))},bt=[100,300],ht=function(e,c){c=c||{},L((function(){var h,C=b(),D=d("FID"),a=function(e){e.startTime<C.firstHiddenTime&&(D.value=e.processingStart-e.startTime,D.entries.push(e),h(!0))},o=function(e){e.forEach(a)},I=l("first-input",o);h=m(e,D,bt,c.reportAllChanges),I&&(v(g((function(){o(I.takeRecords()),I.disconnect()}))),s((function(){var C;D=d("FID"),h=m(e,D,bt,c.reportAllChanges),ct=[],it=-1,rt=null,vt(addEventListener),C=a,ct.push(C),mt()})))}))},Tt=function(e,c){ht((function(c){var h=function(e){var c=e.entries[0],h={eventTarget:o(c.target),eventType:c.name,eventTime:c.startTime,eventEntry:c,loadState:i(c.startTime)};return Object.assign(e,{attribution:h})}(c);e(h)}),c)};module.exports=c})();