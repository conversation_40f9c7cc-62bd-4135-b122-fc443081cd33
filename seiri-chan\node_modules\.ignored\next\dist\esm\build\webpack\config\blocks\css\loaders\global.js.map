{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/global.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\n\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\n\nexport function getGlobalCssLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  preProcessors: readonly webpack.RuleSetUseItem[] = []\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development more or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isAppDir: ctx.isAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  if (ctx.experimental.useLightningcss) {\n    loaders.push({\n      loader: require.resolve('../../../../loaders/lightningcss-loader/src'),\n      options: {\n        importLoaders: 1 + preProcessors.length,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: false,\n        targets: ctx.supportedBrowsers,\n        postcss,\n      },\n    })\n  } else {\n    // Resolve CSS `@import`s and `url()`s\n    loaders.push({\n      loader: require.resolve('../../../../loaders/css-loader/src'),\n      options: {\n        postcss,\n        importLoaders: 1 + preProcessors.length,\n        // Next.js controls CSS Modules eligibility:\n        modules: false,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n      },\n    })\n\n    // Compile CSS\n    loaders.push({\n      loader: require.resolve('../../../../loaders/postcss-loader/src'),\n      options: {\n        postcss,\n      },\n    })\n  }\n\n  loaders.push(\n    // Webpack loaders run like a stack, so we need to reverse the natural\n    // order of preprocessors.\n    ...preProcessors.slice().reverse()\n  )\n\n  return loaders\n}\n"], "names": ["getClientStyleLoader", "cssFileResolve", "getGlobalCssLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "experimental", "useLightningcss", "loader", "require", "resolve", "options", "importLoaders", "length", "url", "resourcePath", "urlImports", "import", "_", "modules", "targets", "supportedBrowsers", "slice", "reverse"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAE/C,OAAO,SAASC,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVR,qBAAqB;YACnBS,WAAWN,IAAIM,SAAS;YACxBC,UAAUP,IAAIO,QAAQ;YACtBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEA,IAAIT,IAAIU,YAAY,CAACC,eAAe,EAAE;QACpCR,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPC,eAAe,IAAId,cAAce,MAAM;gBACvCC,KAAK,CAACA,KAAaC,eACjBrB,eAAeoB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DC,QAAQ,CAACH,KAAaI,GAAQH,eAC5BrB,eAAeoB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DG,SAAS;gBACTC,SAASxB,IAAIyB,iBAAiB;gBAC9BxB;YACF;QACF;IACF,OAAO;QACL,sCAAsC;QACtCE,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPd;gBACAe,eAAe,IAAId,cAAce,MAAM;gBACvC,4CAA4C;gBAC5CM,SAAS;gBACTL,KAAK,CAACA,KAAaC,eACjBrB,eAAeoB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DC,QAAQ,CAACH,KAAaI,GAAQH,eAC5BrB,eAAeoB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;YACjE;QACF;QAEA,cAAc;QACdjB,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPd;YACF;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAcwB,KAAK,GAAGC,OAAO;IAGlC,OAAOxB;AACT", "ignoreList": [0]}