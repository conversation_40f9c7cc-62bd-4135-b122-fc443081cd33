import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始数据库种子数据初始化...');

  // 清理现有数据
  await prisma.taskLog.deleteMany();
  await prisma.task.deleteMany();
  await prisma.taskBatch.deleteMany();
  await prisma.tMDBCache.deleteMany();
  await prisma.config.deleteMany();

  // 创建默认配置
  const defaultConfigs = [
    {
      key: 'tmdbApiKey',
      value: process.env.TMDB_API_KEY || '',
    },
    {
      key: 'tmdbLanguage',
      value: 'zh-CN',
    },
    {
      key: 'aiEnabled',
      value: false,
    },
    {
      key: 'aiProvider',
      value: 'openai',
    },
    {
      key: 'aiConfidenceThreshold',
      value: 'medium',
    },
    {
      key: 'openaiApiKey',
      value: process.env.OPENAI_API_KEY || '',
    },
    {
      key: 'openaiBaseUrl',
      value: 'https://api.openai.com/v1',
    },
    {
      key: 'openaiModel',
      value: 'gpt-4o-mini',
    },
    {
      key: 'openaiOutputFormat',
      value: 'function_calling',
    },
    {
      key: 'geminiApiKey',
      value: process.env.GEMINI_API_KEY || '',
    },
    {
      key: 'geminiBaseUrl',
      value: 'https://generativelanguage.googleapis.com',
    },
    {
      key: 'geminiModel',
      value: 'gemini-2.5-flash',
    },
    {
      key: 'bangumiPath',
      value: process.env.BANGUMI_PATH || '/media/bangumi',
    },
    {
      key: 'moviePath',
      value: process.env.MOVIE_PATH || '/media/movies',
    },
    {
      key: 'animePath',
      value: process.env.ANIME_PATH || '/media/anime',
    },
    {
      key: 'animeMoviePath',
      value: process.env.ANIME_MOVIE_PATH || '/media/anime-movies',
    },
    {
      key: 'defaultSourcePath',
      value: process.env.DEFAULT_SOURCE_PATH || '/downloads',
    },
    {
      key: 'fileOperationMode',
      value: 'hardlink',
    },
    {
      key: 'defaultLocale',
      value: 'zh-CN',
    },
    {
      key: 'maxConcurrentTasks',
      value: 3,
    },
    {
      key: 'logLevel',
      value: 'info',
    },
    {
      key: 'cacheExpiration',
      value: 24,
    },
  ];

  for (const config of defaultConfigs) {
    await prisma.config.create({
      data: config,
    });
  }

  console.log('✅ 默认配置创建完成');

  // 创建示例任务批次
  const batch = await prisma.taskBatch.create({
    data: {
      name: '示例批量任务',
      status: 'completed',
      totalTasks: 3,
      completedTasks: 2,
      failedTasks: 1,
    },
  });

  console.log('✅ 示例批次创建完成');

  // 创建示例任务
  const tasks = [
    {
      name: '进击的巨人 最终季',
      type: 'anime',
      status: 'completed',
      sourcePath: '/downloads/Attack on Titan Final Season',
      progress: 100,
      batchId: batch.id,
      sourceFiles: JSON.stringify([
        {
          path: '/downloads/Attack on Titan Final Season/S04E01.mkv',
          name: 'S04E01.mkv',
          size: 1024 * 1024 * 500, // 500MB
          duration: 1440, // 24分钟
          resolution: '1920x1080',
        },
        {
          path: '/downloads/Attack on Titan Final Season/S04E02.mkv',
          name: 'S04E02.mkv',
          size: 1024 * 1024 * 520,
          duration: 1440,
          resolution: '1920x1080',
        },
      ]),
      metadata: JSON.stringify({
        id: 110277,
        type: 'tv',
        title: '进击的巨人 最终季',
        originalTitle: 'Shingeki no Kyojin: The Final Season',
        year: 2020,
        language: 'zh-CN',
        genres: ['动画', '动作', '剧情'],
        overview: '人类与巨人的最终决战即将开始...',
      }),
      result: JSON.stringify({
        status: 'completed',
        processedFiles: 2,
        successCount: 2,
        failureCount: 0,
        skippedCount: 0,
        operationType: 'hardlink',
        duration: 5000,
      }),
    },
    {
      name: '鬼灭之刃 无限列车篇',
      type: 'anime_movie',
      status: 'completed',
      sourcePath: '/downloads/Demon Slayer Movie',
      progress: 100,
      batchId: batch.id,
      sourceFiles: JSON.stringify([
        {
          path: '/downloads/Demon Slayer Movie/Demon.Slayer.Movie.2020.mkv',
          name: 'Demon.Slayer.Movie.2020.mkv',
          size: 1024 * 1024 * 1024 * 2, // 2GB
          duration: 7200, // 2小时
          resolution: '1920x1080',
        },
      ]),
      metadata: JSON.stringify({
        id: 635302,
        type: 'movie',
        title: '鬼灭之刃 无限列车篇',
        originalTitle: 'Kimetsu no Yaiba: Mugen Ressha-hen',
        year: 2020,
        language: 'zh-CN',
        genres: ['动画', '动作', '冒险'],
        overview: '炭治郎等人与炎柱煉獄杏寿郎一同前往无限列车...',
        runtime: 117,
      }),
      result: JSON.stringify({
        status: 'completed',
        processedFiles: 1,
        successCount: 1,
        failureCount: 0,
        skippedCount: 0,
        operationType: 'hardlink',
        duration: 3000,
      }),
    },
    {
      name: '咒术回战',
      type: 'anime',
      status: 'failed',
      sourcePath: '/downloads/Jujutsu Kaisen',
      progress: 0,
      errorMessage: 'TMDB API 查询失败',
      batchId: batch.id,
      sourceFiles: JSON.stringify([
        {
          path: '/downloads/Jujutsu Kaisen/JJK_01.mkv',
          name: 'JJK_01.mkv',
          size: 1024 * 1024 * 480,
          duration: 1440,
          resolution: '1920x1080',
        },
      ]),
    },
  ];

  for (const taskData of tasks) {
    const task = await prisma.task.create({
      data: {
        ...taskData,
        completedAt: taskData.status === 'completed' ? new Date() : null,
      },
    });

    // 为每个任务创建一些日志
    const logs = [
      {
        taskId: task.id,
        level: 'info',
        message: '任务开始处理',
        metadata: JSON.stringify({ step: 'start' }),
      },
      {
        taskId: task.id,
        level: 'info',
        message: '正在扫描源文件',
        metadata: JSON.stringify({ step: 'scan' }),
      },
    ];

    if (taskData.status === 'completed') {
      logs.push(
        {
          taskId: task.id,
          level: 'info',
          message: '正在查询TMDB元数据',
          metadata: JSON.stringify({ step: 'tmdb' }),
        },
        {
          taskId: task.id,
          level: 'info',
          message: '文件映射生成完成',
          metadata: JSON.stringify({ step: 'mapping' }),
        },
        {
          taskId: task.id,
          level: 'info',
          message: '文件处理完成',
          metadata: JSON.stringify({ step: 'complete' }),
        }
      );
    } else if (taskData.status === 'failed') {
      logs.push({
        taskId: task.id,
        level: 'error',
        message: taskData.errorMessage || '处理失败',
        metadata: JSON.stringify({ step: 'error' }),
      });
    }

    for (const log of logs) {
      await prisma.taskLog.create({
        data: log,
      });
    }
  }

  console.log('✅ 示例任务创建完成');

  // 创建一些TMDB缓存示例
  const tmdbCaches = [
    {
      type: 'tv',
      tmdbId: 110277,
      language: 'zh-CN',
      data: JSON.stringify({
        id: 110277,
        name: '进击的巨人 最终季',
        original_name: 'Shingeki no Kyojin: The Final Season',
        first_air_date: '2020-12-07',
        genres: [{ id: 16, name: '动画' }, { id: 28, name: '动作' }],
        overview: '人类与巨人的最终决战即将开始...',
        seasons: [
          {
            id: 142234,
            season_number: 4,
            name: '最终季',
            episode_count: 16,
            air_date: '2020-12-07',
          },
        ],
      }),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后过期
    },
    {
      type: 'movie',
      tmdbId: 635302,
      language: 'zh-CN',
      data: JSON.stringify({
        id: 635302,
        title: '鬼灭之刃 无限列车篇',
        original_title: 'Kimetsu no Yaiba: Mugen Ressha-hen',
        release_date: '2020-10-16',
        runtime: 117,
        genres: [{ id: 16, name: '动画' }, { id: 28, name: '动作' }],
        overview: '炭治郎等人与炎柱煉獄杏寿郎一同前往无限列车...',
      }),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
    },
  ];

  for (const cache of tmdbCaches) {
    await prisma.tMDBCache.create({
      data: cache,
    });
  }

  console.log('✅ TMDB缓存示例创建完成');

  console.log('🎉 数据库种子数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
