"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RcMap = exports.RateLimiter = exports.Random = exports.Queue = exports.PubSub = exports.PrimaryKey = exports.Pretty = exports.Predicate = exports.Pool = exports.Pipeable = exports.ParseResult = exports.Ordering = exports.Order = exports.Option = exports.Number = exports.NonEmptyIterable = exports.MutableRef = exports.MutableQueue = exports.MutableList = exports.MutableHashSet = exports.MutableHashMap = exports.ModuleVersion = exports.Micro = exports.MetricState = exports.MetricRegistry = exports.MetricPolling = exports.MetricPair = exports.MetricLabel = exports.MetricKeyType = exports.MetricKey = exports.MetricHook = exports.MetricBoundaries = exports.Metric = exports.MergeStrategy = exports.MergeState = exports.MergeDecision = exports.Match = exports.ManagedRuntime = exports.Mailbox = exports.Logger = exports.LogSpan = exports.LogLevel = exports.List = exports.LayerMap = exports.Layer = exports.KeyedPool = exports.JSONSchema = exports.Iterable = exports.Inspectable = exports.HashSet = exports.HashMap = exports.Hash = exports.HKT = exports.GroupBy = exports.GlobalValue = exports.Function = exports.FiberStatus = exports.FiberSet = exports.FiberRefsPatch = exports.FiberRefs = exports.FiberRef = exports.FiberMap = exports.FiberId = exports.FiberHandle = exports.Fiber = exports.FastCheck = exports.Exit = exports.ExecutionStrategy = exports.ExecutionPlan = exports.Equivalence = exports.Equal = exports.Encoding = exports.Either = exports.Effectable = exports.Effect = exports.Duration = exports.Differ = exports.Deferred = exports.DefaultServices = exports.DateTime = exports.Data = exports.Cron = exports.Context = exports.Console = exports.ConfigProviderPathPatch = exports.ConfigProvider = exports.ConfigError = exports.Config = exports.Clock = exports.Chunk = exports.ChildExecutorDecision = exports.Channel = exports.Cause = exports.Cache = exports.Brand = exports.Boolean = exports.BigInt = exports.BigDecimal = exports.Array = exports.Arbitrary = void 0;
exports.Utils = exports.UpstreamPullStrategy = exports.UpstreamPullRequest = exports.Unify = exports.Types = exports.Tuple = exports.Trie = exports.Tracer = exports.TestSized = exports.TestServices = exports.TestLive = exports.TestContext = exports.TestConfig = exports.TestClock = exports.TestAnnotations = exports.TestAnnotationMap = exports.TestAnnotation = exports.Take = exports.TSubscriptionRef = exports.TSet = exports.TSemaphore = exports.TRef = exports.TReentrantLock = exports.TRandom = exports.TQueue = exports.TPubSub = exports.TPriorityQueue = exports.TMap = exports.TDeferred = exports.TArray = exports.SynchronizedRef = exports.Symbol = exports.Supervisor = exports.SubscriptionRef = exports.Subscribable = exports.Struct = exports.String = exports.Streamable = exports.StreamHaltStrategy = exports.StreamEmit = exports.Stream = exports.SortedSet = exports.SortedMap = exports.Sink = exports.SingleProducerAsyncInput = exports.Secret = exports.ScopedRef = exports.ScopedCache = exports.Scope = exports.SchemaAST = exports.Schema = exports.Scheduler = exports.ScheduleIntervals = exports.ScheduleInterval = exports.ScheduleDecision = exports.Schedule = exports.STM = exports.RuntimeFlagsPatch = exports.RuntimeFlags = exports.Runtime = exports.Resource = exports.RequestResolver = exports.RequestBlock = exports.Request = exports.Reloadable = exports.RegExp = exports.Ref = exports.Redacted = exports.RedBlackTree = exports.Record = exports.Readable = exports.RcRef = void 0;
Object.defineProperty(exports, "absurd", {
  enumerable: true,
  get: function () {
    return _Function.absurd;
  }
});
Object.defineProperty(exports, "flow", {
  enumerable: true,
  get: function () {
    return _Function.flow;
  }
});
Object.defineProperty(exports, "hole", {
  enumerable: true,
  get: function () {
    return _Function.hole;
  }
});
Object.defineProperty(exports, "identity", {
  enumerable: true,
  get: function () {
    return _Function.identity;
  }
});
Object.defineProperty(exports, "pipe", {
  enumerable: true,
  get: function () {
    return _Function.pipe;
  }
});
Object.defineProperty(exports, "unsafeCoerce", {
  enumerable: true,
  get: function () {
    return _Function.unsafeCoerce;
  }
});
var _Function = _interopRequireWildcard(require("./Function.js"));
exports.Function = _Function;
var _Arbitrary = _interopRequireWildcard(require("./Arbitrary.js"));
exports.Arbitrary = _Arbitrary;
var _Array = _interopRequireWildcard(require("./Array.js"));
exports.Array = _Array;
var _BigDecimal = _interopRequireWildcard(require("./BigDecimal.js"));
exports.BigDecimal = _BigDecimal;
var _BigInt = _interopRequireWildcard(require("./BigInt.js"));
exports.BigInt = _BigInt;
var _Boolean = _interopRequireWildcard(require("./Boolean.js"));
exports.Boolean = _Boolean;
var _Brand = _interopRequireWildcard(require("./Brand.js"));
exports.Brand = _Brand;
var _Cache = _interopRequireWildcard(require("./Cache.js"));
exports.Cache = _Cache;
var _Cause = _interopRequireWildcard(require("./Cause.js"));
exports.Cause = _Cause;
var _Channel = _interopRequireWildcard(require("./Channel.js"));
exports.Channel = _Channel;
var _ChildExecutorDecision = _interopRequireWildcard(require("./ChildExecutorDecision.js"));
exports.ChildExecutorDecision = _ChildExecutorDecision;
var _Chunk = _interopRequireWildcard(require("./Chunk.js"));
exports.Chunk = _Chunk;
var _Clock = _interopRequireWildcard(require("./Clock.js"));
exports.Clock = _Clock;
var _Config = _interopRequireWildcard(require("./Config.js"));
exports.Config = _Config;
var _ConfigError = _interopRequireWildcard(require("./ConfigError.js"));
exports.ConfigError = _ConfigError;
var _ConfigProvider = _interopRequireWildcard(require("./ConfigProvider.js"));
exports.ConfigProvider = _ConfigProvider;
var _ConfigProviderPathPatch = _interopRequireWildcard(require("./ConfigProviderPathPatch.js"));
exports.ConfigProviderPathPatch = _ConfigProviderPathPatch;
var _Console = _interopRequireWildcard(require("./Console.js"));
exports.Console = _Console;
var _Context = _interopRequireWildcard(require("./Context.js"));
exports.Context = _Context;
var _Cron = _interopRequireWildcard(require("./Cron.js"));
exports.Cron = _Cron;
var _Data = _interopRequireWildcard(require("./Data.js"));
exports.Data = _Data;
var _DateTime = _interopRequireWildcard(require("./DateTime.js"));
exports.DateTime = _DateTime;
var _DefaultServices = _interopRequireWildcard(require("./DefaultServices.js"));
exports.DefaultServices = _DefaultServices;
var _Deferred = _interopRequireWildcard(require("./Deferred.js"));
exports.Deferred = _Deferred;
var _Differ = _interopRequireWildcard(require("./Differ.js"));
exports.Differ = _Differ;
var _Duration = _interopRequireWildcard(require("./Duration.js"));
exports.Duration = _Duration;
var _Effect = _interopRequireWildcard(require("./Effect.js"));
exports.Effect = _Effect;
var _Effectable = _interopRequireWildcard(require("./Effectable.js"));
exports.Effectable = _Effectable;
var _Either = _interopRequireWildcard(require("./Either.js"));
exports.Either = _Either;
var _Encoding = _interopRequireWildcard(require("./Encoding.js"));
exports.Encoding = _Encoding;
var _Equal = _interopRequireWildcard(require("./Equal.js"));
exports.Equal = _Equal;
var _Equivalence = _interopRequireWildcard(require("./Equivalence.js"));
exports.Equivalence = _Equivalence;
var _ExecutionPlan = _interopRequireWildcard(require("./ExecutionPlan.js"));
exports.ExecutionPlan = _ExecutionPlan;
var _ExecutionStrategy = _interopRequireWildcard(require("./ExecutionStrategy.js"));
exports.ExecutionStrategy = _ExecutionStrategy;
var _Exit = _interopRequireWildcard(require("./Exit.js"));
exports.Exit = _Exit;
var _FastCheck = _interopRequireWildcard(require("./FastCheck.js"));
exports.FastCheck = _FastCheck;
var _Fiber = _interopRequireWildcard(require("./Fiber.js"));
exports.Fiber = _Fiber;
var _FiberHandle = _interopRequireWildcard(require("./FiberHandle.js"));
exports.FiberHandle = _FiberHandle;
var _FiberId = _interopRequireWildcard(require("./FiberId.js"));
exports.FiberId = _FiberId;
var _FiberMap = _interopRequireWildcard(require("./FiberMap.js"));
exports.FiberMap = _FiberMap;
var _FiberRef = _interopRequireWildcard(require("./FiberRef.js"));
exports.FiberRef = _FiberRef;
var _FiberRefs = _interopRequireWildcard(require("./FiberRefs.js"));
exports.FiberRefs = _FiberRefs;
var _FiberRefsPatch = _interopRequireWildcard(require("./FiberRefsPatch.js"));
exports.FiberRefsPatch = _FiberRefsPatch;
var _FiberSet = _interopRequireWildcard(require("./FiberSet.js"));
exports.FiberSet = _FiberSet;
var _FiberStatus = _interopRequireWildcard(require("./FiberStatus.js"));
exports.FiberStatus = _FiberStatus;
var _GlobalValue = _interopRequireWildcard(require("./GlobalValue.js"));
exports.GlobalValue = _GlobalValue;
var _GroupBy = _interopRequireWildcard(require("./GroupBy.js"));
exports.GroupBy = _GroupBy;
var _HKT = _interopRequireWildcard(require("./HKT.js"));
exports.HKT = _HKT;
var _Hash = _interopRequireWildcard(require("./Hash.js"));
exports.Hash = _Hash;
var _HashMap = _interopRequireWildcard(require("./HashMap.js"));
exports.HashMap = _HashMap;
var _HashSet = _interopRequireWildcard(require("./HashSet.js"));
exports.HashSet = _HashSet;
var _Inspectable = _interopRequireWildcard(require("./Inspectable.js"));
exports.Inspectable = _Inspectable;
var _Iterable = _interopRequireWildcard(require("./Iterable.js"));
exports.Iterable = _Iterable;
var _JSONSchema = _interopRequireWildcard(require("./JSONSchema.js"));
exports.JSONSchema = _JSONSchema;
var _KeyedPool = _interopRequireWildcard(require("./KeyedPool.js"));
exports.KeyedPool = _KeyedPool;
var _Layer = _interopRequireWildcard(require("./Layer.js"));
exports.Layer = _Layer;
var _LayerMap = _interopRequireWildcard(require("./LayerMap.js"));
exports.LayerMap = _LayerMap;
var _List = _interopRequireWildcard(require("./List.js"));
exports.List = _List;
var _LogLevel = _interopRequireWildcard(require("./LogLevel.js"));
exports.LogLevel = _LogLevel;
var _LogSpan = _interopRequireWildcard(require("./LogSpan.js"));
exports.LogSpan = _LogSpan;
var _Logger = _interopRequireWildcard(require("./Logger.js"));
exports.Logger = _Logger;
var _Mailbox = _interopRequireWildcard(require("./Mailbox.js"));
exports.Mailbox = _Mailbox;
var _ManagedRuntime = _interopRequireWildcard(require("./ManagedRuntime.js"));
exports.ManagedRuntime = _ManagedRuntime;
var _Match = _interopRequireWildcard(require("./Match.js"));
exports.Match = _Match;
var _MergeDecision = _interopRequireWildcard(require("./MergeDecision.js"));
exports.MergeDecision = _MergeDecision;
var _MergeState = _interopRequireWildcard(require("./MergeState.js"));
exports.MergeState = _MergeState;
var _MergeStrategy = _interopRequireWildcard(require("./MergeStrategy.js"));
exports.MergeStrategy = _MergeStrategy;
var _Metric = _interopRequireWildcard(require("./Metric.js"));
exports.Metric = _Metric;
var _MetricBoundaries = _interopRequireWildcard(require("./MetricBoundaries.js"));
exports.MetricBoundaries = _MetricBoundaries;
var _MetricHook = _interopRequireWildcard(require("./MetricHook.js"));
exports.MetricHook = _MetricHook;
var _MetricKey = _interopRequireWildcard(require("./MetricKey.js"));
exports.MetricKey = _MetricKey;
var _MetricKeyType = _interopRequireWildcard(require("./MetricKeyType.js"));
exports.MetricKeyType = _MetricKeyType;
var _MetricLabel = _interopRequireWildcard(require("./MetricLabel.js"));
exports.MetricLabel = _MetricLabel;
var _MetricPair = _interopRequireWildcard(require("./MetricPair.js"));
exports.MetricPair = _MetricPair;
var _MetricPolling = _interopRequireWildcard(require("./MetricPolling.js"));
exports.MetricPolling = _MetricPolling;
var _MetricRegistry = _interopRequireWildcard(require("./MetricRegistry.js"));
exports.MetricRegistry = _MetricRegistry;
var _MetricState = _interopRequireWildcard(require("./MetricState.js"));
exports.MetricState = _MetricState;
var _Micro = _interopRequireWildcard(require("./Micro.js"));
exports.Micro = _Micro;
var _ModuleVersion = _interopRequireWildcard(require("./ModuleVersion.js"));
exports.ModuleVersion = _ModuleVersion;
var _MutableHashMap = _interopRequireWildcard(require("./MutableHashMap.js"));
exports.MutableHashMap = _MutableHashMap;
var _MutableHashSet = _interopRequireWildcard(require("./MutableHashSet.js"));
exports.MutableHashSet = _MutableHashSet;
var _MutableList = _interopRequireWildcard(require("./MutableList.js"));
exports.MutableList = _MutableList;
var _MutableQueue = _interopRequireWildcard(require("./MutableQueue.js"));
exports.MutableQueue = _MutableQueue;
var _MutableRef = _interopRequireWildcard(require("./MutableRef.js"));
exports.MutableRef = _MutableRef;
var _NonEmptyIterable = _interopRequireWildcard(require("./NonEmptyIterable.js"));
exports.NonEmptyIterable = _NonEmptyIterable;
var _Number = _interopRequireWildcard(require("./Number.js"));
exports.Number = _Number;
var _Option = _interopRequireWildcard(require("./Option.js"));
exports.Option = _Option;
var _Order = _interopRequireWildcard(require("./Order.js"));
exports.Order = _Order;
var _Ordering = _interopRequireWildcard(require("./Ordering.js"));
exports.Ordering = _Ordering;
var _ParseResult = _interopRequireWildcard(require("./ParseResult.js"));
exports.ParseResult = _ParseResult;
var _Pipeable = _interopRequireWildcard(require("./Pipeable.js"));
exports.Pipeable = _Pipeable;
var _Pool = _interopRequireWildcard(require("./Pool.js"));
exports.Pool = _Pool;
var _Predicate = _interopRequireWildcard(require("./Predicate.js"));
exports.Predicate = _Predicate;
var _Pretty = _interopRequireWildcard(require("./Pretty.js"));
exports.Pretty = _Pretty;
var _PrimaryKey = _interopRequireWildcard(require("./PrimaryKey.js"));
exports.PrimaryKey = _PrimaryKey;
var _PubSub = _interopRequireWildcard(require("./PubSub.js"));
exports.PubSub = _PubSub;
var _Queue = _interopRequireWildcard(require("./Queue.js"));
exports.Queue = _Queue;
var _Random = _interopRequireWildcard(require("./Random.js"));
exports.Random = _Random;
var _RateLimiter = _interopRequireWildcard(require("./RateLimiter.js"));
exports.RateLimiter = _RateLimiter;
var _RcMap = _interopRequireWildcard(require("./RcMap.js"));
exports.RcMap = _RcMap;
var _RcRef = _interopRequireWildcard(require("./RcRef.js"));
exports.RcRef = _RcRef;
var _Readable = _interopRequireWildcard(require("./Readable.js"));
exports.Readable = _Readable;
var _Record = _interopRequireWildcard(require("./Record.js"));
exports.Record = _Record;
var _RedBlackTree = _interopRequireWildcard(require("./RedBlackTree.js"));
exports.RedBlackTree = _RedBlackTree;
var _Redacted = _interopRequireWildcard(require("./Redacted.js"));
exports.Redacted = _Redacted;
var _Ref = _interopRequireWildcard(require("./Ref.js"));
exports.Ref = _Ref;
var _RegExp = _interopRequireWildcard(require("./RegExp.js"));
exports.RegExp = _RegExp;
var _Reloadable = _interopRequireWildcard(require("./Reloadable.js"));
exports.Reloadable = _Reloadable;
var _Request = _interopRequireWildcard(require("./Request.js"));
exports.Request = _Request;
var _RequestBlock = _interopRequireWildcard(require("./RequestBlock.js"));
exports.RequestBlock = _RequestBlock;
var _RequestResolver = _interopRequireWildcard(require("./RequestResolver.js"));
exports.RequestResolver = _RequestResolver;
var _Resource = _interopRequireWildcard(require("./Resource.js"));
exports.Resource = _Resource;
var _Runtime = _interopRequireWildcard(require("./Runtime.js"));
exports.Runtime = _Runtime;
var _RuntimeFlags = _interopRequireWildcard(require("./RuntimeFlags.js"));
exports.RuntimeFlags = _RuntimeFlags;
var _RuntimeFlagsPatch = _interopRequireWildcard(require("./RuntimeFlagsPatch.js"));
exports.RuntimeFlagsPatch = _RuntimeFlagsPatch;
var _STM = _interopRequireWildcard(require("./STM.js"));
exports.STM = _STM;
var _Schedule = _interopRequireWildcard(require("./Schedule.js"));
exports.Schedule = _Schedule;
var _ScheduleDecision = _interopRequireWildcard(require("./ScheduleDecision.js"));
exports.ScheduleDecision = _ScheduleDecision;
var _ScheduleInterval = _interopRequireWildcard(require("./ScheduleInterval.js"));
exports.ScheduleInterval = _ScheduleInterval;
var _ScheduleIntervals = _interopRequireWildcard(require("./ScheduleIntervals.js"));
exports.ScheduleIntervals = _ScheduleIntervals;
var _Scheduler = _interopRequireWildcard(require("./Scheduler.js"));
exports.Scheduler = _Scheduler;
var _Schema = _interopRequireWildcard(require("./Schema.js"));
exports.Schema = _Schema;
var _SchemaAST = _interopRequireWildcard(require("./SchemaAST.js"));
exports.SchemaAST = _SchemaAST;
var _Scope = _interopRequireWildcard(require("./Scope.js"));
exports.Scope = _Scope;
var _ScopedCache = _interopRequireWildcard(require("./ScopedCache.js"));
exports.ScopedCache = _ScopedCache;
var _ScopedRef = _interopRequireWildcard(require("./ScopedRef.js"));
exports.ScopedRef = _ScopedRef;
var _Secret = _interopRequireWildcard(require("./Secret.js"));
exports.Secret = _Secret;
var _SingleProducerAsyncInput = _interopRequireWildcard(require("./SingleProducerAsyncInput.js"));
exports.SingleProducerAsyncInput = _SingleProducerAsyncInput;
var _Sink = _interopRequireWildcard(require("./Sink.js"));
exports.Sink = _Sink;
var _SortedMap = _interopRequireWildcard(require("./SortedMap.js"));
exports.SortedMap = _SortedMap;
var _SortedSet = _interopRequireWildcard(require("./SortedSet.js"));
exports.SortedSet = _SortedSet;
var _Stream = _interopRequireWildcard(require("./Stream.js"));
exports.Stream = _Stream;
var _StreamEmit = _interopRequireWildcard(require("./StreamEmit.js"));
exports.StreamEmit = _StreamEmit;
var _StreamHaltStrategy = _interopRequireWildcard(require("./StreamHaltStrategy.js"));
exports.StreamHaltStrategy = _StreamHaltStrategy;
var _Streamable = _interopRequireWildcard(require("./Streamable.js"));
exports.Streamable = _Streamable;
var _String = _interopRequireWildcard(require("./String.js"));
exports.String = _String;
var _Struct = _interopRequireWildcard(require("./Struct.js"));
exports.Struct = _Struct;
var _Subscribable = _interopRequireWildcard(require("./Subscribable.js"));
exports.Subscribable = _Subscribable;
var _SubscriptionRef = _interopRequireWildcard(require("./SubscriptionRef.js"));
exports.SubscriptionRef = _SubscriptionRef;
var _Supervisor = _interopRequireWildcard(require("./Supervisor.js"));
exports.Supervisor = _Supervisor;
var _Symbol = _interopRequireWildcard(require("./Symbol.js"));
exports.Symbol = _Symbol;
var _SynchronizedRef = _interopRequireWildcard(require("./SynchronizedRef.js"));
exports.SynchronizedRef = _SynchronizedRef;
var _TArray = _interopRequireWildcard(require("./TArray.js"));
exports.TArray = _TArray;
var _TDeferred = _interopRequireWildcard(require("./TDeferred.js"));
exports.TDeferred = _TDeferred;
var _TMap = _interopRequireWildcard(require("./TMap.js"));
exports.TMap = _TMap;
var _TPriorityQueue = _interopRequireWildcard(require("./TPriorityQueue.js"));
exports.TPriorityQueue = _TPriorityQueue;
var _TPubSub = _interopRequireWildcard(require("./TPubSub.js"));
exports.TPubSub = _TPubSub;
var _TQueue = _interopRequireWildcard(require("./TQueue.js"));
exports.TQueue = _TQueue;
var _TRandom = _interopRequireWildcard(require("./TRandom.js"));
exports.TRandom = _TRandom;
var _TReentrantLock = _interopRequireWildcard(require("./TReentrantLock.js"));
exports.TReentrantLock = _TReentrantLock;
var _TRef = _interopRequireWildcard(require("./TRef.js"));
exports.TRef = _TRef;
var _TSemaphore = _interopRequireWildcard(require("./TSemaphore.js"));
exports.TSemaphore = _TSemaphore;
var _TSet = _interopRequireWildcard(require("./TSet.js"));
exports.TSet = _TSet;
var _TSubscriptionRef = _interopRequireWildcard(require("./TSubscriptionRef.js"));
exports.TSubscriptionRef = _TSubscriptionRef;
var _Take = _interopRequireWildcard(require("./Take.js"));
exports.Take = _Take;
var _TestAnnotation = _interopRequireWildcard(require("./TestAnnotation.js"));
exports.TestAnnotation = _TestAnnotation;
var _TestAnnotationMap = _interopRequireWildcard(require("./TestAnnotationMap.js"));
exports.TestAnnotationMap = _TestAnnotationMap;
var _TestAnnotations = _interopRequireWildcard(require("./TestAnnotations.js"));
exports.TestAnnotations = _TestAnnotations;
var _TestClock = _interopRequireWildcard(require("./TestClock.js"));
exports.TestClock = _TestClock;
var _TestConfig = _interopRequireWildcard(require("./TestConfig.js"));
exports.TestConfig = _TestConfig;
var _TestContext = _interopRequireWildcard(require("./TestContext.js"));
exports.TestContext = _TestContext;
var _TestLive = _interopRequireWildcard(require("./TestLive.js"));
exports.TestLive = _TestLive;
var _TestServices = _interopRequireWildcard(require("./TestServices.js"));
exports.TestServices = _TestServices;
var _TestSized = _interopRequireWildcard(require("./TestSized.js"));
exports.TestSized = _TestSized;
var _Tracer = _interopRequireWildcard(require("./Tracer.js"));
exports.Tracer = _Tracer;
var _Trie = _interopRequireWildcard(require("./Trie.js"));
exports.Trie = _Trie;
var _Tuple = _interopRequireWildcard(require("./Tuple.js"));
exports.Tuple = _Tuple;
var _Types = _interopRequireWildcard(require("./Types.js"));
exports.Types = _Types;
var _Unify = _interopRequireWildcard(require("./Unify.js"));
exports.Unify = _Unify;
var _UpstreamPullRequest = _interopRequireWildcard(require("./UpstreamPullRequest.js"));
exports.UpstreamPullRequest = _UpstreamPullRequest;
var _UpstreamPullStrategy = _interopRequireWildcard(require("./UpstreamPullStrategy.js"));
exports.UpstreamPullStrategy = _UpstreamPullStrategy;
var _Utils = _interopRequireWildcard(require("./Utils.js"));
exports.Utils = _Utils;
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
//# sourceMappingURL=index.js.map