{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_dbfa13df._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/node_modules__pnpm_03211199._.js", "server/edge/chunks/[root-of-the-server]__e444c5de._.js", "server/edge/chunks/edge-wrapper_c468392d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh-CN|en|ja))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(zh-CN|en|ja)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LXtihcTKvbojlDgPCpPLkUY6gslpelVISq1t+hN/KZ0=", "__NEXT_PREVIEW_MODE_ID": "df1de1726d2938f3ed6db2061ccf07c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4a99c4c907c5e4f8603eefb262d805288d6e0de5acc39090327adc7dddb33ada", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fb9ef46cb36332c55502ea8e0360f0dcdda020d0b6fcebff6b33ab260d55c57c"}}}, "sortedMiddleware": ["/"], "functions": {}}