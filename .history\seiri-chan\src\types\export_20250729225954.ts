// 任务导出相关类型定义

export interface TaskExportData {
  version: string;
  exportedAt: string;
  exportedBy?: string;
  tasks: ExportedTask[];
}

export interface ExportedTask {
  id: string;
  name: string;
  type: 'tv' | 'movie' | 'anime' | 'anime_movie';
  sourceFiles: ExportedFile[];
  tmdbMetadata?: TMDBMetadata;
  mapping?: FileMapping[];
  result?: ProcessingResult;
  recognitionMethod: 'traditional' | 'ai' | 'hybrid';
  aiAnalysis?: AIAnalysisInfo;
  createdAt: string;
  completedAt?: string;
}

export interface ExportedFile {
  relativePath: string; // 相对路径，不包含绝对路径信息
  fileName: string;
  size: number;
  duration?: number; // 视频时长（秒）
  resolution?: string; // 分辨率，如 "1920x1080"
  codec?: string; // 编码格式
  fileType: 'video' | 'subtitle' | 'font' | 'other';
}

export interface TMDBMetadata {
  id: number;
  type: 'tv' | 'movie';
  title: string;
  originalTitle?: string;
  year?: number;
  language: string;
  seasons?: SeasonData[];
  episodes?: EpisodeData[];
  genres?: string[];
  overview?: string;
}

export interface SeasonData {
  seasonNumber: number;
  name: string;
  episodeCount: number;
  airDate?: string;
  overview?: string;
}

export interface EpisodeData {
  seasonNumber: number;
  episodeNumber: number;
  name: string;
  airDate?: string;
  runtime?: number;
  overview?: string;
}

export interface FileMapping {
  sourceFile: string; // 相对路径
  targetPath: string; // 目标路径（相对于输出目录）
  season?: number;
  episode?: number;
  episodeType: 'regular' | 'special' | 'movie' | 'trailer' | 'other';
  confidence: 'high' | 'medium' | 'low';
}

export interface ProcessingResult {
  status: 'completed' | 'failed' | 'partial';
  processedFiles: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  operationType: 'hardlink' | 'symlink' | 'copy' | 'move';
  errors?: ProcessingError[];
  warnings?: string[];
  duration: number; // 处理耗时（毫秒）
}

export interface ProcessingError {
  file: string;
  error: string;
  code?: string;
}

export interface AIAnalysisInfo {
  provider: 'openai' | 'gemini';
  model: string;
  confidence: 'high' | 'medium' | 'low';
  reason: string;
  processingTime: number; // AI分析耗时（毫秒）
  tokensUsed?: number;
}

export interface ExportOptions {
  includeMetadata: boolean; // 是否包含TMDB元数据
  includeResults: boolean; // 是否包含处理结果
  includeAIAnalysis: boolean; // 是否包含AI分析信息
  anonymize: boolean; // 是否匿名化（移除可能的隐私信息）
  format: 'json' | 'yaml'; // 导出格式
  compression: boolean; // 是否压缩
}

export interface ImportOptions {
  testMode: boolean; // 测试模式，不实际执行文件操作
  overwriteExisting: boolean; // 是否覆盖已存在的任务
  validatePaths: boolean; // 是否验证文件路径存在性
}

export interface ExportRecord {
  id: string;
  name: string;
  taskIds: string[];
  exportData: TaskExportData;
  createdBy?: string;
  createdAt: string;
  downloadedAt?: string;
  downloadCount: number;
}

// 导出验证结果
export interface ExportValidationResult {
  isValid: boolean;
  version: string;
  taskCount: number;
  issues: ValidationIssue[];
  warnings: string[];
}

export interface ValidationIssue {
  type: 'error' | 'warning';
  message: string;
  taskId?: string;
  field?: string;
}

// 导入预览结果
export interface ImportPreview {
  taskCount: number;
  newTasks: number;
  existingTasks: number;
  conflicts: ImportConflict[];
  estimatedSize: number; // 预估文件大小（字节）
}

export interface ImportConflict {
  taskId: string;
  taskName: string;
  conflictType: 'duplicate_path' | 'duplicate_name' | 'invalid_path';
  description: string;
  resolution: 'skip' | 'overwrite' | 'rename';
}
