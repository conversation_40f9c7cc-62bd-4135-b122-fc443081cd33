{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeLocaleList.js"], "sourcesContent": ["/**\n * http://ecma-international.org/ecma-402/7.0/index.html#sec-canonicalizelocalelist\n * @param locales\n */\nexport function CanonicalizeLocaleList(locales) {\n    return Intl.getCanonicalLocales(locales);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,uBAAuB,OAAO;IAC1C,OAAO,KAAK,mBAAmB,CAAC;AACpC", "ignoreList": [0]}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/languageMatching.js"], "sourcesContent": ["export var data = {\n    supplemental: {\n        languageMatching: {\n            'written-new': [\n                {\n                    paradigmLocales: {\n                        _locales: 'en en_GB es es_419 pt_BR pt_PT',\n                    },\n                },\n                {\n                    $enUS: {\n                        _value: 'AS+CA+GU+MH+MP+PH+PR+UM+US+VI',\n                    },\n                },\n                {\n                    $cnsar: {\n                        _value: 'HK+MO',\n                    },\n                },\n                {\n                    $americas: {\n                        _value: '019',\n                    },\n                },\n                {\n                    $maghreb: {\n                        _value: 'MA+DZ+TN+LY+MR+EH',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nb',\n                        _distance: '1',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'hr',\n                        _distance: '4',\n                    },\n                },\n                {\n                    bs: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    hr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    sr: {\n                        _desired: 'sh',\n                        _distance: '4',\n                    },\n                },\n                {\n                    aa: {\n                        _desired: 'ssy',\n                        _distance: '4',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'gsw',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'lb',\n                        _distance: '4',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'da',\n                        _distance: '8',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ab',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ach',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'af',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ak',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'am',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ay',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'az',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ur: {\n                        _desired: 'bal',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'be',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bem',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'bh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'bn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'bo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'br',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'ca',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fil: {\n                        _desired: 'ceb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'chr',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ckb',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'co',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'crs',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sk: {\n                        _desired: 'cs',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'cy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ee',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'eo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'eu',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    da: {\n                        _desired: 'fo',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nl: {\n                        _desired: 'fy',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ga',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gaa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'gd',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gl',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'gn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'gu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ha',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'haw',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ht',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'hy',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ia',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ig',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'is',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'jv',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ka',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'kg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'kk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'km',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'kri',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    tr: {\n                        _desired: 'ku',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'ky',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    it: {\n                        _desired: 'la',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'ln',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'lo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'loz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'lua',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mai',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mfe',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'mg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mi',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ml',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'mn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'mr',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'ms',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'mt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'my',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ne',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    nb: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    no: {\n                        _desired: 'nn',\n                        _distance: '20',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nso',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ny',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'nyn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'oc',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'om',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'or',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'pcm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ps',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    es: {\n                        _desired: 'qu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    de: {\n                        _desired: 'rm',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'rn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'rw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    hi: {\n                        _desired: 'sa',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sd',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'si',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'so',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sq',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'st',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    id: {\n                        _desired: 'su',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'sw',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ta',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'te',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tg',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ti',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tk',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tlh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tn',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'to',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'tt',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'tum',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'ug',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uk',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'ur',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ru: {\n                        _desired: 'uz',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    fr: {\n                        _desired: 'wo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'xh',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yi',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'yo',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'za',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    en: {\n                        _desired: 'zu',\n                        _distance: '30',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aao',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'abv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'acy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'adf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aeb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'aec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'afb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ajp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'apd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ars',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ary',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'arz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'auz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'avl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ayp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'bbz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'pga',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'shu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ar: {\n                        _desired: 'ssh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    az: {\n                        _desired: 'azb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    et: {\n                        _desired: 'vro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'ffm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ff: {\n                        _desired: 'fuv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gnw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'gun',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    gn: {\n                        _desired: 'nhd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    iu: {\n                        _desired: 'ikt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'enb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'eyo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'niq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'oki',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'pko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'sgc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tec',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kln: {\n                        _desired: 'tuy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kok: {\n                        _desired: 'gom',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    kpe: {\n                        _desired: 'gkp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'ida',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lkb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lko',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lks',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lri',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lrm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lsm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lto',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lts',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'lwg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nle',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'nyd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    luy: {\n                        _desired: 'rag',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    lv: {\n                        _desired: 'ltg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bhr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bjq',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'bzc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'msh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'skg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tdx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'tkg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'txy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mg: {\n                        _desired: 'xmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    mn: {\n                        _desired: 'mvf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bjn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'btj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'bvu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'coa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'dup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'hji',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'id',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'jax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kvr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'kxd',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lce',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'lcf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'liw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'max',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'meo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mfb',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'min',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mqg',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'msi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'mui',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'orn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'ors',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pel',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'pse',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'tmw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'urk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'vkt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'xmm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zlm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ms: {\n                        _desired: 'zmi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ne: {\n                        _desired: 'dty',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'gax',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'hae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    om: {\n                        _desired: 'orc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    or: {\n                        _desired: 'spv',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pbt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    ps: {\n                        _desired: 'pst',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qub',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qud',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quf',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qug',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quk',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qul',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qup',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qur',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qus',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qux',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'quy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qve',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvj',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvm',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qvz',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qwh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qws',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxa',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxl',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    qu: {\n                        _desired: 'qxw',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdc',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sdn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sc: {\n                        _desired: 'sro',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aae',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aat',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    sq: {\n                        _desired: 'aln',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    syr: {\n                        _desired: 'aii',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    uz: {\n                        _desired: 'uzs',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    yi: {\n                        _desired: 'yih',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cdo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cjy',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'cpx',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'czo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'gan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hak',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'hsn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'lzh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'mnp',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'nan',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'wuu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    zh: {\n                        _desired: 'yue',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*': {\n                        _desired: '*',\n                        _distance: '80',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'am-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'az-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'bn-Beng',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'bo-Tibt',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'hy-Armn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ka-Geor',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'km-Khmr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'kn-Knda',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'lo-Laoo',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ml-Mlym',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'my-Mymr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ne-Deva',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'or-Orya',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'pa-Guru',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ps-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'sd-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'si-Sinh',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ta-Taml',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'te-Telu',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ti-Ethi',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'tk-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'ur-Arab',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ru-Cyrl': {\n                        _desired: 'uz-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'en-Latn': {\n                        _desired: 'yi-Hebr',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'sr-Cyrl': {\n                        _desired: 'sr-Latn',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'za-Latn',\n                        _distance: '10',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hant': {\n                        _desired: 'zh-Hani',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ar-Arab': {\n                        _desired: 'ar-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'bn-Beng': {\n                        _desired: 'bn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'gu-Gujr': {\n                        _desired: 'gu-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'hi-Deva': {\n                        _desired: 'hi-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'kn-Knda': {\n                        _desired: 'kn-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ml-Mlym': {\n                        _desired: 'ml-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'mr-Deva': {\n                        _desired: 'mr-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ta-Taml': {\n                        _desired: 'ta-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'te-Telu': {\n                        _desired: 'te-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'zh-Hans': {\n                        _desired: 'zh-Latn',\n                        _distance: '20',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Latn',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Jpan': {\n                        _desired: 'ja-Hrkt',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Hira',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ja-Hrkt': {\n                        _desired: 'ja-Kana',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hani',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Hang',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Kore': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    'ko-Hang': {\n                        _desired: 'ko-Jamo',\n                        _distance: '5',\n                        _oneway: 'true',\n                    },\n                },\n                {\n                    '*-*': {\n                        _desired: '*-*',\n                        _distance: '50',\n                    },\n                },\n                {\n                    'ar-*-$maghreb': {\n                        _desired: 'ar-*-$maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-$!maghreb': {\n                        _desired: 'ar-*-$!maghreb',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'ar-*-*': {\n                        _desired: 'ar-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'en-*-$enUS': {\n                        _desired: 'en-*-$enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-GB': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '3',\n                    },\n                },\n                {\n                    'en-*-$!enUS': {\n                        _desired: 'en-*-$!enUS',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'en-*-*': {\n                        _desired: 'en-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'es-*-$americas': {\n                        _desired: 'es-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-$!americas': {\n                        _desired: 'es-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'es-*-*': {\n                        _desired: 'es-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'pt-*-$americas': {\n                        _desired: 'pt-*-$americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-$!americas': {\n                        _desired: 'pt-*-$!americas',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'pt-*-*': {\n                        _desired: 'pt-*-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    'zh-Hant-$cnsar': {\n                        _desired: 'zh-Hant-$cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-$!cnsar': {\n                        _desired: 'zh-Hant-$!cnsar',\n                        _distance: '4',\n                    },\n                },\n                {\n                    'zh-Hant-*': {\n                        _desired: 'zh-Hant-*',\n                        _distance: '5',\n                    },\n                },\n                {\n                    '*-*-*': {\n                        _desired: '*-*-*',\n                        _distance: '4',\n                    },\n                },\n            ],\n        },\n    },\n};\n"], "names": [], "mappings": ";;;AAAO,IAAI,OAAO;IACd,cAAc;QACV,kBAAkB;YACd,eAAe;gBACX;oBACI,iBAAiB;wBACb,UAAU;oBACd;gBACJ;gBACA;oBACI,OAAO;wBACH,QAAQ;oBACZ;gBACJ;gBACA;oBACI,QAAQ;wBACJ,QAAQ;oBACZ;gBACJ;gBACA;oBACI,WAAW;wBACP,QAAQ;oBACZ;gBACJ;gBACA;oBACI,UAAU;wBACN,QAAQ;oBACZ;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,IAAI;wBACA,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,KAAK;wBACD,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;oBACb;gBACJ;gBACA;oBACI,OAAO;wBACH,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,iBAAiB;wBACb,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,kBAAkB;wBACd,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,UAAU;wBACN,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,cAAc;wBACV,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,WAAW;wBACP,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,eAAe;wBACX,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,UAAU;wBACN,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,kBAAkB;wBACd,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,mBAAmB;wBACf,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,UAAU;wBACN,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,kBAAkB;wBACd,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,mBAAmB;wBACf,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,UAAU;wBACN,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,kBAAkB;wBACd,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,mBAAmB;wBACf,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,aAAa;wBACT,UAAU;wBACV,WAAW;oBACf;gBACJ;gBACA;oBACI,SAAS;wBACL,UAAU;wBACV,WAAW;oBACf;gBACJ;aACH;QACL;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/regions.generated.js"], "sourcesContent": ["// This file is generated from regions-gen.ts\nexport var regions = {\n    \"001\": [\n        \"001\",\n        \"001-status-grouping\",\n        \"002\",\n        \"005\",\n        \"009\",\n        \"011\",\n        \"013\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"019\",\n        \"021\",\n        \"029\",\n        \"030\",\n        \"034\",\n        \"035\",\n        \"039\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AC\",\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AI\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AQ\",\n        \"AR\",\n        \"AS\",\n        \"AT\",\n        \"AU\",\n        \"AW\",\n        \"AX\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BL\",\n        \"BM\",\n        \"BN\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BV\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CC\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CK\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CP\",\n        \"CQ\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CW\",\n        \"CX\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DG\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EA\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"EU\",\n        \"EZ\",\n        \"FI\",\n        \"FJ\",\n        \"FK\",\n        \"FM\",\n        \"FO\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GF\",\n        \"GG\",\n        \"GH\",\n        \"GI\",\n        \"GL\",\n        \"GM\",\n        \"GN\",\n        \"GP\",\n        \"GQ\",\n        \"GR\",\n        \"GS\",\n        \"GT\",\n        \"GU\",\n        \"GW\",\n        \"GY\",\n        \"HK\",\n        \"HM\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"IC\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IM\",\n        \"IN\",\n        \"IO\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KY\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MF\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MP\",\n        \"MQ\",\n        \"MR\",\n        \"MS\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NC\",\n        \"NE\",\n        \"NF\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PF\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PM\",\n        \"PN\",\n        \"PR\",\n        \"PS\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"QO\",\n        \"RE\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SH\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SX\",\n        \"SY\",\n        \"SZ\",\n        \"TA\",\n        \"TC\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TK\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TW\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UM\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VA\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\",\n        \"VN\",\n        \"VU\",\n        \"WF\",\n        \"WS\",\n        \"XK\",\n        \"YE\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"002\": [\n        \"002\",\n        \"002-status-grouping\",\n        \"011\",\n        \"014\",\n        \"015\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IC\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"LY\",\n        \"MA\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SD\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TN\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"003\": [\n        \"003\",\n        \"013\",\n        \"021\",\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BQ\",\n        \"BS\",\n        \"BZ\",\n        \"CA\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GL\",\n        \"GP\",\n        \"GT\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PM\",\n        \"PR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"005\": [\n        \"005\",\n        \"AR\",\n        \"BO\",\n        \"BR\",\n        \"BV\",\n        \"CL\",\n        \"CO\",\n        \"EC\",\n        \"FK\",\n        \"GF\",\n        \"GS\",\n        \"GY\",\n        \"PE\",\n        \"PY\",\n        \"SR\",\n        \"UY\",\n        \"VE\"\n    ],\n    \"009\": [\n        \"009\",\n        \"053\",\n        \"054\",\n        \"057\",\n        \"061\",\n        \"AC\",\n        \"AQ\",\n        \"AS\",\n        \"AU\",\n        \"CC\",\n        \"CK\",\n        \"CP\",\n        \"CX\",\n        \"DG\",\n        \"FJ\",\n        \"FM\",\n        \"GU\",\n        \"HM\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NC\",\n        \"NF\",\n        \"NR\",\n        \"NU\",\n        \"NZ\",\n        \"PF\",\n        \"PG\",\n        \"PN\",\n        \"PW\",\n        \"QO\",\n        \"SB\",\n        \"TA\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"UM\",\n        \"VU\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"011\": [\n        \"011\",\n        \"BF\",\n        \"BJ\",\n        \"CI\",\n        \"CV\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GW\",\n        \"LR\",\n        \"ML\",\n        \"MR\",\n        \"NE\",\n        \"NG\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"TG\"\n    ],\n    \"013\": [\n        \"013\",\n        \"BZ\",\n        \"CR\",\n        \"GT\",\n        \"HN\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"SV\"\n    ],\n    \"014\": [\n        \"014\",\n        \"BI\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"MG\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SO\",\n        \"SS\",\n        \"TF\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"015\": [\n        \"015\",\n        \"DZ\",\n        \"EA\",\n        \"EG\",\n        \"EH\",\n        \"IC\",\n        \"LY\",\n        \"MA\",\n        \"SD\",\n        \"TN\"\n    ],\n    \"017\": [\n        \"017\",\n        \"AO\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CM\",\n        \"GA\",\n        \"GQ\",\n        \"ST\",\n        \"TD\"\n    ],\n    \"018\": [\n        \"018\",\n        \"BW\",\n        \"LS\",\n        \"NA\",\n        \"SZ\",\n        \"ZA\"\n    ],\n    \"019\": [\n        \"003\",\n        \"005\",\n        \"013\",\n        \"019\",\n        \"019-status-grouping\",\n        \"021\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BM\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CA\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GL\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PM\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"US\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"021\": [\n        \"021\",\n        \"BM\",\n        \"CA\",\n        \"GL\",\n        \"PM\",\n        \"US\"\n    ],\n    \"029\": [\n        \"029\",\n        \"AG\",\n        \"AI\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BQ\",\n        \"BS\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"GD\",\n        \"GP\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"PR\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"VC\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"030\": [\n        \"030\",\n        \"CN\",\n        \"HK\",\n        \"JP\",\n        \"KP\",\n        \"KR\",\n        \"MN\",\n        \"MO\",\n        \"TW\"\n    ],\n    \"034\": [\n        \"034\",\n        \"AF\",\n        \"BD\",\n        \"BT\",\n        \"IN\",\n        \"IR\",\n        \"LK\",\n        \"MV\",\n        \"NP\",\n        \"PK\"\n    ],\n    \"035\": [\n        \"035\",\n        \"BN\",\n        \"ID\",\n        \"KH\",\n        \"LA\",\n        \"MM\",\n        \"MY\",\n        \"PH\",\n        \"SG\",\n        \"TH\",\n        \"TL\",\n        \"VN\"\n    ],\n    \"039\": [\n        \"039\",\n        \"AD\",\n        \"AL\",\n        \"BA\",\n        \"ES\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"IT\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"PT\",\n        \"RS\",\n        \"SI\",\n        \"SM\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"053\": [\n        \"053\",\n        \"AU\",\n        \"CC\",\n        \"CX\",\n        \"HM\",\n        \"NF\",\n        \"NZ\"\n    ],\n    \"054\": [\n        \"054\",\n        \"FJ\",\n        \"NC\",\n        \"PG\",\n        \"SB\",\n        \"VU\"\n    ],\n    \"057\": [\n        \"057\",\n        \"FM\",\n        \"GU\",\n        \"KI\",\n        \"MH\",\n        \"MP\",\n        \"NR\",\n        \"PW\",\n        \"UM\"\n    ],\n    \"061\": [\n        \"061\",\n        \"AS\",\n        \"CK\",\n        \"NU\",\n        \"PF\",\n        \"PN\",\n        \"TK\",\n        \"TO\",\n        \"TV\",\n        \"WF\",\n        \"WS\"\n    ],\n    \"142\": [\n        \"030\",\n        \"034\",\n        \"035\",\n        \"142\",\n        \"143\",\n        \"145\",\n        \"AE\",\n        \"AF\",\n        \"AM\",\n        \"AZ\",\n        \"BD\",\n        \"BH\",\n        \"BN\",\n        \"BT\",\n        \"CN\",\n        \"CY\",\n        \"GE\",\n        \"HK\",\n        \"ID\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"JO\",\n        \"JP\",\n        \"KG\",\n        \"KH\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LK\",\n        \"MM\",\n        \"MN\",\n        \"MO\",\n        \"MV\",\n        \"MY\",\n        \"NP\",\n        \"OM\",\n        \"PH\",\n        \"PK\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SG\",\n        \"SY\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TR\",\n        \"TW\",\n        \"UZ\",\n        \"VN\",\n        \"YE\"\n    ],\n    \"143\": [\n        \"143\",\n        \"KG\",\n        \"KZ\",\n        \"TJ\",\n        \"TM\",\n        \"UZ\"\n    ],\n    \"145\": [\n        \"145\",\n        \"AE\",\n        \"AM\",\n        \"AZ\",\n        \"BH\",\n        \"CY\",\n        \"GE\",\n        \"IL\",\n        \"IQ\",\n        \"JO\",\n        \"KW\",\n        \"LB\",\n        \"OM\",\n        \"PS\",\n        \"QA\",\n        \"SA\",\n        \"SY\",\n        \"TR\",\n        \"YE\"\n    ],\n    \"150\": [\n        \"039\",\n        \"150\",\n        \"151\",\n        \"154\",\n        \"155\",\n        \"AD\",\n        \"AL\",\n        \"AT\",\n        \"AX\",\n        \"BA\",\n        \"BE\",\n        \"BG\",\n        \"BY\",\n        \"CH\",\n        \"CQ\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"FI\",\n        \"FO\",\n        \"FR\",\n        \"GB\",\n        \"GG\",\n        \"GI\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"IT\",\n        \"JE\",\n        \"LI\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MK\",\n        \"MT\",\n        \"NL\",\n        \"NO\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"SE\",\n        \"SI\",\n        \"SJ\",\n        \"SK\",\n        \"SM\",\n        \"UA\",\n        \"VA\",\n        \"XK\"\n    ],\n    \"151\": [\n        \"151\",\n        \"BG\",\n        \"BY\",\n        \"CZ\",\n        \"HU\",\n        \"MD\",\n        \"PL\",\n        \"RO\",\n        \"RU\",\n        \"SK\",\n        \"UA\"\n    ],\n    \"154\": [\n        \"154\",\n        \"AX\",\n        \"CQ\",\n        \"DK\",\n        \"EE\",\n        \"FI\",\n        \"FO\",\n        \"GB\",\n        \"GG\",\n        \"IE\",\n        \"IM\",\n        \"IS\",\n        \"JE\",\n        \"LT\",\n        \"LV\",\n        \"NO\",\n        \"SE\",\n        \"SJ\"\n    ],\n    \"155\": [\n        \"155\",\n        \"AT\",\n        \"BE\",\n        \"CH\",\n        \"DE\",\n        \"FR\",\n        \"LI\",\n        \"LU\",\n        \"MC\",\n        \"NL\"\n    ],\n    \"202\": [\n        \"011\",\n        \"014\",\n        \"017\",\n        \"018\",\n        \"202\",\n        \"AO\",\n        \"BF\",\n        \"BI\",\n        \"BJ\",\n        \"BW\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CI\",\n        \"CM\",\n        \"CV\",\n        \"DJ\",\n        \"ER\",\n        \"ET\",\n        \"GA\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GW\",\n        \"IO\",\n        \"KE\",\n        \"KM\",\n        \"LR\",\n        \"LS\",\n        \"MG\",\n        \"ML\",\n        \"MR\",\n        \"MU\",\n        \"MW\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"RE\",\n        \"RW\",\n        \"SC\",\n        \"SH\",\n        \"SL\",\n        \"SN\",\n        \"SO\",\n        \"SS\",\n        \"ST\",\n        \"SZ\",\n        \"TD\",\n        \"TF\",\n        \"TG\",\n        \"TZ\",\n        \"UG\",\n        \"YT\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ],\n    \"419\": [\n        \"005\",\n        \"013\",\n        \"029\",\n        \"419\",\n        \"AG\",\n        \"AI\",\n        \"AR\",\n        \"AW\",\n        \"BB\",\n        \"BL\",\n        \"BO\",\n        \"BQ\",\n        \"BR\",\n        \"BS\",\n        \"BV\",\n        \"BZ\",\n        \"CL\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CW\",\n        \"DM\",\n        \"DO\",\n        \"EC\",\n        \"FK\",\n        \"GD\",\n        \"GF\",\n        \"GP\",\n        \"GS\",\n        \"GT\",\n        \"GY\",\n        \"HN\",\n        \"HT\",\n        \"JM\",\n        \"KN\",\n        \"KY\",\n        \"LC\",\n        \"MF\",\n        \"MQ\",\n        \"MS\",\n        \"MX\",\n        \"NI\",\n        \"PA\",\n        \"PE\",\n        \"PR\",\n        \"PY\",\n        \"SR\",\n        \"SV\",\n        \"SX\",\n        \"TC\",\n        \"TT\",\n        \"UY\",\n        \"VC\",\n        \"VE\",\n        \"VG\",\n        \"VI\"\n    ],\n    \"EU\": [\n        \"AT\",\n        \"BE\",\n        \"BG\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DK\",\n        \"EE\",\n        \"ES\",\n        \"EU\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"HR\",\n        \"HU\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PL\",\n        \"PT\",\n        \"RO\",\n        \"SE\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"EZ\": [\n        \"AT\",\n        \"BE\",\n        \"CY\",\n        \"DE\",\n        \"EE\",\n        \"ES\",\n        \"EZ\",\n        \"FI\",\n        \"FR\",\n        \"GR\",\n        \"IE\",\n        \"IT\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"MT\",\n        \"NL\",\n        \"PT\",\n        \"SI\",\n        \"SK\"\n    ],\n    \"QO\": [\n        \"AC\",\n        \"AQ\",\n        \"CP\",\n        \"DG\",\n        \"QO\",\n        \"TA\"\n    ],\n    \"UN\": [\n        \"AD\",\n        \"AE\",\n        \"AF\",\n        \"AG\",\n        \"AL\",\n        \"AM\",\n        \"AO\",\n        \"AR\",\n        \"AT\",\n        \"AU\",\n        \"AZ\",\n        \"BA\",\n        \"BB\",\n        \"BD\",\n        \"BE\",\n        \"BF\",\n        \"BG\",\n        \"BH\",\n        \"BI\",\n        \"BJ\",\n        \"BN\",\n        \"BO\",\n        \"BR\",\n        \"BS\",\n        \"BT\",\n        \"BW\",\n        \"BY\",\n        \"BZ\",\n        \"CA\",\n        \"CD\",\n        \"CF\",\n        \"CG\",\n        \"CH\",\n        \"CI\",\n        \"CL\",\n        \"CM\",\n        \"CN\",\n        \"CO\",\n        \"CR\",\n        \"CU\",\n        \"CV\",\n        \"CY\",\n        \"CZ\",\n        \"DE\",\n        \"DJ\",\n        \"DK\",\n        \"DM\",\n        \"DO\",\n        \"DZ\",\n        \"EC\",\n        \"EE\",\n        \"EG\",\n        \"ER\",\n        \"ES\",\n        \"ET\",\n        \"FI\",\n        \"FJ\",\n        \"FM\",\n        \"FR\",\n        \"GA\",\n        \"GB\",\n        \"GD\",\n        \"GE\",\n        \"GH\",\n        \"GM\",\n        \"GN\",\n        \"GQ\",\n        \"GR\",\n        \"GT\",\n        \"GW\",\n        \"GY\",\n        \"HN\",\n        \"HR\",\n        \"HT\",\n        \"HU\",\n        \"ID\",\n        \"IE\",\n        \"IL\",\n        \"IN\",\n        \"IQ\",\n        \"IR\",\n        \"IS\",\n        \"IT\",\n        \"JM\",\n        \"JO\",\n        \"JP\",\n        \"KE\",\n        \"KG\",\n        \"KH\",\n        \"KI\",\n        \"KM\",\n        \"KN\",\n        \"KP\",\n        \"KR\",\n        \"KW\",\n        \"KZ\",\n        \"LA\",\n        \"LB\",\n        \"LC\",\n        \"LI\",\n        \"LK\",\n        \"LR\",\n        \"LS\",\n        \"LT\",\n        \"LU\",\n        \"LV\",\n        \"LY\",\n        \"MA\",\n        \"MC\",\n        \"MD\",\n        \"ME\",\n        \"MG\",\n        \"MH\",\n        \"MK\",\n        \"ML\",\n        \"MM\",\n        \"MN\",\n        \"MR\",\n        \"MT\",\n        \"MU\",\n        \"MV\",\n        \"MW\",\n        \"MX\",\n        \"MY\",\n        \"MZ\",\n        \"NA\",\n        \"NE\",\n        \"NG\",\n        \"NI\",\n        \"NL\",\n        \"NO\",\n        \"NP\",\n        \"NR\",\n        \"NZ\",\n        \"OM\",\n        \"PA\",\n        \"PE\",\n        \"PG\",\n        \"PH\",\n        \"PK\",\n        \"PL\",\n        \"PT\",\n        \"PW\",\n        \"PY\",\n        \"QA\",\n        \"RO\",\n        \"RS\",\n        \"RU\",\n        \"RW\",\n        \"SA\",\n        \"SB\",\n        \"SC\",\n        \"SD\",\n        \"SE\",\n        \"SG\",\n        \"SI\",\n        \"SK\",\n        \"SL\",\n        \"SM\",\n        \"SN\",\n        \"SO\",\n        \"SR\",\n        \"SS\",\n        \"ST\",\n        \"SV\",\n        \"SY\",\n        \"SZ\",\n        \"TD\",\n        \"TG\",\n        \"TH\",\n        \"TJ\",\n        \"TL\",\n        \"TM\",\n        \"TN\",\n        \"TO\",\n        \"TR\",\n        \"TT\",\n        \"TV\",\n        \"TZ\",\n        \"UA\",\n        \"UG\",\n        \"UN\",\n        \"US\",\n        \"UY\",\n        \"UZ\",\n        \"VC\",\n        \"VE\",\n        \"VN\",\n        \"VU\",\n        \"WS\",\n        \"YE\",\n        \"ZA\",\n        \"ZM\",\n        \"ZW\"\n    ]\n};\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AACtC,IAAI,UAAU;IACjB,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;AACL", "ignoreList": [0]}}, {"offset": {"line": 3986, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/utils.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport { data as jsonData } from './languageMatching';\nimport { regions } from './regions.generated';\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n// This is effectively 2 languages in 2 different regions in the same cluster\nvar DEFAULT_MATCHING_THRESHOLD = 838;\nvar PROCESSED_DATA;\nfunction processData() {\n    var _a, _b;\n    if (!PROCESSED_DATA) {\n        var paradigmLocales = (_b = (_a = jsonData.supplemental.languageMatching['written-new'][0]) === null || _a === void 0 ? void 0 : _a.paradigmLocales) === null || _b === void 0 ? void 0 : _b._locales.split(' ');\n        var matchVariables = jsonData.supplemental.languageMatching['written-new'].slice(1, 5);\n        var data = jsonData.supplemental.languageMatching['written-new'].slice(5);\n        var matches = data.map(function (d) {\n            var key = Object.keys(d)[0];\n            var value = d[key];\n            return {\n                supported: key,\n                desired: value._desired,\n                distance: +value._distance,\n                oneway: value.oneway === 'true' ? true : false,\n            };\n        }, {});\n        PROCESSED_DATA = {\n            matches: matches,\n            matchVariables: matchVariables.reduce(function (all, d) {\n                var key = Object.keys(d)[0];\n                var value = d[key];\n                all[key.slice(1)] = value._value.split('+');\n                return all;\n            }, {}),\n            paradigmLocales: __spreadArray(__spreadArray([], paradigmLocales, true), paradigmLocales.map(function (l) {\n                return new Intl.Locale(l.replace(/_/g, '-')).maximize().toString();\n            }), true),\n        };\n    }\n    return PROCESSED_DATA;\n}\nfunction isMatched(locale, languageMatchInfoLocale, matchVariables) {\n    var _a = languageMatchInfoLocale.split('-'), language = _a[0], script = _a[1], region = _a[2];\n    var matches = true;\n    if (region && region[0] === '$') {\n        var shouldInclude = region[1] !== '!';\n        var matchRegions = shouldInclude\n            ? matchVariables[region.slice(1)]\n            : matchVariables[region.slice(2)];\n        var expandedMatchedRegions = matchRegions\n            .map(function (r) { return regions[r] || [r]; })\n            .reduce(function (all, list) { return __spreadArray(__spreadArray([], all, true), list, true); }, []);\n        matches && (matches = !(expandedMatchedRegions.indexOf(locale.region || '') > 1 !=\n            shouldInclude));\n    }\n    else {\n        matches && (matches = locale.region\n            ? region === '*' || region === locale.region\n            : true);\n    }\n    matches && (matches = locale.script ? script === '*' || script === locale.script : true);\n    matches && (matches = locale.language\n        ? language === '*' || language === locale.language\n        : true);\n    return matches;\n}\nfunction serializeLSR(lsr) {\n    return [lsr.language, lsr.script, lsr.region].filter(Boolean).join('-');\n}\nfunction findMatchingDistanceForLSR(desired, supported, data) {\n    for (var _i = 0, _a = data.matches; _i < _a.length; _i++) {\n        var d = _a[_i];\n        var matches = isMatched(desired, d.desired, data.matchVariables) &&\n            isMatched(supported, d.supported, data.matchVariables);\n        if (!d.oneway && !matches) {\n            matches =\n                isMatched(desired, d.supported, data.matchVariables) &&\n                    isMatched(supported, d.desired, data.matchVariables);\n        }\n        if (matches) {\n            var distance = d.distance * 10;\n            if (data.paradigmLocales.indexOf(serializeLSR(desired)) > -1 !=\n                data.paradigmLocales.indexOf(serializeLSR(supported)) > -1) {\n                return distance - 1;\n            }\n            return distance;\n        }\n    }\n    throw new Error('No matching distance found');\n}\nexport function findMatchingDistance(desired, supported) {\n    var desiredLocale = new Intl.Locale(desired).maximize();\n    var supportedLocale = new Intl.Locale(supported).maximize();\n    var desiredLSR = {\n        language: desiredLocale.language,\n        script: desiredLocale.script || '',\n        region: desiredLocale.region || '',\n    };\n    var supportedLSR = {\n        language: supportedLocale.language,\n        script: supportedLocale.script || '',\n        region: supportedLocale.region || '',\n    };\n    var matchingDistance = 0;\n    var data = processData();\n    if (desiredLSR.language !== supportedLSR.language) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: '',\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: '',\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.script !== supportedLSR.script) {\n        matchingDistance += findMatchingDistanceForLSR({\n            language: desiredLocale.language,\n            script: desiredLSR.script,\n            region: '',\n        }, {\n            language: supportedLocale.language,\n            script: desiredLSR.script,\n            region: '',\n        }, data);\n    }\n    if (desiredLSR.region !== supportedLSR.region) {\n        matchingDistance += findMatchingDistanceForLSR(desiredLSR, supportedLSR, data);\n    }\n    return matchingDistance;\n}\nexport function findBestMatch(requestedLocales, supportedLocales, threshold) {\n    if (threshold === void 0) { threshold = DEFAULT_MATCHING_THRESHOLD; }\n    var lowestDistance = Infinity;\n    var result = {\n        matchedDesiredLocale: '',\n        distances: {},\n    };\n    requestedLocales.forEach(function (desired, i) {\n        if (!result.distances[desired]) {\n            result.distances[desired] = {};\n        }\n        supportedLocales.forEach(function (supported) {\n            // Add some weight to the distance based on the order of the supported locales\n            // Add penalty for the order of the requested locales, which currently is 0 since ECMA-402\n            // doesn't really have room for weighted locales like `en; q=0.1`\n            var distance = findMatchingDistance(desired, supported) + 0 + i * 40;\n            result.distances[desired][supported] = distance;\n            if (distance < lowestDistance) {\n                lowestDistance = distance;\n                result.matchedDesiredLocale = desired;\n                result.matchedSupportedLocale = supported;\n            }\n        });\n    });\n    if (lowestDistance >= threshold) {\n        result.matchedDesiredLocale = undefined;\n        result.matchedSupportedLocale = undefined;\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACO,IAAI,mCAAmC;AACvC,SAAS,UAAU,SAAS,EAAE,OAAO,EAAE,GAAG;IAC7C,IAAI,QAAQ,KAAK,GAAG;QAAE,MAAM;IAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,IAAI;IAClB;AACJ;AACA,6EAA6E;AAC7E,IAAI,6BAA6B;AACjC,IAAI;AACJ,SAAS;IACL,IAAI,IAAI;IACR,IAAI,CAAC,gBAAgB;QACjB,IAAI,kBAAkB,CAAC,KAAK,CAAC,KAAK,mRAAA,CAAA,OAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC5M,IAAI,iBAAiB,mRAAA,CAAA,OAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;QACpF,IAAI,OAAO,mRAAA,CAAA,OAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC;QACvE,IAAI,UAAU,KAAK,GAAG,CAAC,SAAU,CAAC;YAC9B,IAAI,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAC,CAAC,IAAI;YAClB,OAAO;gBACH,WAAW;gBACX,SAAS,MAAM,QAAQ;gBACvB,UAAU,CAAC,MAAM,SAAS;gBAC1B,QAAQ,MAAM,MAAM,KAAK,SAAS,OAAO;YAC7C;QACJ,GAAG,CAAC;QACJ,iBAAiB;YACb,SAAS;YACT,gBAAgB,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;gBAClD,IAAI,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE;gBAC3B,IAAI,QAAQ,CAAC,CAAC,IAAI;gBAClB,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;gBACvC,OAAO;YACX,GAAG,CAAC;YACJ,iBAAiB,CAAA,GAAA,gMAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,gMAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,iBAAiB,OAAO,gBAAgB,GAAG,CAAC,SAAU,CAAC;gBACpG,OAAO,IAAI,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,QAAQ,GAAG,QAAQ;YACpE,IAAI;QACR;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,MAAM,EAAE,uBAAuB,EAAE,cAAc;IAC9D,IAAI,KAAK,wBAAwB,KAAK,CAAC,MAAM,WAAW,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;IAC7F,IAAI,UAAU;IACd,IAAI,UAAU,MAAM,CAAC,EAAE,KAAK,KAAK;QAC7B,IAAI,gBAAgB,MAAM,CAAC,EAAE,KAAK;QAClC,IAAI,eAAe,gBACb,cAAc,CAAC,OAAO,KAAK,CAAC,GAAG,GAC/B,cAAc,CAAC,OAAO,KAAK,CAAC,GAAG;QACrC,IAAI,yBAAyB,aACxB,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,uRAAA,CAAA,UAAO,CAAC,EAAE,IAAI;gBAAC;aAAE;QAAE,GAC7C,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;YAAI,OAAO,CAAA,GAAA,gMAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,gMAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,KAAK,OAAO,MAAM;QAAO,GAAG,EAAE;QACxG,WAAW,CAAC,UAAU,CAAC,CAAC,uBAAuB,OAAO,CAAC,OAAO,MAAM,IAAI,MAAM,KAC1E,aAAa,CAAC;IACtB,OACK;QACD,WAAW,CAAC,UAAU,OAAO,MAAM,GAC7B,WAAW,OAAO,WAAW,OAAO,MAAM,GAC1C,IAAI;IACd;IACA,WAAW,CAAC,UAAU,OAAO,MAAM,GAAG,WAAW,OAAO,WAAW,OAAO,MAAM,GAAG,IAAI;IACvF,WAAW,CAAC,UAAU,OAAO,QAAQ,GAC/B,aAAa,OAAO,aAAa,OAAO,QAAQ,GAChD,IAAI;IACV,OAAO;AACX;AACA,SAAS,aAAa,GAAG;IACrB,OAAO;QAAC,IAAI,QAAQ;QAAE,IAAI,MAAM;QAAE,IAAI,MAAM;KAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;AACvE;AACA,SAAS,2BAA2B,OAAO,EAAE,SAAS,EAAE,IAAI;IACxD,IAAK,IAAI,KAAK,GAAG,KAAK,KAAK,OAAO,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;QACtD,IAAI,IAAI,EAAE,CAAC,GAAG;QACd,IAAI,UAAU,UAAU,SAAS,EAAE,OAAO,EAAE,KAAK,cAAc,KAC3D,UAAU,WAAW,EAAE,SAAS,EAAE,KAAK,cAAc;QACzD,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,SAAS;YACvB,UACI,UAAU,SAAS,EAAE,SAAS,EAAE,KAAK,cAAc,KAC/C,UAAU,WAAW,EAAE,OAAO,EAAE,KAAK,cAAc;QAC/D;QACA,IAAI,SAAS;YACT,IAAI,WAAW,EAAE,QAAQ,GAAG;YAC5B,IAAI,KAAK,eAAe,CAAC,OAAO,CAAC,aAAa,YAAY,CAAC,KACvD,KAAK,eAAe,CAAC,OAAO,CAAC,aAAa,cAAc,CAAC,GAAG;gBAC5D,OAAO,WAAW;YACtB;YACA,OAAO;QACX;IACJ;IACA,MAAM,IAAI,MAAM;AACpB;AACO,SAAS,qBAAqB,OAAO,EAAE,SAAS;IACnD,IAAI,gBAAgB,IAAI,KAAK,MAAM,CAAC,SAAS,QAAQ;IACrD,IAAI,kBAAkB,IAAI,KAAK,MAAM,CAAC,WAAW,QAAQ;IACzD,IAAI,aAAa;QACb,UAAU,cAAc,QAAQ;QAChC,QAAQ,cAAc,MAAM,IAAI;QAChC,QAAQ,cAAc,MAAM,IAAI;IACpC;IACA,IAAI,eAAe;QACf,UAAU,gBAAgB,QAAQ;QAClC,QAAQ,gBAAgB,MAAM,IAAI;QAClC,QAAQ,gBAAgB,MAAM,IAAI;IACtC;IACA,IAAI,mBAAmB;IACvB,IAAI,OAAO;IACX,IAAI,WAAW,QAAQ,KAAK,aAAa,QAAQ,EAAE;QAC/C,oBAAoB,2BAA2B;YAC3C,UAAU,cAAc,QAAQ;YAChC,QAAQ;YACR,QAAQ;QACZ,GAAG;YACC,UAAU,gBAAgB,QAAQ;YAClC,QAAQ;YACR,QAAQ;QACZ,GAAG;IACP;IACA,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC3C,oBAAoB,2BAA2B;YAC3C,UAAU,cAAc,QAAQ;YAChC,QAAQ,WAAW,MAAM;YACzB,QAAQ;QACZ,GAAG;YACC,UAAU,gBAAgB,QAAQ;YAClC,QAAQ,WAAW,MAAM;YACzB,QAAQ;QACZ,GAAG;IACP;IACA,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC3C,oBAAoB,2BAA2B,YAAY,cAAc;IAC7E;IACA,OAAO;AACX;AACO,SAAS,cAAc,gBAAgB,EAAE,gBAAgB,EAAE,SAAS;IACvE,IAAI,cAAc,KAAK,GAAG;QAAE,YAAY;IAA4B;IACpE,IAAI,iBAAiB;IACrB,IAAI,SAAS;QACT,sBAAsB;QACtB,WAAW,CAAC;IAChB;IACA,iBAAiB,OAAO,CAAC,SAAU,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,EAAE;YAC5B,OAAO,SAAS,CAAC,QAAQ,GAAG,CAAC;QACjC;QACA,iBAAiB,OAAO,CAAC,SAAU,SAAS;YACxC,8EAA8E;YAC9E,0FAA0F;YAC1F,iEAAiE;YACjE,IAAI,WAAW,qBAAqB,SAAS,aAAa,IAAI,IAAI;YAClE,OAAO,SAAS,CAAC,QAAQ,CAAC,UAAU,GAAG;YACvC,IAAI,WAAW,gBAAgB;gBAC3B,iBAAiB;gBACjB,OAAO,oBAAoB,GAAG;gBAC9B,OAAO,sBAAsB,GAAG;YACpC;QACJ;IACJ;IACA,IAAI,kBAAkB,WAAW;QAC7B,OAAO,oBAAoB,GAAG;QAC9B,OAAO,sBAAsB,GAAG;IACpC;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4164, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/BestFitMatcher.js"], "sourcesContent": ["import { UNICODE_EXTENSION_SEQUENCE_REGEX, findBestMatch } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-bestfitmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var foundLocale;\n    var extension;\n    var noExtensionLocales = [];\n    var noExtensionLocaleMap = requestedLocales.reduce(function (all, l) {\n        var noExtensionLocale = l.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        noExtensionLocales.push(noExtensionLocale);\n        all[noExtensionLocale] = l;\n        return all;\n    }, {});\n    var result = findBestMatch(noExtensionLocales, availableLocales);\n    if (result.matchedSupportedLocale && result.matchedDesiredLocale) {\n        foundLocale = result.matchedSupportedLocale;\n        extension =\n            noExtensionLocaleMap[result.matchedDesiredLocale].slice(result.matchedDesiredLocale.length) || undefined;\n    }\n    if (!foundLocale) {\n        return { locale: getDefaultLocale() };\n    }\n    return {\n        locale: foundLocale,\n        extension: extension,\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS,eAAe,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;IAC/E,IAAI;IACJ,IAAI;IACJ,IAAI,qBAAqB,EAAE;IAC3B,IAAI,uBAAuB,iBAAiB,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QAC/D,IAAI,oBAAoB,EAAE,OAAO,CAAC,wQAAA,CAAA,mCAAgC,EAAE;QACpE,mBAAmB,IAAI,CAAC;QACxB,GAAG,CAAC,kBAAkB,GAAG;QACzB,OAAO;IACX,GAAG,CAAC;IACJ,IAAI,SAAS,CAAA,GAAA,wQAAA,CAAA,gBAAa,AAAD,EAAE,oBAAoB;IAC/C,IAAI,OAAO,sBAAsB,IAAI,OAAO,oBAAoB,EAAE;QAC9D,cAAc,OAAO,sBAAsB;QAC3C,YACI,oBAAoB,CAAC,OAAO,oBAAoB,CAAC,CAAC,KAAK,CAAC,OAAO,oBAAoB,CAAC,MAAM,KAAK;IACvG;IACA,IAAI,CAAC,aAAa;QACd,OAAO;YAAE,QAAQ;QAAmB;IACxC;IACA,OAAO;QACH,QAAQ;QACR,WAAW;IACf;AACJ", "ignoreList": [0]}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeUValue.js"], "sourcesContent": ["import { invariant } from './utils';\nexport function CanonicalizeUValue(ukey, uvalue) {\n    // TODO: Implement algorithm for CanonicalizeUValue per https://tc39.es/ecma402/#sec-canonicalizeuvalue\n    var lowerValue = uvalue.toLowerCase();\n    invariant(ukey !== undefined, \"ukey must be defined\");\n    var canonicalized = lowerValue;\n    return canonicalized;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,mBAAmB,IAAI,EAAE,MAAM;IAC3C,uGAAuG;IACvG,IAAI,aAAa,OAAO,WAAW;IACnC,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,SAAS,WAAW;IAC9B,IAAI,gBAAgB;IACpB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4214, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/CanonicalizeUnicodeLocaleId.js"], "sourcesContent": ["export function CanonicalizeUnicodeLocaleId(locale) {\n    return Intl.getCanonicalLocales(locale)[0];\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,4BAA4B,MAAM;IAC9C,OAAO,KAAK,mBAAmB,CAAC,OAAO,CAAC,EAAE;AAC9C", "ignoreList": [0]}}, {"offset": {"line": 4224, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/InsertUnicodeExtensionAndCanonicalize.js"], "sourcesContent": ["import { CanonicalizeUnicodeLocaleId } from './CanonicalizeUnicodeLocaleId';\nimport { invariant } from './utils';\nexport function InsertUnicodeExtensionAndCanonicalize(locale, attributes, keywords) {\n    invariant(locale.indexOf('-u-') === -1, 'Expected locale to not have a Unicode locale extension');\n    var extension = '-u';\n    for (var _i = 0, attributes_1 = attributes; _i < attributes_1.length; _i++) {\n        var attr = attributes_1[_i];\n        extension += \"-\".concat(attr);\n    }\n    for (var _a = 0, keywords_1 = keywords; _a < keywords_1.length; _a++) {\n        var kw = keywords_1[_a];\n        var key = kw.key, value = kw.value;\n        extension += \"-\".concat(key);\n        if (value !== '') {\n            extension += \"-\".concat(value);\n        }\n    }\n    if (extension === '-u') {\n        return CanonicalizeUnicodeLocaleId(locale);\n    }\n    var privateIndex = locale.indexOf('-x-');\n    var newLocale;\n    if (privateIndex === -1) {\n        newLocale = locale + extension;\n    }\n    else {\n        var preExtension = locale.slice(0, privateIndex);\n        var postExtension = locale.slice(privateIndex);\n        newLocale = preExtension + extension + postExtension;\n    }\n    return CanonicalizeUnicodeLocaleId(newLocale);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,sCAAsC,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC9E,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG;IACxC,IAAI,YAAY;IAChB,IAAK,IAAI,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,MAAM,EAAE,KAAM;QACxE,IAAI,OAAO,YAAY,CAAC,GAAG;QAC3B,aAAa,IAAI,MAAM,CAAC;IAC5B;IACA,IAAK,IAAI,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,MAAM,EAAE,KAAM;QAClE,IAAI,KAAK,UAAU,CAAC,GAAG;QACvB,IAAI,MAAM,GAAG,GAAG,EAAE,QAAQ,GAAG,KAAK;QAClC,aAAa,IAAI,MAAM,CAAC;QACxB,IAAI,UAAU,IAAI;YACd,aAAa,IAAI,MAAM,CAAC;QAC5B;IACJ;IACA,IAAI,cAAc,MAAM;QACpB,OAAO,CAAA,GAAA,8RAAA,CAAA,8BAA2B,AAAD,EAAE;IACvC;IACA,IAAI,eAAe,OAAO,OAAO,CAAC;IAClC,IAAI;IACJ,IAAI,iBAAiB,CAAC,GAAG;QACrB,YAAY,SAAS;IACzB,OACK;QACD,IAAI,eAAe,OAAO,KAAK,CAAC,GAAG;QACnC,IAAI,gBAAgB,OAAO,KAAK,CAAC;QACjC,YAAY,eAAe,YAAY;IAC3C;IACA,OAAO,CAAA,GAAA,8RAAA,CAAA,8BAA2B,AAAD,EAAE;AACvC", "ignoreList": [0]}}, {"offset": {"line": 4264, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/BestAvailableLocale.js"], "sourcesContent": ["/**\n * https://tc39.es/ecma402/#sec-bestavailablelocale\n * @param availableLocales\n * @param locale\n */\nexport function BestAvailableLocale(availableLocales, locale) {\n    var candidate = locale;\n    while (true) {\n        if (availableLocales.indexOf(candidate) > -1) {\n            return candidate;\n        }\n        var pos = candidate.lastIndexOf('-');\n        if (!~pos) {\n            return undefined;\n        }\n        if (pos >= 2 && candidate[pos - 2] === '-') {\n            pos -= 2;\n        }\n        candidate = candidate.slice(0, pos);\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,SAAS,oBAAoB,gBAAgB,EAAE,MAAM;IACxD,IAAI,YAAY;IAChB,MAAO,KAAM;QACT,IAAI,iBAAiB,OAAO,CAAC,aAAa,CAAC,GAAG;YAC1C,OAAO;QACX;QACA,IAAI,MAAM,UAAU,WAAW,CAAC;QAChC,IAAI,CAAC,CAAC,KAAK;YACP,OAAO;QACX;QACA,IAAI,OAAO,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,KAAK;YACxC,OAAO;QACX;QACA,YAAY,UAAU,KAAK,CAAC,GAAG;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 4291, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/LookupMatcher.js"], "sourcesContent": ["import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function LookupMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n    var result = { locale: '' };\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            result.locale = availableLocale;\n            if (locale !== noExtensionLocale) {\n                result.extension = locale.slice(noExtensionLocale.length, locale.length);\n            }\n            return result;\n        }\n    }\n    result.locale = getDefaultLocale();\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAOO,SAAS,cAAc,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;IAC9E,IAAI,SAAS;QAAE,QAAQ;IAAG;IAC1B,IAAK,IAAI,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,MAAM,EAAE,KAAM;QAC1F,IAAI,SAAS,kBAAkB,CAAC,GAAG;QACnC,IAAI,oBAAoB,OAAO,OAAO,CAAC,wQAAA,CAAA,mCAAgC,EAAE;QACzE,IAAI,kBAAkB,CAAA,GAAA,sRAAA,CAAA,sBAAmB,AAAD,EAAE,kBAAkB;QAC5D,IAAI,iBAAiB;YACjB,OAAO,MAAM,GAAG;YAChB,IAAI,WAAW,mBAAmB;gBAC9B,OAAO,SAAS,GAAG,OAAO,KAAK,CAAC,kBAAkB,MAAM,EAAE,OAAO,MAAM;YAC3E;YACA,OAAO;QACX;IACJ;IACA,OAAO,MAAM,GAAG;IAChB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4321, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/UnicodeExtensionComponents.js"], "sourcesContent": ["import { invariant } from './utils';\nexport function UnicodeExtensionComponents(extension) {\n    invariant(extension === extension.toLowerCase(), 'Expected extension to be lowercase');\n    invariant(extension.slice(0, 3) === '-u-', 'Expected extension to be a Unicode locale extension');\n    var attributes = [];\n    var keywords = [];\n    var keyword;\n    var size = extension.length;\n    var k = 3;\n    while (k < size) {\n        var e = extension.indexOf('-', k);\n        var len = void 0;\n        if (e === -1) {\n            len = size - k;\n        }\n        else {\n            len = e - k;\n        }\n        var subtag = extension.slice(k, k + len);\n        invariant(len >= 2, 'Expected a subtag to have at least 2 characters');\n        if (keyword === undefined && len != 2) {\n            if (attributes.indexOf(subtag) === -1) {\n                attributes.push(subtag);\n            }\n        }\n        else if (len === 2) {\n            keyword = { key: subtag, value: '' };\n            if (keywords.find(function (k) { return k.key === (keyword === null || keyword === void 0 ? void 0 : keyword.key); }) === undefined) {\n                keywords.push(keyword);\n            }\n        }\n        else if ((keyword === null || keyword === void 0 ? void 0 : keyword.value) === '') {\n            keyword.value = subtag;\n        }\n        else {\n            invariant(keyword !== undefined, 'Expected keyword to be defined');\n            keyword.value += '-' + subtag;\n        }\n        k += len + 1;\n    }\n    return { attributes: attributes, keywords: keywords };\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,2BAA2B,SAAS;IAChD,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,WAAW,IAAI;IACjD,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,UAAU,KAAK,CAAC,GAAG,OAAO,OAAO;IAC3C,IAAI,aAAa,EAAE;IACnB,IAAI,WAAW,EAAE;IACjB,IAAI;IACJ,IAAI,OAAO,UAAU,MAAM;IAC3B,IAAI,IAAI;IACR,MAAO,IAAI,KAAM;QACb,IAAI,IAAI,UAAU,OAAO,CAAC,KAAK;QAC/B,IAAI,MAAM,KAAK;QACf,IAAI,MAAM,CAAC,GAAG;YACV,MAAM,OAAO;QACjB,OACK;YACD,MAAM,IAAI;QACd;QACA,IAAI,SAAS,UAAU,KAAK,CAAC,GAAG,IAAI;QACpC,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG;QACpB,IAAI,YAAY,aAAa,OAAO,GAAG;YACnC,IAAI,WAAW,OAAO,CAAC,YAAY,CAAC,GAAG;gBACnC,WAAW,IAAI,CAAC;YACpB;QACJ,OACK,IAAI,QAAQ,GAAG;YAChB,UAAU;gBAAE,KAAK;gBAAQ,OAAO;YAAG;YACnC,IAAI,SAAS,IAAI,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,KAAK,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;YAAG,OAAO,WAAW;gBACjI,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI;YAC/E,QAAQ,KAAK,GAAG;QACpB,OACK;YACD,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,YAAY,WAAW;YACjC,QAAQ,KAAK,IAAI,MAAM;QAC3B;QACA,KAAK,MAAM;IACf;IACA,OAAO;QAAE,YAAY;QAAY,UAAU;IAAS;AACxD", "ignoreList": [0]}}, {"offset": {"line": 4375, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/ResolveLocale.js"], "sourcesContent": ["import { BestFitMatcher } from './BestFitMatcher';\nimport { CanonicalizeUValue } from './CanonicalizeUValue';\nimport { InsertUnicodeExtensionAndCanonicalize } from './InsertUnicodeExtensionAndCanonicalize';\nimport { LookupMatcher } from './LookupMatcher';\nimport { UnicodeExtensionComponents } from './UnicodeExtensionComponents';\nimport { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-resolvelocale\n */\nexport function ResolveLocale(availableLocales, requestedLocales, options, relevantExtensionKeys, localeData, getDefaultLocale) {\n    var _a;\n    var matcher = options.localeMatcher;\n    var r;\n    if (matcher === 'lookup') {\n        r = LookupMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    else {\n        r = BestFitMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n    }\n    if (r == null) {\n        r = {\n            locale: getDefaultLocale(),\n            extension: '',\n        };\n    }\n    var foundLocale = r.locale;\n    var foundLocaleData = localeData[foundLocale];\n    // TODO: We can't really guarantee that the locale data is available\n    // invariant(\n    //   foundLocaleData !== undefined,\n    //   `Missing locale data for ${foundLocale}`\n    // )\n    var result = { locale: 'en', dataLocale: foundLocale };\n    var components;\n    var keywords;\n    if (r.extension) {\n        components = UnicodeExtensionComponents(r.extension);\n        keywords = components.keywords;\n    }\n    else {\n        keywords = [];\n    }\n    var supportedKeywords = [];\n    var _loop_1 = function (key) {\n        // TODO: Shouldn't default to empty array, see TODO above\n        var keyLocaleData = (_a = foundLocaleData === null || foundLocaleData === void 0 ? void 0 : foundLocaleData[key]) !== null && _a !== void 0 ? _a : [];\n        invariant(Array.isArray(keyLocaleData), \"keyLocaleData for \".concat(key, \" must be an array\"));\n        var value = keyLocaleData[0];\n        invariant(value === undefined || typeof value === 'string', \"value must be a string or undefined\");\n        var supportedKeyword = void 0;\n        var entry = keywords.find(function (k) { return k.key === key; });\n        if (entry) {\n            var requestedValue = entry.value;\n            if (requestedValue !== '') {\n                if (keyLocaleData.indexOf(requestedValue) > -1) {\n                    value = requestedValue;\n                    supportedKeyword = {\n                        key: key,\n                        value: value,\n                    };\n                }\n            }\n            else if (keyLocaleData.indexOf('true') > -1) {\n                value = 'true';\n                supportedKeyword = {\n                    key: key,\n                    value: value,\n                };\n            }\n        }\n        var optionsValue = options[key];\n        invariant(optionsValue == null || typeof optionsValue === 'string', \"optionsValue must be a string or undefined\");\n        if (typeof optionsValue === 'string') {\n            var ukey = key.toLowerCase();\n            optionsValue = CanonicalizeUValue(ukey, optionsValue);\n            if (optionsValue === '') {\n                optionsValue = 'true';\n            }\n        }\n        if (optionsValue !== value && keyLocaleData.indexOf(optionsValue) > -1) {\n            value = optionsValue;\n            supportedKeyword = undefined;\n        }\n        if (supportedKeyword) {\n            supportedKeywords.push(supportedKeyword);\n        }\n        result[key] = value;\n    };\n    for (var _i = 0, relevantExtensionKeys_1 = relevantExtensionKeys; _i < relevantExtensionKeys_1.length; _i++) {\n        var key = relevantExtensionKeys_1[_i];\n        _loop_1(key);\n    }\n    var supportedAttributes = [];\n    if (supportedKeywords.length > 0) {\n        supportedAttributes = [];\n        foundLocale = InsertUnicodeExtensionAndCanonicalize(foundLocale, supportedAttributes, supportedKeywords);\n    }\n    result.locale = foundLocale;\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAIO,SAAS,cAAc,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,gBAAgB;IAC1H,IAAI;IACJ,IAAI,UAAU,QAAQ,aAAa;IACnC,IAAI;IACJ,IAAI,YAAY,UAAU;QACtB,IAAI,CAAA,GAAA,gRAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,CAAC,mBAAmB,kBAAkB;IACtE,OACK;QACD,IAAI,CAAA,GAAA,iRAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,IAAI,CAAC,mBAAmB,kBAAkB;IACvE;IACA,IAAI,KAAK,MAAM;QACX,IAAI;YACA,QAAQ;YACR,WAAW;QACf;IACJ;IACA,IAAI,cAAc,EAAE,MAAM;IAC1B,IAAI,kBAAkB,UAAU,CAAC,YAAY;IAC7C,oEAAoE;IACpE,aAAa;IACb,mCAAmC;IACnC,6CAA6C;IAC7C,IAAI;IACJ,IAAI,SAAS;QAAE,QAAQ;QAAM,YAAY;IAAY;IACrD,IAAI;IACJ,IAAI;IACJ,IAAI,EAAE,SAAS,EAAE;QACb,aAAa,CAAA,GAAA,6RAAA,CAAA,6BAA0B,AAAD,EAAE,EAAE,SAAS;QACnD,WAAW,WAAW,QAAQ;IAClC,OACK;QACD,WAAW,EAAE;IACjB;IACA,IAAI,oBAAoB,EAAE;IAC1B,IAAI,UAAU,SAAU,GAAG;QACvB,yDAAyD;QACzD,IAAI,gBAAgB,CAAC,KAAK,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;QACrJ,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,MAAM,OAAO,CAAC,gBAAgB,qBAAqB,MAAM,CAAC,KAAK;QACzE,IAAI,QAAQ,aAAa,CAAC,EAAE;QAC5B,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,UAAU,aAAa,OAAO,UAAU,UAAU;QAC5D,IAAI,mBAAmB,KAAK;QAC5B,IAAI,QAAQ,SAAS,IAAI,CAAC,SAAU,CAAC;YAAI,OAAO,EAAE,GAAG,KAAK;QAAK;QAC/D,IAAI,OAAO;YACP,IAAI,iBAAiB,MAAM,KAAK;YAChC,IAAI,mBAAmB,IAAI;gBACvB,IAAI,cAAc,OAAO,CAAC,kBAAkB,CAAC,GAAG;oBAC5C,QAAQ;oBACR,mBAAmB;wBACf,KAAK;wBACL,OAAO;oBACX;gBACJ;YACJ,OACK,IAAI,cAAc,OAAO,CAAC,UAAU,CAAC,GAAG;gBACzC,QAAQ;gBACR,mBAAmB;oBACf,KAAK;oBACL,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,eAAe,OAAO,CAAC,IAAI;QAC/B,CAAA,GAAA,wQAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,QAAQ,OAAO,iBAAiB,UAAU;QACpE,IAAI,OAAO,iBAAiB,UAAU;YAClC,IAAI,OAAO,IAAI,WAAW;YAC1B,eAAe,CAAA,GAAA,qRAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;YACxC,IAAI,iBAAiB,IAAI;gBACrB,eAAe;YACnB;QACJ;QACA,IAAI,iBAAiB,SAAS,cAAc,OAAO,CAAC,gBAAgB,CAAC,GAAG;YACpE,QAAQ;YACR,mBAAmB;QACvB;QACA,IAAI,kBAAkB;YAClB,kBAAkB,IAAI,CAAC;QAC3B;QACA,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,IAAK,IAAI,KAAK,GAAG,0BAA0B,uBAAuB,KAAK,wBAAwB,MAAM,EAAE,KAAM;QACzG,IAAI,MAAM,uBAAuB,CAAC,GAAG;QACrC,QAAQ;IACZ;IACA,IAAI,sBAAsB,EAAE;IAC5B,IAAI,kBAAkB,MAAM,GAAG,GAAG;QAC9B,sBAAsB,EAAE;QACxB,cAAc,CAAA,GAAA,wSAAA,CAAA,wCAAqC,AAAD,EAAE,aAAa,qBAAqB;IAC1F;IACA,OAAO,MAAM,GAAG;IAChB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4487, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/abstract/LookupSupportedLocales.js"], "sourcesContent": ["import { BestAvailableLocale } from './BestAvailableLocale';\nimport { UNICODE_EXTENSION_SEQUENCE_REGEX } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-lookupsupportedlocales\n * @param availableLocales\n * @param requestedLocales\n */\nexport function LookupSupportedLocales(availableLocales, requestedLocales) {\n    var subset = [];\n    for (var _i = 0, requestedLocales_1 = requestedLocales; _i < requestedLocales_1.length; _i++) {\n        var locale = requestedLocales_1[_i];\n        var noExtensionLocale = locale.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n        var availableLocale = BestAvailableLocale(availableLocales, noExtensionLocale);\n        if (availableLocale) {\n            subset.push(availableLocale);\n        }\n    }\n    return subset;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMO,SAAS,uBAAuB,gBAAgB,EAAE,gBAAgB;IACrE,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,qBAAqB,kBAAkB,KAAK,mBAAmB,MAAM,EAAE,KAAM;QAC1F,IAAI,SAAS,kBAAkB,CAAC,GAAG;QACnC,IAAI,oBAAoB,OAAO,OAAO,CAAC,wQAAA,CAAA,mCAAgC,EAAE;QACzE,IAAI,kBAAkB,CAAA,GAAA,sRAAA,CAAA,sBAAmB,AAAD,EAAE,kBAAkB;QAC5D,IAAI,iBAAiB;YACjB,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 4510, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@formatjs+intl-localematcher@0.5.10/node_modules/@formatjs/intl-localematcher/lib/index.js"], "sourcesContent": ["import { CanonicalizeLocaleList } from './abstract/CanonicalizeLocaleList';\nimport { ResolveLocale } from './abstract/ResolveLocale';\nexport function match(requestedLocales, availableLocales, defaultLocale, opts) {\n    return ResolveLocale(availableLocales, CanonicalizeLocaleList(requestedLocales), {\n        localeMatcher: (opts === null || opts === void 0 ? void 0 : opts.algorithm) || 'best fit',\n    }, [], {}, function () { return defaultLocale; }).locale;\n}\nexport { LookupSupportedLocales } from './abstract/LookupSupportedLocales';\nexport { ResolveLocale } from './abstract/ResolveLocale';\n"], "names": [], "mappings": ";;;AAAA;AACA;AAMA;;;AALO,SAAS,MAAM,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE,IAAI;IACzE,OAAO,CAAA,GAAA,gRAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,yRAAA,CAAA,yBAAsB,AAAD,EAAE,mBAAmB;QAC7E,eAAe,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS,KAAK;IACnF,GAAG,EAAE,EAAE,CAAC,GAAG;QAAc,OAAO;IAAe,GAAG,MAAM;AAC5D", "ignoreList": [0]}}]}