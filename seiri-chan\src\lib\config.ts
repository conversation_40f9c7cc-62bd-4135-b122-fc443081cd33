import { db } from './db';
import type { AppConfig } from '@/types';

// 默认配置
const DEFAULT_CONFIG: AppConfig = {
  // TMDB配置
  tmdbApiKey: process.env.TMDB_API_KEY || '',
  tmdbLanguage: 'zh-CN',
  
  // AI配置
  aiEnabled: process.env.AI_ENABLED === 'true',
  aiProvider: (process.env.AI_PROVIDER as any) || 'openai',
  aiConfidenceThreshold: (process.env.AI_CONFIDENCE_THRESHOLD as any) || 'medium',
  
  // OpenAI配置
  openaiApiKey: process.env.OPENAI_API_KEY || '',
  openaiBaseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
  openaiModel: process.env.OPENAI_MODEL || 'gpt-4o-mini',
  openaiOutputFormat: (process.env.OPENAI_OUTPUT_FORMAT as any) || 'function_calling',
  
  // Gemini配置
  geminiApiKey: process.env.GEMINI_API_KEY || '',
  geminiBaseUrl: process.env.GEMINI_BASE_URL || 'https://generativelanguage.googleapis.com',
  geminiModel: process.env.GEMINI_MODEL || 'gemini-2.5-flash',
  
  // 路径配置
  bangumiPath: process.env.BANGUMI_PATH || '',
  moviePath: process.env.MOVIE_PATH || '',
  animePath: process.env.ANIME_PATH || '',
  animeMoviePath: process.env.ANIME_MOVIE_PATH || '',
  defaultSourcePath: process.env.DEFAULT_SOURCE_PATH || '',
  
  // 文件操作配置
  fileOperationMode: (process.env.FILE_OPERATION_MODE as any) || 'hardlink',
  
  // 其他配置
  defaultLocale: process.env.DEFAULT_LOCALE || 'zh-CN',
  maxConcurrentTasks: parseInt(process.env.MAX_CONCURRENT_TASKS || '3'),
  logLevel: (process.env.LOG_LEVEL as any) || 'info',
  cacheExpiration: parseInt(process.env.CACHE_EXPIRATION || '24'), // 24小时
};

// 配置键映射
const CONFIG_KEYS = {
  // TMDB
  'tmdb.apiKey': 'tmdbApiKey',
  'tmdb.language': 'tmdbLanguage',
  
  // AI
  'ai.enabled': 'aiEnabled',
  'ai.provider': 'aiProvider',
  'ai.confidenceThreshold': 'aiConfidenceThreshold',
  
  // OpenAI
  'openai.apiKey': 'openaiApiKey',
  'openai.baseUrl': 'openaiBaseUrl',
  'openai.model': 'openaiModel',
  'openai.outputFormat': 'openaiOutputFormat',
  
  // Gemini
  'gemini.apiKey': 'geminiApiKey',
  'gemini.baseUrl': 'geminiBaseUrl',
  'gemini.model': 'geminiModel',
  
  // 路径
  'paths.bangumi': 'bangumiPath',
  'paths.movie': 'moviePath',
  'paths.anime': 'animePath',
  'paths.animeMovie': 'animeMoviePath',
  'paths.defaultSource': 'defaultSourcePath',
  
  // 文件操作
  'file.operationMode': 'fileOperationMode',
  
  // 其他
  'general.defaultLocale': 'defaultLocale',
  'general.maxConcurrentTasks': 'maxConcurrentTasks',
  'general.logLevel': 'logLevel',
  'general.cacheExpiration': 'cacheExpiration',
} as const;

class ConfigManager {
  private cache: Partial<AppConfig> = {};
  private initialized = false;

  async initialize() {
    if (this.initialized) return;
    
    try {
      // 从数据库加载配置
      const configs = await db.config.findMany();
      
      // 合并配置
      for (const config of configs) {
        const configKey = Object.keys(CONFIG_KEYS).find(
          key => CONFIG_KEYS[key as keyof typeof CONFIG_KEYS] === config.key
        );
        
        if (configKey) {
          this.cache[config.key as keyof AppConfig] = config.value as any;
        }
      }
      
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize config:', error);
      // 使用默认配置
      this.cache = { ...DEFAULT_CONFIG };
      this.initialized = true;
    }
  }

  async get<K extends keyof AppConfig>(key: K): Promise<AppConfig[K]> {
    await this.initialize();
    return (this.cache[key] ?? DEFAULT_CONFIG[key]) as AppConfig[K];
  }

  async getAll(): Promise<AppConfig> {
    await this.initialize();
    return { ...DEFAULT_CONFIG, ...this.cache };
  }

  async set<K extends keyof AppConfig>(key: K, value: AppConfig[K]): Promise<void> {
    await this.initialize();
    
    try {
      await db.config.upsert({
        where: { key: key as string },
        update: { value: value as any },
        create: { key: key as string, value: value as any },
      });
      
      this.cache[key] = value;
    } catch (error) {
      console.error(`Failed to set config ${key}:`, error);
      throw error;
    }
  }

  async setMany(configs: Partial<AppConfig>): Promise<void> {
    await this.initialize();
    
    try {
      const operations = Object.entries(configs).map(([key, value]) =>
        db.config.upsert({
          where: { key },
          update: { value: value as any },
          create: { key, value: value as any },
        })
      );
      
      await Promise.all(operations);
      
      // 更新缓存
      Object.assign(this.cache, configs);
    } catch (error) {
      console.error('Failed to set configs:', error);
      throw error;
    }
  }

  async reset(): Promise<void> {
    try {
      await db.config.deleteMany();
      this.cache = {};
      this.initialized = false;
    } catch (error) {
      console.error('Failed to reset config:', error);
      throw error;
    }
  }

  // 验证配置
  async validate(): Promise<{ isValid: boolean; errors: string[] }> {
    const config = await this.getAll();
    const errors: string[] = [];

    // 验证必需的配置
    if (!config.tmdbApiKey) {
      errors.push('TMDB API Key is required');
    }

    if (config.aiEnabled) {
      if (config.aiProvider === 'openai' && !config.openaiApiKey) {
        errors.push('OpenAI API Key is required when AI is enabled');
      }
      if (config.aiProvider === 'gemini' && !config.geminiApiKey) {
        errors.push('Gemini API Key is required when AI is enabled');
      }
    }

    // 验证路径配置
    const paths = [
      config.bangumiPath,
      config.moviePath,
      config.animePath,
      config.animeMoviePath,
    ];

    if (paths.every(path => !path)) {
      errors.push('At least one output path must be configured');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // 测试配置
  async testConfig(type: 'tmdb' | 'openai' | 'gemini'): Promise<{ success: boolean; message: string }> {
    const config = await this.getAll();

    try {
      switch (type) {
        case 'tmdb':
          if (!config.tmdbApiKey) {
            return { success: false, message: 'TMDB API Key not configured' };
          }
          // TODO: 实际测试TMDB API
          return { success: true, message: 'TMDB API connection successful' };

        case 'openai':
          if (!config.openaiApiKey) {
            return { success: false, message: 'OpenAI API Key not configured' };
          }
          // TODO: 实际测试OpenAI API
          return { success: true, message: 'OpenAI API connection successful' };

        case 'gemini':
          if (!config.geminiApiKey) {
            return { success: false, message: 'Gemini API Key not configured' };
          }
          // TODO: 实际测试Gemini API
          return { success: true, message: 'Gemini API connection successful' };

        default:
          return { success: false, message: 'Unknown config type' };
      }
    } catch (error) {
      return {
        success: false,
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }
}

// 单例实例
export const configManager = new ConfigManager();

// 便捷函数
export const getConfig = <K extends keyof AppConfig>(key: K) => configManager.get(key);
export const getAllConfig = () => configManager.getAll();
export const setConfig = <K extends keyof AppConfig>(key: K, value: AppConfig[K]) => 
  configManager.set(key, value);
export const setManyConfig = (configs: Partial<AppConfig>) => configManager.setMany(configs);
