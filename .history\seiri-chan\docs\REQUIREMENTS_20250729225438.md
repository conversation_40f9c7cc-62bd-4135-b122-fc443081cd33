# Seiri-chan 需求分析文档

## 1. 项目概述

Seiri-chan 是一个基于 Next.js 的动漫BD自动整理工具，旨在根据 TMDB 元数据智能识别本地视频文件，并将其重命名为标准的 SxxExx 格式。项目结合传统规则引擎和 AI 增强识别，提供高精度的文件整理服务。

### 1.1 项目目标
- 自动化整理动漫、电视剧、电影文件
- 提供直观易用的 Web 界面
- 支持多语言国际化
- 实现高可靠性的文件操作
- 提供完善的任务管理和状态跟踪

### 1.2 目标用户
- 动漫爱好者和收藏者
- 媒体服务器管理员
- 需要批量整理视频文件的用户

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 文件识别与整理
- **传统识别引擎**：基于规则的文件名解析和匹配
- **AI增强识别**：使用大语言模型处理复杂和模糊的匹配情况
- **多媒体类型支持**：电视剧、电影、动画、动画电影
- **标准化重命名**：输出符合 SxxExx 格式的文件名

#### 2.1.2 文件操作
- **多种操作模式**：硬链接、软链接、复制、移动
- **容错机制**：乐观处理策略，失败时记录并继续
- **回退机制**：移动操作支持失败回退
- **关联文件处理**：同时处理字幕、字体等关联文件

#### 2.1.3 任务管理
- **实时任务创建**：添加任务后立即显示在列表中
- **状态跟踪**：pending、processing、completed、failed、cancelled
- **批量处理**：支持一次性添加多个任务
- **深度扫描**：支持指定深度的递归扫描
- **任务历史**：完整的任务执行记录

### 2.2 增强功能

#### 2.2.1 智能识别优化
- **结构化数据提供**：为 AI 提供格式化的本地视频数据
- **混合识别策略**：传统识别 + AI 增强，而非完全替代
- **标签预处理**：使用传统识别的标签对文件进行预筛选
- **置信度评估**：AI 分析结果包含置信度评级

#### 2.2.2 特殊文件处理
- **剧场版识别**：识别被 TMDB 单独列为电影的剧场版文件
- **预告片整理**：区分集预告和季预告，整理到对应目录
- **字幕文件处理**：处理字幕组标签不一致的问题
- **字体文件收集**：自动解压并收集字体文件

#### 2.2.3 高级任务功能
- **多路径选择**：支持文件夹多选
- **批量任务创建**：以父路径批量创建子任务
- **特别篇处理**：支持多个视频映射到相同季号集号
- **任务导出分享**：支持导出任务数据用于反馈提交和测试复现

### 2.3 用户界面需求

#### 2.3.1 基础界面
- **响应式设计**：适配桌面和移动设备
- **主题系统**：支持明暗主题切换
- **国际化支持**：中文、英文、日文
- **直观导航**：清晰的页面结构和导航

#### 2.3.2 任务管理界面
- **任务列表**：展示所有任务及其状态
- **任务详情**：详细的任务信息和处理结果
- **批量操作**：支持批量删除、重试等操作
- **实时更新**：任务状态实时刷新

#### 2.3.3 配置管理界面
- **分类配置**：TMDB、AI、路径、常规设置
- **配置验证**：实时验证配置的有效性
- **配置测试**：提供配置测试功能
- **导入导出**：支持配置的备份和恢复

#### 2.3.4 文件浏览器
- **路径选择**：可视化的文件夹选择
- **多选支持**：支持选择多个路径
- **路径预览**：显示选中路径的基本信息
- **权限检查**：检查路径的读写权限

## 3. 非功能需求

### 3.1 性能需求
- **响应时间**：界面操作响应时间 < 200ms
- **并发处理**：支持多个任务并发执行
- **内存使用**：合理控制内存占用
- **文件处理速度**：高效的文件操作

### 3.2 可靠性需求
- **错误处理**：完善的错误捕获和处理机制
- **数据一致性**：确保数据库和文件系统的一致性
- **故障恢复**：支持任务中断后的恢复
- **日志记录**：详细的操作日志

### 3.3 安全性需求
- **路径验证**：防止路径遍历攻击
- **身份验证**：进入控制台前需要使用Token登录
- **API 安全**：API 密钥的安全存储和使用
- **输入验证**：严格的用户输入验证

### 3.4 可维护性需求
- **代码结构**：清晰的代码组织和模块化
- **文档完善**：完整的 API 文档和用户手册
- **测试覆盖**：充分的单元测试和集成测试
- **部署简化**：简单的部署和配置流程

## 4. 技术约束

### 4.1 技术栈限制
- **前端框架**：Next.js 14+
- **包管理器**：pnpm
- **数据库**：SQLite（开发）/ PostgreSQL（生产）
- **ORM**：Prisma
- **UI 框架**：Tailwind CSS + Radix UI

### 4.2 外部依赖
- **TMDB API**：获取媒体元数据(使用tmdb-js-node库)
- **AI 服务**：OpenAI API、Google Gemini API
- **文件系统**：需要服务器端文件系统访问权限

### 4.3 兼容性要求
- **浏览器支持**：现代浏览器（Chrome 90+、Firefox 88+、Safari 14+）
- **Node.js 版本**：Node.js 18+
- **操作系统**：跨平台支持（Windows、macOS、Linux）

## 5. 业务规则

### 5.1 文件识别规则
- 优先使用传统识别，AI 作为增强和补充
- 置信度低于阈值时不采用 AI 结果
- 支持用户手动调整识别结果

### 5.2 文件操作规则
- 硬链接和软链接失败时记录错误继续执行
- 复制操作失败时记录错误继续执行
- 移动操作失败时必须回退已处理的文件
- 仅在处理第0季剧集时，允许多个视频文件映射到完全相同的季号和集号，并进行相应的重命名操作。

### 5.3 任务管理规则
- 任务创建后立即显示，状态为 pending
- 同一路径不能同时存在多个处理中的任务
- 失败的任务可以重试，成功的任务可以重新执行

## 6. 数据需求

### 6.1 配置数据
- TMDB API 配置
- AI 提供商配置
- 文件路径配置
- 用户偏好设置

### 6.2 任务数据
- 任务基本信息
- 源文件列表
- TMDB 元数据
- 文件映射关系
- 处理结果

### 6.3 缓存数据
- TMDB API 响应缓存
- AI 分析结果缓存
- 测试用例数据

### 6.4 日志数据
- 任务执行日志
- 错误日志
- 系统操作日志
