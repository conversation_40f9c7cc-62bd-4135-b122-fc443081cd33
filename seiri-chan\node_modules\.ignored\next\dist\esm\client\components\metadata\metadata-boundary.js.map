{"version": 3, "sources": ["../../../../src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary"], "mappings": "AAAA;AAEA,SACEA,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,QACf,2CAA0C;AAEjD,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACH,uBAAuB,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCI,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACH,uBAAuB,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCG,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACF,qBAAqB,EAAE,SAAU,KAIjC;QAJiC,IAAA,EAChCE,QAAQ,EAGT,GAJiC;QAKhC,OAAOA;IACT;AACF;AAEA,OAAO,MAAMC,mBACX,gFAAgF;AAChF,4DAA4D;AAC5DF,SAAS,CAACH,uBAAuBM,KAAK,CAAC,GAAoC,CAAA;AAE7E,OAAO,MAAMC,mBACX,gFAAgF;AAChF,4DAA4D;AAC5DJ,SAAS,CAACF,uBAAuBK,KAAK,CAAC,GAAoC,CAAA;AAE7E,OAAO,MAAME,iBACX,gFAAgF;AAChF,4DAA4D;AAC5DL,SAAS,CAACD,qBAAqBI,KAAK,CAAC,GAAkC,CAAA", "ignoreList": [0]}