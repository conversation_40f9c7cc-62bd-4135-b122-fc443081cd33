{"version": 3, "sources": ["../../../../src/build/webpack/plugins/css-chunking-plugin.ts"], "sourcesContent": ["import type { Chunk, Compiler, Module } from 'webpack'\n\nconst PLUGIN_NAME = 'CssChunkingPlugin'\n\n/**\n * Merge chunks until they are bigger than the target size.\n */\nconst MIN_CSS_CHUNK_SIZE = 30 * 1024\n/**\n * Avoid merging chunks when they would be bigger than this size.\n */\nconst MAX_CSS_CHUNK_SIZE = 100 * 1024\n\nfunction isGlobalCss(module: Module) {\n  return !/\\.module\\.(css|scss|sass)$/.test(module.nameForCondition() || '')\n}\n\ntype ChunkState = {\n  chunk: Chunk\n  modules: Module[]\n  order: number\n  requests: number\n}\n\nexport class CssChunkingPlugin {\n  private strict: boolean\n  constructor(strict: boolean) {\n    this.strict = strict\n  }\n\n  public apply(compiler: Compiler) {\n    const strict = this.strict\n    const summary = !!process.env.CSS_CHUNKING_SUMMARY\n    compiler.hooks.thisCompilation.tap(PLUGIN_NAME, (compilation) => {\n      let once = false\n      compilation.hooks.optimizeChunks.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: 5,\n        },\n        () => {\n          if (once) {\n            return\n          }\n          once = true\n          const chunkGraph = compilation.chunkGraph\n          let changed: undefined | true = undefined\n\n          const chunkStates = new Map<Chunk, ChunkState>()\n          const chunkStatesByModule = new Map<Module, Map<ChunkState, number>>()\n\n          // Collect all css modules in chunks and the execpted order of them\n          for (const chunk of compilation.chunks) {\n            if (chunk.name?.startsWith('pages/')) continue\n            const modules = []\n            for (const module of chunkGraph.getChunkModulesIterable(chunk)) {\n              if (!module.type?.startsWith('css')) continue\n              modules.push(module)\n            }\n            if (!modules.length) continue\n            const chunkState = {\n              chunk,\n              modules,\n              order: 0,\n              requests: modules.length,\n            }\n            chunkStates.set(chunk, chunkState)\n            for (let i = 0; i < modules.length; i++) {\n              const module = modules[i]\n              let moduleChunkStates = chunkStatesByModule.get(module)\n              if (!moduleChunkStates) {\n                moduleChunkStates = new Map()\n                chunkStatesByModule.set(module, moduleChunkStates)\n              }\n              moduleChunkStates.set(chunkState, i)\n              chunkStatesByModule.set(module, moduleChunkStates)\n            }\n          }\n\n          // Sort modules by their index sum\n          const orderedModules: { module: Module; sum: number }[] = []\n\n          for (const [module, moduleChunkStates] of chunkStatesByModule) {\n            let sum = 0\n            for (const i of moduleChunkStates.values()) {\n              sum += i\n            }\n            orderedModules.push({ module, sum })\n          }\n\n          orderedModules.sort((a, b) => a.sum - b.sum)\n\n          // A queue of modules that still need to be processed\n          const remainingModules = new Set(\n            orderedModules.map(({ module }) => module)\n          )\n\n          // In loose mode we guess the dependents of modules from the order\n          // assuming that when a module is a dependency of another module\n          // it will always appear before it in every chunk.\n          const allDependents = new Map<Module, Set<Module>>()\n\n          if (!this.strict) {\n            for (const b of remainingModules) {\n              const dependent = new Set<Module>()\n              loop: for (const a of remainingModules) {\n                if (a === b) continue\n                // check if a depends on b\n                for (const [chunkState, ia] of chunkStatesByModule.get(a)!) {\n                  const bChunkStates = chunkStatesByModule.get(b)!\n                  const ib = bChunkStates.get(chunkState)\n                  if (ib === undefined) {\n                    // If a would depend on b, it would be included in that chunk group too\n                    continue loop\n                  }\n                  if (ib > ia) {\n                    // If a would depend on b, b would be before a in order\n                    continue loop\n                  }\n                }\n                dependent.add(a)\n              }\n              if (dependent.size > 0) allDependents.set(b, dependent)\n            }\n          }\n\n          // Stores the new chunk for every module\n          const newChunksByModule = new Map<Module, Chunk>()\n\n          // Process through all modules\n          for (const startModule of remainingModules) {\n            let globalCssMode = isGlobalCss(startModule)\n\n            // The current position of processing in all selected chunks\n            let allChunkStates = new Map(chunkStatesByModule.get(startModule)!)\n\n            // The list of modules that goes into the new chunk\n            const newChunkModules = new Set([startModule])\n\n            // The current size of the new chunk\n            let currentSize = startModule.size()\n\n            // A pool of potential modules where the next module is selected from.\n            // It's filled from the next module of the selected modules in every chunk.\n            // It also keeps some metadata to improve performance [size, chunkStates].\n            const potentialNextModules = new Map<\n              Module,\n              [number, Map<ChunkState, number>]\n            >()\n            for (const [chunkState, i] of allChunkStates) {\n              const nextModule = chunkState.modules[i + 1]\n              if (nextModule && remainingModules.has(nextModule)) {\n                potentialNextModules.set(nextModule, [\n                  nextModule.size(),\n                  chunkStatesByModule.get(nextModule)!,\n                ])\n              }\n            }\n\n            // Try to add modules to the chunk until a break condition is met\n            let cont\n            do {\n              cont = false\n              // We try to select a module that reduces request count and\n              // has the highest number of requests\n              const orderedPotentialNextModules = []\n              for (const [\n                nextModule,\n                [size, nextChunkStates],\n              ] of potentialNextModules) {\n                let maxRequests = 0\n                for (const chunkState of nextChunkStates.keys()) {\n                  // There is always some overlap\n                  if (allChunkStates.has(chunkState)) {\n                    maxRequests = Math.max(maxRequests, chunkState.requests)\n                  }\n                }\n\n                orderedPotentialNextModules.push([\n                  nextModule,\n                  size,\n                  nextChunkStates,\n                  maxRequests,\n                ] as const)\n              }\n              orderedPotentialNextModules.sort(\n                (a, b) =>\n                  b[3] - a[3] ||\n                  (a[0].identifier() < b[0].identifier() ? -1 : 1)\n              )\n\n              // Try every potential module\n              loop: for (const [\n                nextModule,\n                size,\n                nextChunkStates,\n              ] of orderedPotentialNextModules) {\n                if (currentSize + size > MAX_CSS_CHUNK_SIZE) {\n                  // Chunk would be too large\n                  continue\n                }\n                if (!strict) {\n                  // In loose mode we only check if the dependencies are not violated\n                  const dependent = allDependents.get(nextModule)\n                  if (dependent) {\n                    for (const dep of dependent) {\n                      if (newChunkModules.has(dep)) {\n                        // A dependent of the module is already in the chunk, which would violate the order\n                        continue loop\n                      }\n                    }\n                  }\n                } else {\n                  // In strict mode we check that none of the order in any chunk is changed by adding the module\n                  for (const [chunkState, i] of nextChunkStates) {\n                    const prevState = allChunkStates.get(chunkState)\n                    if (prevState === undefined) {\n                      // New chunk group, can add it, but should we?\n                      // We only add that if below min size\n                      if (currentSize < MIN_CSS_CHUNK_SIZE) {\n                        continue\n                      } else {\n                        continue loop\n                      }\n                    } else if (prevState + 1 === i) {\n                      // Existing chunk group, order fits\n                      continue\n                    } else {\n                      // Existing chunk group, there is something in between or order is reversed\n                      continue loop\n                    }\n                  }\n                }\n\n                // Global CSS must not leak into unrelated chunks\n                const nextIsGlobalCss = isGlobalCss(nextModule)\n                if (nextIsGlobalCss && globalCssMode) {\n                  if (allChunkStates.size !== nextChunkStates.size) {\n                    // Fast check\n                    continue\n                  }\n                }\n                if (globalCssMode) {\n                  for (const chunkState of nextChunkStates.keys()) {\n                    if (!allChunkStates.has(chunkState)) {\n                      // Global CSS would leak into chunkState\n                      continue loop\n                    }\n                  }\n                }\n                if (nextIsGlobalCss) {\n                  for (const chunkState of allChunkStates.keys()) {\n                    if (!nextChunkStates.has(chunkState)) {\n                      // Global CSS would leak into chunkState\n                      continue loop\n                    }\n                  }\n                }\n                potentialNextModules.delete(nextModule)\n                currentSize += size\n                if (nextIsGlobalCss) {\n                  globalCssMode = true\n                }\n                for (const [chunkState, i] of nextChunkStates) {\n                  if (allChunkStates.has(chunkState)) {\n                    // This reduces the request count of the chunk group\n                    chunkState.requests--\n                  }\n                  allChunkStates.set(chunkState, i)\n                  const newNextModule = chunkState.modules[i + 1]\n                  if (\n                    newNextModule &&\n                    remainingModules.has(newNextModule) &&\n                    !newChunkModules.has(newNextModule)\n                  ) {\n                    potentialNextModules.set(newNextModule, [\n                      newNextModule.size(),\n                      chunkStatesByModule.get(newNextModule)!,\n                    ])\n                  }\n                }\n                newChunkModules.add(nextModule)\n                cont = true\n                break\n              }\n            } while (cont)\n            const newChunk = compilation.addChunk()\n            newChunk.preventIntegration = true\n            newChunk.idNameHints.add('css')\n            for (const module of newChunkModules) {\n              remainingModules.delete(module)\n              chunkGraph.connectChunkAndModule(newChunk, module)\n              newChunksByModule.set(module, newChunk)\n            }\n            changed = true\n          }\n\n          for (const { chunk, modules } of chunkStates.values()) {\n            const chunks = new Set()\n            for (const module of modules) {\n              const newChunk = newChunksByModule.get(module)\n              if (newChunk) {\n                chunkGraph.disconnectChunkAndModule(chunk, module)\n                if (chunks.has(newChunk)) continue\n                chunks.add(newChunk)\n                chunk.split(newChunk)\n              }\n            }\n          }\n\n          if (summary) {\n            console.log('Top 20 chunks by request count:')\n            const orderedChunkStates = [...chunkStates.values()]\n            orderedChunkStates.sort((a, b) => b.requests - a.requests)\n            for (const { chunk, modules, requests } of orderedChunkStates.slice(\n              0,\n              20\n            )) {\n              console.log(\n                `- ${requests} requests for ${chunk.name} (has ${modules.length} modules)`\n              )\n            }\n          }\n\n          return changed\n        }\n      )\n    })\n  }\n}\n"], "names": ["PLUGIN_NAME", "MIN_CSS_CHUNK_SIZE", "MAX_CSS_CHUNK_SIZE", "isGlobalCss", "module", "test", "nameForCondition", "CssChunkingPlugin", "constructor", "strict", "apply", "compiler", "summary", "process", "env", "CSS_CHUNKING_SUMMARY", "hooks", "thisCompilation", "tap", "compilation", "once", "optimizeChunks", "name", "stage", "chunkGraph", "changed", "undefined", "chunkStates", "Map", "chunkStatesByModule", "chunk", "chunks", "startsWith", "modules", "getChunkModulesIterable", "type", "push", "length", "chunkState", "order", "requests", "set", "i", "moduleChunkStates", "get", "orderedModules", "sum", "values", "sort", "a", "b", "remainingModules", "Set", "map", "allDependents", "dependent", "loop", "ia", "bChunkStates", "ib", "add", "size", "newChunksByModule", "startModule", "globalCssMode", "allChunkStates", "newChunkModules", "currentSize", "potentialNextModules", "nextModule", "has", "cont", "orderedPotentialNextModules", "nextChunkStates", "maxRequests", "keys", "Math", "max", "identifier", "dep", "prevState", "nextIsGlobalCss", "delete", "newNextModule", "newChunk", "addChunk", "preventIntegration", "idNameHints", "connectChunkAndModule", "disconnectChunkAndModule", "split", "console", "log", "orderedChunkStates", "slice"], "mappings": "AAEA,MAAMA,cAAc;AAEpB;;CAEC,GACD,MAAMC,qBAAqB,KAAK;AAChC;;CAEC,GACD,MAAMC,qBAAqB,MAAM;AAEjC,SAASC,YAAYC,MAAc;IACjC,OAAO,CAAC,6BAA6BC,IAAI,CAACD,OAAOE,gBAAgB,MAAM;AACzE;AASA,OAAO,MAAMC;IAEXC,YAAYC,MAAe,CAAE;QAC3B,IAAI,CAACA,MAAM,GAAGA;IAChB;IAEOC,MAAMC,QAAkB,EAAE;QAC/B,MAAMF,SAAS,IAAI,CAACA,MAAM;QAC1B,MAAMG,UAAU,CAAC,CAACC,QAAQC,GAAG,CAACC,oBAAoB;QAClDJ,SAASK,KAAK,CAACC,eAAe,CAACC,GAAG,CAAClB,aAAa,CAACmB;YAC/C,IAAIC,OAAO;YACXD,YAAYH,KAAK,CAACK,cAAc,CAACH,GAAG,CAClC;gBACEI,MAAMtB;gBACNuB,OAAO;YACT,GACA;gBACE,IAAIH,MAAM;oBACR;gBACF;gBACAA,OAAO;gBACP,MAAMI,aAAaL,YAAYK,UAAU;gBACzC,IAAIC,UAA4BC;gBAEhC,MAAMC,cAAc,IAAIC;gBACxB,MAAMC,sBAAsB,IAAID;gBAEhC,mEAAmE;gBACnE,KAAK,MAAME,SAASX,YAAYY,MAAM,CAAE;wBAClCD;oBAAJ,KAAIA,cAAAA,MAAMR,IAAI,qBAAVQ,YAAYE,UAAU,CAAC,WAAW;oBACtC,MAAMC,UAAU,EAAE;oBAClB,KAAK,MAAM7B,UAAUoB,WAAWU,uBAAuB,CAACJ,OAAQ;4BACzD1B;wBAAL,IAAI,GAACA,eAAAA,OAAO+B,IAAI,qBAAX/B,aAAa4B,UAAU,CAAC,SAAQ;wBACrCC,QAAQG,IAAI,CAAChC;oBACf;oBACA,IAAI,CAAC6B,QAAQI,MAAM,EAAE;oBACrB,MAAMC,aAAa;wBACjBR;wBACAG;wBACAM,OAAO;wBACPC,UAAUP,QAAQI,MAAM;oBAC1B;oBACAV,YAAYc,GAAG,CAACX,OAAOQ;oBACvB,IAAK,IAAII,IAAI,GAAGA,IAAIT,QAAQI,MAAM,EAAEK,IAAK;wBACvC,MAAMtC,SAAS6B,OAAO,CAACS,EAAE;wBACzB,IAAIC,oBAAoBd,oBAAoBe,GAAG,CAACxC;wBAChD,IAAI,CAACuC,mBAAmB;4BACtBA,oBAAoB,IAAIf;4BACxBC,oBAAoBY,GAAG,CAACrC,QAAQuC;wBAClC;wBACAA,kBAAkBF,GAAG,CAACH,YAAYI;wBAClCb,oBAAoBY,GAAG,CAACrC,QAAQuC;oBAClC;gBACF;gBAEA,kCAAkC;gBAClC,MAAME,iBAAoD,EAAE;gBAE5D,KAAK,MAAM,CAACzC,QAAQuC,kBAAkB,IAAId,oBAAqB;oBAC7D,IAAIiB,MAAM;oBACV,KAAK,MAAMJ,KAAKC,kBAAkBI,MAAM,GAAI;wBAC1CD,OAAOJ;oBACT;oBACAG,eAAeT,IAAI,CAAC;wBAAEhC;wBAAQ0C;oBAAI;gBACpC;gBAEAD,eAAeG,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEH,GAAG,GAAGI,EAAEJ,GAAG;gBAE3C,qDAAqD;gBACrD,MAAMK,mBAAmB,IAAIC,IAC3BP,eAAeQ,GAAG,CAAC,CAAC,EAAEjD,MAAM,EAAE,GAAKA;gBAGrC,kEAAkE;gBAClE,gEAAgE;gBAChE,kDAAkD;gBAClD,MAAMkD,gBAAgB,IAAI1B;gBAE1B,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;oBAChB,KAAK,MAAMyC,KAAKC,iBAAkB;wBAChC,MAAMI,YAAY,IAAIH;wBACtBI,MAAM,KAAK,MAAMP,KAAKE,iBAAkB;4BACtC,IAAIF,MAAMC,GAAG;4BACb,0BAA0B;4BAC1B,KAAK,MAAM,CAACZ,YAAYmB,GAAG,IAAI5B,oBAAoBe,GAAG,CAACK,GAAK;gCAC1D,MAAMS,eAAe7B,oBAAoBe,GAAG,CAACM;gCAC7C,MAAMS,KAAKD,aAAad,GAAG,CAACN;gCAC5B,IAAIqB,OAAOjC,WAAW;oCAEpB,SAAS8B;gCACX;gCACA,IAAIG,KAAKF,IAAI;oCAEX,SAASD;gCACX;4BACF;4BACAD,UAAUK,GAAG,CAACX;wBAChB;wBACA,IAAIM,UAAUM,IAAI,GAAG,GAAGP,cAAcb,GAAG,CAACS,GAAGK;oBAC/C;gBACF;gBAEA,wCAAwC;gBACxC,MAAMO,oBAAoB,IAAIlC;gBAE9B,8BAA8B;gBAC9B,KAAK,MAAMmC,eAAeZ,iBAAkB;oBAC1C,IAAIa,gBAAgB7D,YAAY4D;oBAEhC,4DAA4D;oBAC5D,IAAIE,iBAAiB,IAAIrC,IAAIC,oBAAoBe,GAAG,CAACmB;oBAErD,mDAAmD;oBACnD,MAAMG,kBAAkB,IAAId,IAAI;wBAACW;qBAAY;oBAE7C,oCAAoC;oBACpC,IAAII,cAAcJ,YAAYF,IAAI;oBAElC,sEAAsE;oBACtE,2EAA2E;oBAC3E,0EAA0E;oBAC1E,MAAMO,uBAAuB,IAAIxC;oBAIjC,KAAK,MAAM,CAACU,YAAYI,EAAE,IAAIuB,eAAgB;wBAC5C,MAAMI,aAAa/B,WAAWL,OAAO,CAACS,IAAI,EAAE;wBAC5C,IAAI2B,cAAclB,iBAAiBmB,GAAG,CAACD,aAAa;4BAClDD,qBAAqB3B,GAAG,CAAC4B,YAAY;gCACnCA,WAAWR,IAAI;gCACfhC,oBAAoBe,GAAG,CAACyB;6BACzB;wBACH;oBACF;oBAEA,iEAAiE;oBACjE,IAAIE;oBACJ,GAAG;wBACDA,OAAO;wBACP,2DAA2D;wBAC3D,qCAAqC;wBACrC,MAAMC,8BAA8B,EAAE;wBACtC,KAAK,MAAM,CACTH,YACA,CAACR,MAAMY,gBAAgB,CACxB,IAAIL,qBAAsB;4BACzB,IAAIM,cAAc;4BAClB,KAAK,MAAMpC,cAAcmC,gBAAgBE,IAAI,GAAI;gCAC/C,+BAA+B;gCAC/B,IAAIV,eAAeK,GAAG,CAAChC,aAAa;oCAClCoC,cAAcE,KAAKC,GAAG,CAACH,aAAapC,WAAWE,QAAQ;gCACzD;4BACF;4BAEAgC,4BAA4BpC,IAAI,CAAC;gCAC/BiC;gCACAR;gCACAY;gCACAC;6BACD;wBACH;wBACAF,4BAA4BxB,IAAI,CAC9B,CAACC,GAAGC,IACFA,CAAC,CAAC,EAAE,GAAGD,CAAC,CAAC,EAAE,IACVA,CAAAA,CAAC,CAAC,EAAE,CAAC6B,UAAU,KAAK5B,CAAC,CAAC,EAAE,CAAC4B,UAAU,KAAK,CAAC,IAAI,CAAA;wBAGlD,6BAA6B;wBAC7BtB,MAAM,KAAK,MAAM,CACfa,YACAR,MACAY,gBACD,IAAID,4BAA6B;4BAChC,IAAIL,cAAcN,OAAO3D,oBAAoB;gCAE3C;4BACF;4BACA,IAAI,CAACO,QAAQ;gCACX,mEAAmE;gCACnE,MAAM8C,YAAYD,cAAcV,GAAG,CAACyB;gCACpC,IAAId,WAAW;oCACb,KAAK,MAAMwB,OAAOxB,UAAW;wCAC3B,IAAIW,gBAAgBI,GAAG,CAACS,MAAM;4CAE5B,SAASvB;wCACX;oCACF;gCACF;4BACF,OAAO;gCACL,8FAA8F;gCAC9F,KAAK,MAAM,CAAClB,YAAYI,EAAE,IAAI+B,gBAAiB;oCAC7C,MAAMO,YAAYf,eAAerB,GAAG,CAACN;oCACrC,IAAI0C,cAActD,WAAW;wCAC3B,8CAA8C;wCAC9C,qCAAqC;wCACrC,IAAIyC,cAAclE,oBAAoB;4CACpC;wCACF,OAAO;4CACL,SAASuD;wCACX;oCACF,OAAO,IAAIwB,YAAY,MAAMtC,GAAG;wCAE9B;oCACF,OAAO;wCAEL,SAASc;oCACX;gCACF;4BACF;4BAEA,iDAAiD;4BACjD,MAAMyB,kBAAkB9E,YAAYkE;4BACpC,IAAIY,mBAAmBjB,eAAe;gCACpC,IAAIC,eAAeJ,IAAI,KAAKY,gBAAgBZ,IAAI,EAAE;oCAEhD;gCACF;4BACF;4BACA,IAAIG,eAAe;gCACjB,KAAK,MAAM1B,cAAcmC,gBAAgBE,IAAI,GAAI;oCAC/C,IAAI,CAACV,eAAeK,GAAG,CAAChC,aAAa;wCAEnC,SAASkB;oCACX;gCACF;4BACF;4BACA,IAAIyB,iBAAiB;gCACnB,KAAK,MAAM3C,cAAc2B,eAAeU,IAAI,GAAI;oCAC9C,IAAI,CAACF,gBAAgBH,GAAG,CAAChC,aAAa;wCAEpC,SAASkB;oCACX;gCACF;4BACF;4BACAY,qBAAqBc,MAAM,CAACb;4BAC5BF,eAAeN;4BACf,IAAIoB,iBAAiB;gCACnBjB,gBAAgB;4BAClB;4BACA,KAAK,MAAM,CAAC1B,YAAYI,EAAE,IAAI+B,gBAAiB;gCAC7C,IAAIR,eAAeK,GAAG,CAAChC,aAAa;oCAClC,oDAAoD;oCACpDA,WAAWE,QAAQ;gCACrB;gCACAyB,eAAexB,GAAG,CAACH,YAAYI;gCAC/B,MAAMyC,gBAAgB7C,WAAWL,OAAO,CAACS,IAAI,EAAE;gCAC/C,IACEyC,iBACAhC,iBAAiBmB,GAAG,CAACa,kBACrB,CAACjB,gBAAgBI,GAAG,CAACa,gBACrB;oCACAf,qBAAqB3B,GAAG,CAAC0C,eAAe;wCACtCA,cAActB,IAAI;wCAClBhC,oBAAoBe,GAAG,CAACuC;qCACzB;gCACH;4BACF;4BACAjB,gBAAgBN,GAAG,CAACS;4BACpBE,OAAO;4BACP;wBACF;oBACF,QAASA,MAAK;oBACd,MAAMa,WAAWjE,YAAYkE,QAAQ;oBACrCD,SAASE,kBAAkB,GAAG;oBAC9BF,SAASG,WAAW,CAAC3B,GAAG,CAAC;oBACzB,KAAK,MAAMxD,UAAU8D,gBAAiB;wBACpCf,iBAAiB+B,MAAM,CAAC9E;wBACxBoB,WAAWgE,qBAAqB,CAACJ,UAAUhF;wBAC3C0D,kBAAkBrB,GAAG,CAACrC,QAAQgF;oBAChC;oBACA3D,UAAU;gBACZ;gBAEA,KAAK,MAAM,EAAEK,KAAK,EAAEG,OAAO,EAAE,IAAIN,YAAYoB,MAAM,GAAI;oBACrD,MAAMhB,SAAS,IAAIqB;oBACnB,KAAK,MAAMhD,UAAU6B,QAAS;wBAC5B,MAAMmD,WAAWtB,kBAAkBlB,GAAG,CAACxC;wBACvC,IAAIgF,UAAU;4BACZ5D,WAAWiE,wBAAwB,CAAC3D,OAAO1B;4BAC3C,IAAI2B,OAAOuC,GAAG,CAACc,WAAW;4BAC1BrD,OAAO6B,GAAG,CAACwB;4BACXtD,MAAM4D,KAAK,CAACN;wBACd;oBACF;gBACF;gBAEA,IAAIxE,SAAS;oBACX+E,QAAQC,GAAG,CAAC;oBACZ,MAAMC,qBAAqB;2BAAIlE,YAAYoB,MAAM;qBAAG;oBACpD8C,mBAAmB7C,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEV,QAAQ,GAAGS,EAAET,QAAQ;oBACzD,KAAK,MAAM,EAAEV,KAAK,EAAEG,OAAO,EAAEO,QAAQ,EAAE,IAAIqD,mBAAmBC,KAAK,CACjE,GACA,IACC;wBACDH,QAAQC,GAAG,CACT,CAAC,EAAE,EAAEpD,SAAS,cAAc,EAAEV,MAAMR,IAAI,CAAC,MAAM,EAAEW,QAAQI,MAAM,CAAC,SAAS,CAAC;oBAE9E;gBACF;gBAEA,OAAOZ;YACT;QAEJ;IACF;AACF", "ignoreList": [0]}