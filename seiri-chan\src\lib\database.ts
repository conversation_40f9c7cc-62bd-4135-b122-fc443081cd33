import { db } from './db';
import type { 
  Task, 
  TaskBatch, 
  TaskLog, 
  TaskSearchParams, 
  PaginatedResponse,
  CreateTaskParams,
  CreateBatchTasksParams,
  TaskStats
} from '@/types';

// 任务相关数据库操作
export class TaskDatabase {
  // 创建单个任务
  static async createTask(params: CreateTaskParams): Promise<Task> {
    const task = await db.task.create({
      data: {
        name: params.name,
        type: params.type,
        sourcePath: params.sourcePath,
        status: 'pending',
        progress: 0,
      },
    });

    return this.mapTaskFromDB(task);
  }

  // 创建批量任务
  static async createBatchTasks(params: CreateBatchTasksParams): Promise<TaskBatch> {
    const batch = await db.taskBatch.create({
      data: {
        name: params.name,
        status: 'pending',
        totalTasks: params.paths.length,
      },
    });

    const tasks = await Promise.all(
      params.paths.map(path =>
        db.task.create({
          data: {
            name: `${params.name} - ${path}`,
            type: params.type,
            sourcePath: path,
            status: 'pending',
            progress: 0,
            batchId: batch.id,
          },
        })
      )
    );

    return {
      ...batch,
      tasks: tasks.map(this.mapTaskFromDB),
    };
  }

  // 获取任务列表
  static async getTasks(params: TaskSearchParams = {}): Promise<PaginatedResponse<Task>> {
    const {
      page = 1,
      limit = 20,
      status,
      type,
      batchId,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = params;

    const skip = (page - 1) * limit;
    
    // 构建查询条件
    const where: any = {};
    
    if (status && status !== 'all') {
      where.status = status;
    }
    
    if (type && type !== 'all') {
      where.type = type;
    }
    
    if (batchId) {
      where.batchId = batchId;
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { sourcePath: { contains: search, mode: 'insensitive' } },
      ];
    }

    // 执行查询
    const [tasks, total] = await Promise.all([
      db.task.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          batch: true,
        },
      }),
      db.task.count({ where }),
    ]);

    return {
      data: tasks.map(this.mapTaskFromDB),
      total,
      page,
      limit,
      hasNext: skip + limit < total,
      hasPrev: page > 1,
    };
  }

  // 获取单个任务
  static async getTask(id: string): Promise<Task | null> {
    const task = await db.task.findUnique({
      where: { id },
      include: {
        batch: true,
        logs: {
          orderBy: { createdAt: 'desc' },
          take: 100, // 最近100条日志
        },
      },
    });

    return task ? this.mapTaskFromDB(task) : null;
  }

  // 更新任务
  static async updateTask(id: string, data: Partial<Task>): Promise<Task> {
    const task = await db.task.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
        ...(data.status === 'completed' && { completedAt: new Date() }),
      },
    });

    return this.mapTaskFromDB(task);
  }

  // 删除任务
  static async deleteTask(id: string): Promise<void> {
    await db.task.delete({
      where: { id },
    });
  }

  // 批量删除任务
  static async deleteTasks(ids: string[]): Promise<void> {
    await db.task.deleteMany({
      where: {
        id: { in: ids },
      },
    });
  }

  // 获取任务统计
  static async getTaskStats(): Promise<TaskStats> {
    const stats = await db.task.groupBy({
      by: ['status'],
      _count: true,
    });

    const result: TaskStats = {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      successRate: 0,
    };

    for (const stat of stats) {
      result.total += stat._count;
      result[stat.status as keyof TaskStats] = stat._count as any;
    }

    result.successRate = result.total > 0 ? (result.completed / result.total) * 100 : 0;

    return result;
  }

  // 添加任务日志
  static async addTaskLog(taskId: string, log: Omit<TaskLog, 'id' | 'taskId' | 'createdAt'>): Promise<TaskLog> {
    const taskLog = await db.taskLog.create({
      data: {
        taskId,
        level: log.level,
        message: log.message,
        metadata: log.metadata || {},
      },
    });

    return {
      id: taskLog.id,
      taskId: taskLog.taskId,
      level: taskLog.level as any,
      message: taskLog.message,
      metadata: taskLog.metadata as any,
      createdAt: taskLog.createdAt,
    };
  }

  // 获取任务日志
  static async getTaskLogs(taskId: string, limit = 100): Promise<TaskLog[]> {
    const logs = await db.taskLog.findMany({
      where: { taskId },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });

    return logs.map(log => ({
      id: log.id,
      taskId: log.taskId,
      level: log.level as any,
      message: log.message,
      metadata: log.metadata as any,
      createdAt: log.createdAt,
    }));
  }

  // 数据库对象到类型的映射
  private static mapTaskFromDB(task: any): Task {
    return {
      id: task.id,
      name: task.name,
      type: task.type,
      status: task.status,
      sourcePath: task.sourcePath,
      sourceFiles: task.sourceFiles ? JSON.parse(task.sourceFiles) : undefined,
      metadata: task.metadata ? JSON.parse(task.metadata) : undefined,
      mapping: task.mapping ? JSON.parse(task.mapping) : undefined,
      result: task.result ? JSON.parse(task.result) : undefined,
      progress: task.progress,
      errorMessage: task.errorMessage,
      batchId: task.batchId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      completedAt: task.completedAt,
    };
  }
}

// 批次相关数据库操作
export class BatchDatabase {
  // 获取批次列表
  static async getBatches(): Promise<TaskBatch[]> {
    const batches = await db.taskBatch.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        tasks: true,
      },
    });

    return batches.map(batch => ({
      id: batch.id,
      name: batch.name,
      status: batch.status as any,
      totalTasks: batch.totalTasks,
      completedTasks: batch.completedTasks,
      failedTasks: batch.failedTasks,
      createdAt: batch.createdAt,
      updatedAt: batch.updatedAt,
      tasks: batch.tasks?.map(TaskDatabase['mapTaskFromDB']),
    }));
  }

  // 获取单个批次
  static async getBatch(id: string): Promise<TaskBatch | null> {
    const batch = await db.taskBatch.findUnique({
      where: { id },
      include: {
        tasks: true,
      },
    });

    if (!batch) return null;

    return {
      id: batch.id,
      name: batch.name,
      status: batch.status as any,
      totalTasks: batch.totalTasks,
      completedTasks: batch.completedTasks,
      failedTasks: batch.failedTasks,
      createdAt: batch.createdAt,
      updatedAt: batch.updatedAt,
      tasks: batch.tasks?.map(TaskDatabase['mapTaskFromDB']),
    };
  }

  // 更新批次状态
  static async updateBatchStatus(id: string): Promise<void> {
    const tasks = await db.task.findMany({
      where: { batchId: id },
      select: { status: true },
    });

    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const failedTasks = tasks.filter(t => t.status === 'failed').length;
    const processingTasks = tasks.filter(t => t.status === 'processing').length;

    let batchStatus: string;
    if (processingTasks > 0) {
      batchStatus = 'processing';
    } else if (completedTasks === tasks.length) {
      batchStatus = 'completed';
    } else if (failedTasks === tasks.length) {
      batchStatus = 'failed';
    } else if (completedTasks + failedTasks === tasks.length) {
      batchStatus = 'completed'; // 部分成功也算完成
    } else {
      batchStatus = 'pending';
    }

    await db.taskBatch.update({
      where: { id },
      data: {
        status: batchStatus,
        completedTasks,
        failedTasks,
        updatedAt: new Date(),
      },
    });
  }

  // 删除批次
  static async deleteBatch(id: string): Promise<void> {
    await db.taskBatch.delete({
      where: { id },
    });
  }
}

// TMDB缓存相关数据库操作
export class TMDBCacheDatabase {
  // 获取缓存
  static async getCache(type: 'tv' | 'movie', tmdbId: number, language = 'zh-CN') {
    const cache = await db.tMDBCache.findUnique({
      where: {
        type_tmdbId_language: {
          type,
          tmdbId,
          language,
        },
      },
    });

    if (!cache || cache.expiresAt < new Date()) {
      return null;
    }

    return JSON.parse(cache.data as string);
  }

  // 设置缓存
  static async setCache(
    type: 'tv' | 'movie',
    tmdbId: number,
    data: any,
    language = 'zh-CN',
    expirationHours = 24
  ) {
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expirationHours);

    await db.tMDBCache.upsert({
      where: {
        type_tmdbId_language: {
          type,
          tmdbId,
          language,
        },
      },
      update: {
        data: JSON.stringify(data),
        expiresAt,
      },
      create: {
        type,
        tmdbId,
        language,
        data: JSON.stringify(data),
        expiresAt,
      },
    });
  }

  // 清理过期缓存
  static async cleanExpiredCache() {
    await db.tMDBCache.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
  }
}
