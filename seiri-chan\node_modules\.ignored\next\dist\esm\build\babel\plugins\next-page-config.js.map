{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-page-config.ts"], "sourcesContent": ["import { types as BabelTypes } from 'next/dist/compiled/babel/core'\nimport type {\n  PluginObj,\n  PluginPass,\n  Visitor,\n  NodePath,\n} from 'next/dist/compiled/babel/core'\nimport type { PageConfig } from '../../../types'\nimport { STRING_LITERAL_DROP_BUNDLE } from '../../../shared/lib/constants'\n\nconst CONFIG_KEY = 'config'\n\n// replace program path with just a variable with the drop identifier\nfunction replaceBundle(path: any, t: typeof BabelTypes): void {\n  path.parentPath.replaceWith(\n    t.program(\n      [\n        t.variableDeclaration('const', [\n          t.variableDeclarator(\n            t.identifier(STRING_LITERAL_DROP_BUNDLE),\n            t.stringLiteral(`${STRING_LITERAL_DROP_BUNDLE} ${Date.now()}`)\n          ),\n        ]),\n      ],\n      []\n    )\n  )\n}\n\nfunction errorMessage(state: any, details: string): string {\n  const pageName =\n    (state.filename || '').split(state.cwd || '').pop() || 'unknown'\n  return `Invalid page config export found. ${details} in file ${pageName}. See: https://nextjs.org/docs/messages/invalid-page-config`\n}\n\ninterface ConfigState extends PluginPass {\n  bundleDropped?: boolean\n}\n\n// config to parsing pageConfig for client bundles\nexport default function nextPageConfig({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      Program: {\n        enter(path, state) {\n          path.traverse(\n            {\n              ExportDeclaration(exportPath, exportState) {\n                if (\n                  BabelTypes.isExportNamedDeclaration(exportPath.node) &&\n                  exportPath.node.specifiers?.some((specifier) => {\n                    return (\n                      (t.isIdentifier(specifier.exported)\n                        ? specifier.exported.name\n                        : specifier.exported.value) === CONFIG_KEY\n                    )\n                  }) &&\n                  BabelTypes.isStringLiteral(\n                    (exportPath.node as BabelTypes.ExportNamedDeclaration)\n                      .source\n                  )\n                ) {\n                  throw new Error(\n                    errorMessage(\n                      exportState,\n                      'Expected object but got export from'\n                    )\n                  )\n                }\n              },\n              ExportNamedDeclaration(\n                exportPath: NodePath<BabelTypes.ExportNamedDeclaration>,\n                exportState: any\n              ) {\n                if (\n                  exportState.bundleDropped ||\n                  (!exportPath.node.declaration &&\n                    exportPath.node.specifiers.length === 0)\n                ) {\n                  return\n                }\n\n                const config: PageConfig = {}\n                const declarations: BabelTypes.VariableDeclarator[] = [\n                  ...((\n                    exportPath.node\n                      .declaration as BabelTypes.VariableDeclaration\n                  )?.declarations || []),\n                  exportPath.scope.getBinding(CONFIG_KEY)?.path\n                    .node as BabelTypes.VariableDeclarator,\n                ].filter(Boolean)\n\n                for (const specifier of exportPath.node.specifiers) {\n                  if (\n                    (t.isIdentifier(specifier.exported)\n                      ? specifier.exported.name\n                      : specifier.exported.value) === CONFIG_KEY\n                  ) {\n                    // export {} from 'somewhere'\n                    if (BabelTypes.isStringLiteral(exportPath.node.source)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Expected object but got import`\n                        )\n                      )\n                      // import hello from 'world'\n                      // export { hello as config }\n                    } else if (\n                      BabelTypes.isIdentifier(\n                        (specifier as BabelTypes.ExportSpecifier).local\n                      )\n                    ) {\n                      if (\n                        BabelTypes.isImportSpecifier(\n                          exportPath.scope.getBinding(\n                            (specifier as BabelTypes.ExportSpecifier).local.name\n                          )?.path.node\n                        )\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Expected object but got import`\n                          )\n                        )\n                      }\n                    }\n                  }\n                }\n\n                for (const declaration of declarations) {\n                  if (\n                    !BabelTypes.isIdentifier(declaration.id, {\n                      name: CONFIG_KEY,\n                    })\n                  ) {\n                    continue\n                  }\n\n                  let { init } = declaration\n                  if (BabelTypes.isTSAsExpression(init)) {\n                    init = init.expression\n                  }\n\n                  if (!BabelTypes.isObjectExpression(init)) {\n                    const got = init ? init.type : 'undefined'\n                    throw new Error(\n                      errorMessage(\n                        exportState,\n                        `Expected object but got ${got}`\n                      )\n                    )\n                  }\n\n                  for (const prop of init.properties) {\n                    if (BabelTypes.isSpreadElement(prop)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Property spread is not allowed`\n                        )\n                      )\n                    }\n                    const { name } = prop.key as BabelTypes.Identifier\n                    if (BabelTypes.isIdentifier(prop.key, { name: 'amp' })) {\n                      if (!BabelTypes.isObjectProperty(prop)) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid property \"${name}\"`\n                          )\n                        )\n                      }\n                      if (\n                        !BabelTypes.isBooleanLiteral(prop.value) &&\n                        !BabelTypes.isStringLiteral(prop.value)\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid value for \"${name}\"`\n                          )\n                        )\n                      }\n                      config.amp = prop.value.value as PageConfig['amp']\n                    }\n                  }\n                }\n\n                if (config.amp === true) {\n                  if (!exportState.file?.opts?.caller.isDev) {\n                    // don't replace bundle in development so HMR can track\n                    // dependencies and trigger reload when they are changed\n                    replaceBundle(exportPath, t)\n                  }\n                  exportState.bundleDropped = true\n                  return\n                }\n              },\n            },\n            state\n          )\n        },\n      },\n    } as Visitor<ConfigState>,\n  }\n}\n"], "names": ["types", "BabelTypes", "STRING_LITERAL_DROP_BUNDLE", "CONFIG_KEY", "replaceBundle", "path", "t", "parentPath", "replaceWith", "program", "variableDeclaration", "variableDeclarator", "identifier", "stringLiteral", "Date", "now", "errorMessage", "state", "details", "pageName", "filename", "split", "cwd", "pop", "nextPageConfig", "visitor", "Program", "enter", "traverse", "ExportDeclaration", "exportPath", "exportState", "isExportNamedDeclaration", "node", "specifiers", "some", "specifier", "isIdentifier", "exported", "name", "value", "isStringLiteral", "source", "Error", "ExportNamedDeclaration", "bundleDropped", "declaration", "length", "config", "declarations", "scope", "getBinding", "filter", "Boolean", "local", "isImportSpecifier", "id", "init", "isTSAsExpression", "expression", "isObjectExpression", "got", "type", "prop", "properties", "isSpreadElement", "key", "isObjectProperty", "isBooleanLiteral", "amp", "file", "opts", "caller", "isDev"], "mappings": "AAAA,SAASA,SAASC,UAAU,QAAQ,gCAA+B;AAQnE,SAASC,0BAA0B,QAAQ,gCAA+B;AAE1E,MAAMC,aAAa;AAEnB,qEAAqE;AACrE,SAASC,cAAcC,IAAS,EAAEC,CAAoB;IACpDD,KAAKE,UAAU,CAACC,WAAW,CACzBF,EAAEG,OAAO,CACP;QACEH,EAAEI,mBAAmB,CAAC,SAAS;YAC7BJ,EAAEK,kBAAkB,CAClBL,EAAEM,UAAU,CAACV,6BACbI,EAAEO,aAAa,CAAC,GAAGX,2BAA2B,CAAC,EAAEY,KAAKC,GAAG,IAAI;SAEhE;KACF,EACD,EAAE;AAGR;AAEA,SAASC,aAAaC,KAAU,EAAEC,OAAe;IAC/C,MAAMC,WACJ,AAACF,CAAAA,MAAMG,QAAQ,IAAI,EAAC,EAAGC,KAAK,CAACJ,MAAMK,GAAG,IAAI,IAAIC,GAAG,MAAM;IACzD,OAAO,CAAC,kCAAkC,EAAEL,QAAQ,SAAS,EAAEC,SAAS,2DAA2D,CAAC;AACtI;AAMA,kDAAkD;AAClD,eAAe,SAASK,eAAe,EACrCxB,OAAOM,CAAC,EAGT;IACC,OAAO;QACLmB,SAAS;YACPC,SAAS;gBACPC,OAAMtB,IAAI,EAAEY,KAAK;oBACfZ,KAAKuB,QAAQ,CACX;wBACEC,mBAAkBC,UAAU,EAAEC,WAAW;gCAGrCD;4BAFF,IACE7B,WAAW+B,wBAAwB,CAACF,WAAWG,IAAI,OACnDH,8BAAAA,WAAWG,IAAI,CAACC,UAAU,qBAA1BJ,4BAA4BK,IAAI,CAAC,CAACC;gCAChC,OACE,AAAC9B,CAAAA,EAAE+B,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOrC;4BAEtC,OACAF,WAAWwC,eAAe,CACxB,AAACX,WAAWG,IAAI,CACbS,MAAM,GAEX;gCACA,MAAM,qBAKL,CALK,IAAIC,MACR3B,aACEe,aACA,yCAHE,qBAAA;2CAAA;gDAAA;kDAAA;gCAKN;4BACF;wBACF;wBACAa,wBACEd,UAAuD,EACvDC,WAAgB;gCAaZD,8BAGFA;4BAdF,IACEC,YAAYc,aAAa,IACxB,CAACf,WAAWG,IAAI,CAACa,WAAW,IAC3BhB,WAAWG,IAAI,CAACC,UAAU,CAACa,MAAM,KAAK,GACxC;gCACA;4BACF;4BAEA,MAAMC,SAAqB,CAAC;4BAC5B,MAAMC,eAAgD;mCAChD,EACFnB,+BAAAA,WAAWG,IAAI,CACZa,WAAW,qBAFZ,AACFhB,6BAECmB,YAAY,KAAI,EAAE;iCACrBnB,+BAAAA,WAAWoB,KAAK,CAACC,UAAU,CAAChD,gCAA5B2B,6BAAyCzB,IAAI,CAC1C4B,IAAI;6BACR,CAACmB,MAAM,CAACC;4BAET,KAAK,MAAMjB,aAAaN,WAAWG,IAAI,CAACC,UAAU,CAAE;gCAClD,IACE,AAAC5B,CAAAA,EAAE+B,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOrC,YAClC;oCACA,6BAA6B;oCAC7B,IAAIF,WAAWwC,eAAe,CAACX,WAAWG,IAAI,CAACS,MAAM,GAAG;wCACtD,MAAM,qBAKL,CALK,IAAIC,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;mDAAA;wDAAA;0DAAA;wCAKN;oCACA,4BAA4B;oCAC5B,6BAA6B;oCAC/B,OAAO,IACL9B,WAAWoC,YAAY,CACrB,AAACD,UAAyCkB,KAAK,GAEjD;4CAGIxB;wCAFJ,IACE7B,WAAWsD,iBAAiB,EAC1BzB,gCAAAA,WAAWoB,KAAK,CAACC,UAAU,CACzB,AAACf,UAAyCkB,KAAK,CAACf,IAAI,sBADtDT,8BAEGzB,IAAI,CAAC4B,IAAI,GAEd;4CACA,MAAM,qBAKL,CALK,IAAIU,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;oCACF;gCACF;4BACF;4BAEA,KAAK,MAAMe,eAAeG,aAAc;gCACtC,IACE,CAAChD,WAAWoC,YAAY,CAACS,YAAYU,EAAE,EAAE;oCACvCjB,MAAMpC;gCACR,IACA;oCACA;gCACF;gCAEA,IAAI,EAAEsD,IAAI,EAAE,GAAGX;gCACf,IAAI7C,WAAWyD,gBAAgB,CAACD,OAAO;oCACrCA,OAAOA,KAAKE,UAAU;gCACxB;gCAEA,IAAI,CAAC1D,WAAW2D,kBAAkB,CAACH,OAAO;oCACxC,MAAMI,MAAMJ,OAAOA,KAAKK,IAAI,GAAG;oCAC/B,MAAM,qBAKL,CALK,IAAInB,MACR3B,aACEe,aACA,CAAC,wBAAwB,EAAE8B,KAAK,IAH9B,qBAAA;+CAAA;oDAAA;sDAAA;oCAKN;gCACF;gCAEA,KAAK,MAAME,QAAQN,KAAKO,UAAU,CAAE;oCAClC,IAAI/D,WAAWgE,eAAe,CAACF,OAAO;wCACpC,MAAM,qBAKL,CALK,IAAIpB,MACR3B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;mDAAA;wDAAA;0DAAA;wCAKN;oCACF;oCACA,MAAM,EAAEQ,IAAI,EAAE,GAAGwB,KAAKG,GAAG;oCACzB,IAAIjE,WAAWoC,YAAY,CAAC0B,KAAKG,GAAG,EAAE;wCAAE3B,MAAM;oCAAM,IAAI;wCACtD,IAAI,CAACtC,WAAWkE,gBAAgB,CAACJ,OAAO;4CACtC,MAAM,qBAKL,CALK,IAAIpB,MACR3B,aACEe,aACA,CAAC,kBAAkB,EAAEQ,KAAK,CAAC,CAAC,IAH1B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;wCACA,IACE,CAACtC,WAAWmE,gBAAgB,CAACL,KAAKvB,KAAK,KACvC,CAACvC,WAAWwC,eAAe,CAACsB,KAAKvB,KAAK,GACtC;4CACA,MAAM,qBAKL,CALK,IAAIG,MACR3B,aACEe,aACA,CAAC,mBAAmB,EAAEQ,KAAK,CAAC,CAAC,IAH3B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;wCACAS,OAAOqB,GAAG,GAAGN,KAAKvB,KAAK,CAACA,KAAK;oCAC/B;gCACF;4BACF;4BAEA,IAAIQ,OAAOqB,GAAG,KAAK,MAAM;oCAClBtC,wBAAAA;gCAAL,IAAI,GAACA,oBAAAA,YAAYuC,IAAI,sBAAhBvC,yBAAAA,kBAAkBwC,IAAI,qBAAtBxC,uBAAwByC,MAAM,CAACC,KAAK,GAAE;oCACzC,uDAAuD;oCACvD,wDAAwD;oCACxDrE,cAAc0B,YAAYxB;gCAC5B;gCACAyB,YAAYc,aAAa,GAAG;gCAC5B;4BACF;wBACF;oBACF,GACA5B;gBAEJ;YACF;QACF;IACF;AACF", "ignoreList": [0]}