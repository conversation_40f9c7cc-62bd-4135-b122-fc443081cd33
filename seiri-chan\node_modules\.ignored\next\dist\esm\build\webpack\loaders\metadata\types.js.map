{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/types.ts"], "sourcesContent": ["// TODO-APP: check if this can be narrowed.\nexport type ModuleGetter = () => any\n\nexport type ModuleTuple = [getModule: ModuleGetter, filePath: string]\n\n// Contain the collecting image module paths\nexport type CollectingMetadata = {\n  icon: string[]\n  apple: string[]\n  twitter: string[]\n  openGraph: string[]\n  manifest?: string\n}\n\n// Contain the collecting evaluated image module\nexport type CollectedMetadata = {\n  icon: ModuleGetter[]\n  apple: ModuleGetter[]\n  twitter: ModuleGetter[] | null\n  openGraph: ModuleGetter[] | null\n  manifest?: string\n}\n\nexport type MetadataImageModule = {\n  url: string\n  type?: string\n  alt?: string\n} & (\n  | { sizes?: string }\n  | {\n      width?: number\n      height?: number\n    }\n)\n\nexport type PossibleImageFileNameConvention =\n  | 'icon'\n  | 'apple'\n  | 'favicon'\n  | 'twitter'\n  | 'openGraph'\n\nexport type PossibleStaticMetadataFileNameConvention =\n  | PossibleImageFileNameConvention\n  | 'manifest'\n"], "names": [], "mappings": "AAAA,2CAA2C;AA0C3C,WAEc", "ignoreList": [0]}