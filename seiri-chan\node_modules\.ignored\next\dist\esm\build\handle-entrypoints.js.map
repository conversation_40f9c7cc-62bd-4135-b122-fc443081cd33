{"version": 3, "sources": ["../../src/build/handle-entrypoints.ts"], "sourcesContent": ["import type { TurbopackManifestLoader } from '../shared/lib/turbopack/manifest-loader'\nimport type {\n  Entrypoints,\n  PageRoute,\n  AppRoute,\n  RawEntrypoints,\n} from './swc/types'\nimport { getEntryKey } from '../shared/lib/turbopack/entry-key'\nimport * as Log from './output/log'\n\nexport async function rawEntrypointsToEntrypoints(\n  entrypointsOp: RawEntrypoints\n): Promise<Entrypoints> {\n  const page = new Map()\n  const app = new Map()\n\n  for (const [pathname, route] of entrypointsOp.routes) {\n    switch (route.type) {\n      case 'page':\n      case 'page-api':\n        page.set(pathname, route)\n        break\n      case 'app-page': {\n        for (const p of route.pages) {\n          app.set(p.originalName, {\n            type: 'app-page',\n            ...p,\n          })\n        }\n        break\n      }\n      case 'app-route': {\n        app.set(route.originalName, route)\n        break\n      }\n      default:\n        Log.info(`skipping ${pathname} (${route.type})`)\n        break\n    }\n  }\n\n  return {\n    global: {\n      app: entrypointsOp.pagesAppEndpoint,\n      document: entrypointsOp.pagesDocumentEndpoint,\n      error: entrypointsOp.pagesErrorEndpoint,\n      instrumentation: entrypointsOp.instrumentation,\n      middleware: entrypointsOp.middleware,\n    },\n    page,\n    app,\n  }\n}\n\nexport async function handleRouteType({\n  page,\n  route,\n  manifestLoader,\n}: {\n  page: string\n  route: PageRoute | AppRoute\n  manifestLoader: TurbopackManifestLoader\n}) {\n  const shouldCreateWebpackStats = process.env.TURBOPACK_STATS != null\n\n  switch (route.type) {\n    case 'page': {\n      const serverKey = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadBuildManifest(page)\n      await manifestLoader.loadPagesManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(serverKey)\n      }\n\n      await manifestLoader.loadFontManifest('/_app', 'pages')\n      await manifestLoader.loadFontManifest(page, 'pages')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'pages')\n      }\n\n      break\n    }\n    case 'page-api': {\n      const key = getEntryKey('pages', 'server', page)\n\n      await manifestLoader.loadPagesManifest(page)\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'pages')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    case 'app-page': {\n      const key = getEntryKey('app', 'server', page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      await manifestLoader.loadAppBuildManifest(page)\n      await manifestLoader.loadBuildManifest(page, 'app')\n      await manifestLoader.loadAppPathsManifest(page)\n      await manifestLoader.loadActionManifest(page)\n      await manifestLoader.loadFontManifest(page, 'app')\n\n      if (shouldCreateWebpackStats) {\n        await manifestLoader.loadWebpackStats(page, 'app')\n      }\n\n      break\n    }\n    case 'app-route': {\n      const key = getEntryKey('app', 'server', page)\n\n      await manifestLoader.loadAppPathsManifest(page)\n\n      const middlewareManifestWritten =\n        await manifestLoader.loadMiddlewareManifest(page, 'app')\n\n      if (!middlewareManifestWritten) {\n        manifestLoader.deleteMiddlewareManifest(key)\n      }\n\n      break\n    }\n    default: {\n      throw new Error(`unknown route type ${(route as any).type} for ${page}`)\n    }\n  }\n}\n"], "names": ["getEntry<PERSON>ey", "Log", "rawEntrypointsToEntrypoints", "entrypointsOp", "page", "Map", "app", "pathname", "route", "routes", "type", "set", "p", "pages", "originalName", "info", "global", "pagesAppEndpoint", "document", "pagesDocumentEndpoint", "error", "pagesErrorEndpoint", "instrumentation", "middleware", "handleRouteType", "manifest<PERSON><PERSON>der", "shouldCreateWebpackStats", "process", "env", "TURBOPACK_STATS", "server<PERSON>ey", "loadBuildManifest", "loadPagesManifest", "middlewareManifestWritten", "loadMiddlewareManifest", "deleteMiddlewareManifest", "loadFontManifest", "loadWebpackStats", "key", "loadAppBuildManifest", "loadAppPathsManifest", "loadActionManifest", "Error"], "mappings": "AAOA,SAASA,WAAW,QAAQ,oCAAmC;AAC/D,YAAYC,SAAS,eAAc;AAEnC,OAAO,eAAeC,4BACpBC,aAA6B;IAE7B,MAAMC,OAAO,IAAIC;IACjB,MAAMC,MAAM,IAAID;IAEhB,KAAK,MAAM,CAACE,UAAUC,MAAM,IAAIL,cAAcM,MAAM,CAAE;QACpD,OAAQD,MAAME,IAAI;YAChB,KAAK;YACL,KAAK;gBACHN,KAAKO,GAAG,CAACJ,UAAUC;gBACnB;YACF,KAAK;gBAAY;oBACf,KAAK,MAAMI,KAAKJ,MAAMK,KAAK,CAAE;wBAC3BP,IAAIK,GAAG,CAACC,EAAEE,YAAY,EAAE;4BACtBJ,MAAM;4BACN,GAAGE,CAAC;wBACN;oBACF;oBACA;gBACF;YACA,KAAK;gBAAa;oBAChBN,IAAIK,GAAG,CAACH,MAAMM,YAAY,EAAEN;oBAC5B;gBACF;YACA;gBACEP,IAAIc,IAAI,CAAC,CAAC,SAAS,EAAER,SAAS,EAAE,EAAEC,MAAME,IAAI,CAAC,CAAC,CAAC;gBAC/C;QACJ;IACF;IAEA,OAAO;QACLM,QAAQ;YACNV,KAAKH,cAAcc,gBAAgB;YACnCC,UAAUf,cAAcgB,qBAAqB;YAC7CC,OAAOjB,cAAckB,kBAAkB;YACvCC,iBAAiBnB,cAAcmB,eAAe;YAC9CC,YAAYpB,cAAcoB,UAAU;QACtC;QACAnB;QACAE;IACF;AACF;AAEA,OAAO,eAAekB,gBAAgB,EACpCpB,IAAI,EACJI,KAAK,EACLiB,cAAc,EAKf;IACC,MAAMC,2BAA2BC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAEhE,OAAQrB,MAAME,IAAI;QAChB,KAAK;YAAQ;gBACX,MAAMoB,YAAY9B,YAAY,SAAS,UAAUI;gBAEjD,MAAMqB,eAAeM,iBAAiB,CAAC3B;gBACvC,MAAMqB,eAAeO,iBAAiB,CAAC5B;gBAEvC,MAAM6B,4BACJ,MAAMR,eAAeS,sBAAsB,CAAC9B,MAAM;gBACpD,IAAI,CAAC6B,2BAA2B;oBAC9BR,eAAeU,wBAAwB,CAACL;gBAC1C;gBAEA,MAAML,eAAeW,gBAAgB,CAAC,SAAS;gBAC/C,MAAMX,eAAeW,gBAAgB,CAAChC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAeY,gBAAgB,CAACjC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMkC,MAAMtC,YAAY,SAAS,UAAUI;gBAE3C,MAAMqB,eAAeO,iBAAiB,CAAC5B;gBACvC,MAAM6B,4BACJ,MAAMR,eAAeS,sBAAsB,CAAC9B,MAAM;gBACpD,IAAI,CAAC6B,2BAA2B;oBAC9BR,eAAeU,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA,KAAK;YAAY;gBACf,MAAMA,MAAMtC,YAAY,OAAO,UAAUI;gBAEzC,MAAM6B,4BACJ,MAAMR,eAAeS,sBAAsB,CAAC9B,MAAM;gBACpD,IAAI,CAAC6B,2BAA2B;oBAC9BR,eAAeU,wBAAwB,CAACG;gBAC1C;gBAEA,MAAMb,eAAec,oBAAoB,CAACnC;gBAC1C,MAAMqB,eAAeM,iBAAiB,CAAC3B,MAAM;gBAC7C,MAAMqB,eAAee,oBAAoB,CAACpC;gBAC1C,MAAMqB,eAAegB,kBAAkB,CAACrC;gBACxC,MAAMqB,eAAeW,gBAAgB,CAAChC,MAAM;gBAE5C,IAAIsB,0BAA0B;oBAC5B,MAAMD,eAAeY,gBAAgB,CAACjC,MAAM;gBAC9C;gBAEA;YACF;QACA,KAAK;YAAa;gBAChB,MAAMkC,MAAMtC,YAAY,OAAO,UAAUI;gBAEzC,MAAMqB,eAAee,oBAAoB,CAACpC;gBAE1C,MAAM6B,4BACJ,MAAMR,eAAeS,sBAAsB,CAAC9B,MAAM;gBAEpD,IAAI,CAAC6B,2BAA2B;oBAC9BR,eAAeU,wBAAwB,CAACG;gBAC1C;gBAEA;YACF;QACA;YAAS;gBACP,MAAM,qBAAkE,CAAlE,IAAII,MAAM,CAAC,mBAAmB,EAAE,AAAClC,MAAcE,IAAI,CAAC,KAAK,EAAEN,MAAM,GAAjE,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiE;YACzE;IACF;AACF", "ignoreList": [0]}