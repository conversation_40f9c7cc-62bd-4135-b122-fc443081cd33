// 数据库工具函数

import { db } from './db';

// 数据库健康检查
export async function checkDatabaseHealth() {
  try {
    // 检查数据库连接
    await db.$queryRaw`SELECT 1`;
    
    // 获取各表的记录数
    const [
      configCount,
      taskCount,
      batchCount,
      logCount,
      cacheCount,
    ] = await Promise.all([
      db.config.count(),
      db.task.count(),
      db.taskBatch.count(),
      db.taskLog.count(),
      db.tMDBCache.count(),
    ]);

    return {
      status: 'healthy',
      connection: true,
      tables: {
        configs: configCount,
        tasks: taskCount,
        batches: batchCount,
        logs: logCount,
        cache: cacheCount,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      connection: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
}

// 清理过期数据
export async function cleanupExpiredData() {
  try {
    // 清理过期的TMDB缓存
    const expiredCacheResult = await db.tMDBCache.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    // 清理超过30天的日志
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const oldLogsResult = await db.taskLog.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo,
        },
      },
    });

    // 清理超过90天的已完成任务
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
    
    const oldTasksResult = await db.task.deleteMany({
      where: {
        status: 'completed',
        completedAt: {
          lt: ninetyDaysAgo,
        },
      },
    });

    return {
      success: true,
      cleaned: {
        expiredCache: expiredCacheResult.count,
        oldLogs: oldLogsResult.count,
        oldTasks: oldTasksResult.count,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
}

// 获取数据库统计信息
export async function getDatabaseStats() {
  try {
    // 任务统计
    const taskStats = await db.task.groupBy({
      by: ['status'],
      _count: true,
    });

    const tasksByType = await db.task.groupBy({
      by: ['type'],
      _count: true,
    });

    // 最近7天的任务创建趋势
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentTasks = await db.task.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
      select: {
        createdAt: true,
        status: true,
      },
    });

    // 缓存统计
    const cacheStats = await db.tMDBCache.groupBy({
      by: ['type'],
      _count: true,
    });

    const cacheHitRate = await calculateCacheHitRate();

    // 存储使用情况（SQLite文件大小）
    const dbSize = await getDatabaseSize();

    return {
      tasks: {
        total: taskStats.reduce((sum, stat) => sum + stat._count, 0),
        byStatus: Object.fromEntries(
          taskStats.map(stat => [stat.status, stat._count])
        ),
        byType: Object.fromEntries(
          tasksByType.map(stat => [stat.type, stat._count])
        ),
        recentTrend: groupTasksByDate(recentTasks),
      },
      cache: {
        total: cacheStats.reduce((sum, stat) => sum + stat._count, 0),
        byType: Object.fromEntries(
          cacheStats.map(stat => [stat.type, stat._count])
        ),
        hitRate: cacheHitRate,
      },
      storage: {
        databaseSize: dbSize,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    throw new Error(`Failed to get database stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// 辅助函数：计算缓存命中率（简化版）
async function calculateCacheHitRate(): Promise<number> {
  // 这里是一个简化的实现，实际应用中需要记录缓存命中和未命中的次数
  const totalCache = await db.tMDBCache.count();
  const validCache = await db.tMDBCache.count({
    where: {
      expiresAt: {
        gt: new Date(),
      },
    },
  });
  
  return totalCache > 0 ? (validCache / totalCache) * 100 : 0;
}

// 辅助函数：获取数据库文件大小
async function getDatabaseSize(): Promise<number> {
  try {
    const fs = await import('fs');
    const path = await import('path');
    
    const dbPath = path.join(process.cwd(), 'prisma', 'dev.db');
    const stats = fs.statSync(dbPath);
    return stats.size;
  } catch {
    return 0;
  }
}

// 辅助函数：按日期分组任务
function groupTasksByDate(tasks: { createdAt: Date; status: string }[]) {
  const groups: Record<string, Record<string, number>> = {};
  
  for (const task of tasks) {
    const date = task.createdAt.toISOString().split('T')[0];
    if (!groups[date]) {
      groups[date] = {};
    }
    groups[date][task.status] = (groups[date][task.status] || 0) + 1;
  }
  
  return groups;
}

// 数据库备份（导出为JSON）
export async function exportDatabaseToJSON() {
  try {
    const [configs, tasks, batches, logs, cache] = await Promise.all([
      db.config.findMany(),
      db.task.findMany({
        include: {
          batch: true,
        },
      }),
      db.taskBatch.findMany({
        include: {
          tasks: true,
        },
      }),
      db.taskLog.findMany(),
      db.tMDBCache.findMany(),
    ]);

    return {
      version: '1.0.0',
      exportedAt: new Date().toISOString(),
      data: {
        configs,
        tasks,
        batches,
        logs,
        cache,
      },
    };
  } catch (error) {
    throw new Error(`Failed to export database: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// 重置数据库到初始状态
export async function resetDatabase() {
  try {
    // 删除所有数据
    await db.taskLog.deleteMany();
    await db.task.deleteMany();
    await db.taskBatch.deleteMany();
    await db.tMDBCache.deleteMany();
    await db.config.deleteMany();

    return {
      success: true,
      message: 'Database reset successfully',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    };
  }
}

// 验证数据完整性
export async function validateDataIntegrity() {
  try {
    const issues: string[] = [];

    // 检查孤立的任务日志
    const orphanedLogs = await db.taskLog.count({
      where: {
        task: null,
      },
    });
    if (orphanedLogs > 0) {
      issues.push(`Found ${orphanedLogs} orphaned task logs`);
    }

    // 检查批次中的任务数量是否一致
    const batches = await db.taskBatch.findMany({
      include: {
        _count: {
          select: {
            tasks: true,
          },
        },
      },
    });

    for (const batch of batches) {
      if (batch.totalTasks !== batch._count.tasks) {
        issues.push(`Batch ${batch.id} has inconsistent task count: expected ${batch.totalTasks}, actual ${batch._count.tasks}`);
      }
    }

    // 检查过期的缓存
    const expiredCache = await db.tMDBCache.count({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
    if (expiredCache > 0) {
      issues.push(`Found ${expiredCache} expired cache entries`);
    }

    return {
      isValid: issues.length === 0,
      issues,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      isValid: false,
      issues: [`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      timestamp: new Date().toISOString(),
    };
  }
}
