{"version": 3, "sources": ["../../../../src/client/components/router-reducer/router-reducer.ts"], "sourcesContent": ["import {\n  ACTION_NAVIGATE,\n  ACTION_SERVER_PATCH,\n  ACTION_RESTORE,\n  ACTION_REFRESH,\n  ACTION_PREFETCH,\n  ACTION_HMR_REFRESH,\n  ACTION_SERVER_ACTION,\n} from './router-reducer-types'\nimport type {\n  ReducerActions,\n  ReducerState,\n  ReadonlyReducerState,\n} from './router-reducer-types'\nimport { navigateReducer } from './reducers/navigate-reducer'\nimport { serverPatchReducer } from './reducers/server-patch-reducer'\nimport { restoreReducer } from './reducers/restore-reducer'\nimport { refreshReducer } from './reducers/refresh-reducer'\nimport { prefetchReducer } from './reducers/prefetch-reducer'\nimport { hmrRefreshReducer } from './reducers/hmr-refresh-reducer'\nimport { serverActionReducer } from './reducers/server-action-reducer'\n\n/**\n * Reducer that handles the app-router state updates.\n */\nfunction clientReducer(\n  state: ReadonlyReducerState,\n  action: ReducerActions\n): ReducerState {\n  switch (action.type) {\n    case ACTION_NAVIGATE: {\n      return navigateReducer(state, action)\n    }\n    case ACTION_SERVER_PATCH: {\n      return serverPatchReducer(state, action)\n    }\n    case ACTION_RESTORE: {\n      return restoreReducer(state, action)\n    }\n    case ACTION_REFRESH: {\n      return refreshReducer(state, action)\n    }\n    case ACTION_HMR_REFRESH: {\n      return hmrRefreshReducer(state, action)\n    }\n    case ACTION_PREFETCH: {\n      return prefetchReducer(state, action)\n    }\n    case ACTION_SERVER_ACTION: {\n      return serverActionReducer(state, action)\n    }\n    // This case should never be hit as dispatch is strongly typed.\n    default:\n      throw new Error('Unknown action')\n  }\n}\n\nfunction serverReducer(\n  state: ReadonlyReducerState,\n  _action: ReducerActions\n): ReducerState {\n  return state\n}\n\n// we don't run the client reducer on the server, so we use a noop function for better tree shaking\nexport const reducer =\n  typeof window === 'undefined' ? serverReducer : clientReducer\n"], "names": ["ACTION_NAVIGATE", "ACTION_SERVER_PATCH", "ACTION_RESTORE", "ACTION_REFRESH", "ACTION_PREFETCH", "ACTION_HMR_REFRESH", "ACTION_SERVER_ACTION", "navigateReducer", "serverPatchReducer", "restoreReducer", "refreshReducer", "prefetchReducer", "hmrRefreshReducer", "serverActionReducer", "clientReducer", "state", "action", "type", "Error", "serverReducer", "_action", "reducer", "window"], "mappings": "AAAA,SACEA,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,QACf,yBAAwB;AAM/B,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,iBAAiB,QAAQ,iCAAgC;AAClE,SAASC,mBAAmB,QAAQ,mCAAkC;AAEtE;;CAEC,GACD,SAASC,cACPC,KAA2B,EAC3BC,MAAsB;IAEtB,OAAQA,OAAOC,IAAI;QACjB,KAAKjB;YAAiB;gBACpB,OAAOO,gBAAgBQ,OAAOC;YAChC;QACA,KAAKf;YAAqB;gBACxB,OAAOO,mBAAmBO,OAAOC;YACnC;QACA,KAAKd;YAAgB;gBACnB,OAAOO,eAAeM,OAAOC;YAC/B;QACA,KAAKb;YAAgB;gBACnB,OAAOO,eAAeK,OAAOC;YAC/B;QACA,KAAKX;YAAoB;gBACvB,OAAOO,kBAAkBG,OAAOC;YAClC;QACA,KAAKZ;YAAiB;gBACpB,OAAOO,gBAAgBI,OAAOC;YAChC;QACA,KAAKV;YAAsB;gBACzB,OAAOO,oBAAoBE,OAAOC;YACpC;QACA,+DAA+D;QAC/D;YACE,MAAM,qBAA2B,CAA3B,IAAIE,MAAM,mBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0B;IACpC;AACF;AAEA,SAASC,cACPJ,KAA2B,EAC3BK,OAAuB;IAEvB,OAAOL;AACT;AAEA,mGAAmG;AACnG,OAAO,MAAMM,UACX,OAAOC,WAAW,cAAcH,gBAAgBL,cAAa", "ignoreList": [0]}