{"version": 3, "sources": ["../../../src/build/webpack/cache-invalidation.ts"], "sourcesContent": ["import fs from 'node:fs/promises'\nimport path from 'node:path'\n\nconst INVALIDATION_MARKER = '__nextjs_invalidated_cache'\n\n/**\n * Atomically write an invalidation marker.\n *\n * Because attempting to delete currently open cache files could cause issues,\n * actual deletion of files is deferred until the next start-up (in\n * `checkPersistentCacheInvalidationAndCleanup`).\n *\n * In the case that no database is currently open (e.g. via a separate CLI\n * subcommand), you should call `cleanupPersistentCache` *after* this to eagerly\n * remove the cache files.\n */\nexport async function invalidatePersistentCache(cacheDirectory: string) {\n  let file\n  try {\n    // We're just opening it so that `open()` creates the file.\n    file = await fs.open(path.join(cacheDirectory, INVALIDATION_MARKER), 'w')\n    // We don't currently write anything to the file, but we could choose to\n    // later, e.g. a reason for the invalidation.\n  } catch (err: any) {\n    // it's valid for the cache to not exist at all\n    if (err.code !== 'ENOENT') {\n      throw err\n    }\n  } finally {\n    file?.close()\n  }\n}\n\n/**\n * Called during startup. See if the cache is in a partially-completed\n * invalidation state. Finds and delete any invalidated cache files.\n */\nexport async function checkPersistentCacheInvalidationAndCleanup(\n  cacheDirectory: string\n) {\n  const invalidated = await fs\n    .access(path.join(cacheDirectory, INVALIDATION_MARKER))\n    .then(\n      () => true,\n      () => false\n    )\n  if (invalidated) {\n    await cleanupPersistentCache(cacheDirectory)\n  }\n}\n\n/**\n * Helper for `checkPersistentCacheInvalidationAndCleanup`. You can call this to\n * explicitly clean up a database after running `invalidatePersistentCache` when\n * webpack is not running.\n *\n * You should not run this if the cache has not yet been invalidated, as this\n * operation is not atomic and could result in a partially-deleted and corrupted\n * database.\n */\nasync function cleanupPersistentCache(cacheDirectory: string) {\n  try {\n    await cleanupPersistentCacheInner(cacheDirectory)\n  } catch (e) {\n    // generate a user-friendly error message\n    throw new Error(\n      `Unable to remove an invalidated webpack cache. If this issue persists ` +\n        `you can work around it by deleting ${cacheDirectory}`,\n      { cause: e }\n    )\n  }\n}\n\nasync function cleanupPersistentCacheInner(cacheDirectory: string) {\n  const files = await fs.readdir(cacheDirectory)\n\n  // delete everything except the invalidation marker\n  await Promise.all(\n    files.map((name) =>\n      name !== INVALIDATION_MARKER\n        ? fs.rm(path.join(cacheDirectory, name), {\n            force: true, // ignore errors if path does not exist\n            recursive: true,\n            maxRetries: 2, // windows prevents deletion of open files\n          })\n        : null\n    )\n  )\n\n  // delete the invalidation marker last, once we're sure everything is cleaned\n  // up\n  await fs.rm(path.join(cacheDirectory, INVALIDATION_MARKER), {\n    force: true,\n    maxRetries: 2,\n  })\n}\n"], "names": ["fs", "path", "INVALIDATION_MARKER", "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheDirectory", "file", "open", "join", "err", "code", "close", "checkPersistentCacheInvalidationAndCleanup", "invalidated", "access", "then", "cleanupPersistentCache", "cleanupPersistentCacheInner", "e", "Error", "cause", "files", "readdir", "Promise", "all", "map", "name", "rm", "force", "recursive", "maxRetries"], "mappings": "AAAA,OAAOA,QAAQ,mBAAkB;AACjC,OAAOC,UAAU,YAAW;AAE5B,MAAMC,sBAAsB;AAE5B;;;;;;;;;;CAUC,GACD,OAAO,eAAeC,0BAA0BC,cAAsB;IACpE,IAAIC;IACJ,IAAI;QACF,2DAA2D;QAC3DA,OAAO,MAAML,GAAGM,IAAI,CAACL,KAAKM,IAAI,CAACH,gBAAgBF,sBAAsB;IACrE,wEAAwE;IACxE,6CAA6C;IAC/C,EAAE,OAAOM,KAAU;QACjB,+CAA+C;QAC/C,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,MAAMD;QACR;IACF,SAAU;QACRH,wBAAAA,KAAMK,KAAK;IACb;AACF;AAEA;;;CAGC,GACD,OAAO,eAAeC,2CACpBP,cAAsB;IAEtB,MAAMQ,cAAc,MAAMZ,GACvBa,MAAM,CAACZ,KAAKM,IAAI,CAACH,gBAAgBF,sBACjCY,IAAI,CACH,IAAM,MACN,IAAM;IAEV,IAAIF,aAAa;QACf,MAAMG,uBAAuBX;IAC/B;AACF;AAEA;;;;;;;;CAQC,GACD,eAAeW,uBAAuBX,cAAsB;IAC1D,IAAI;QACF,MAAMY,4BAA4BZ;IACpC,EAAE,OAAOa,GAAG;QACV,yCAAyC;QACzC,MAAM,qBAIL,CAJK,IAAIC,MACR,CAAC,sEAAsE,CAAC,GACtE,CAAC,mCAAmC,EAAEd,gBAAgB,EACxD;YAAEe,OAAOF;QAAE,IAHP,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;AACF;AAEA,eAAeD,4BAA4BZ,cAAsB;IAC/D,MAAMgB,QAAQ,MAAMpB,GAAGqB,OAAO,CAACjB;IAE/B,mDAAmD;IACnD,MAAMkB,QAAQC,GAAG,CACfH,MAAMI,GAAG,CAAC,CAACC,OACTA,SAASvB,sBACLF,GAAG0B,EAAE,CAACzB,KAAKM,IAAI,CAACH,gBAAgBqB,OAAO;YACrCE,OAAO;YACPC,WAAW;YACXC,YAAY;QACd,KACA;IAIR,6EAA6E;IAC7E,KAAK;IACL,MAAM7B,GAAG0B,EAAE,CAACzB,KAAKM,IAAI,CAACH,gBAAgBF,sBAAsB;QAC1DyB,OAAO;QACPE,YAAY;IACd;AACF", "ignoreList": [0]}