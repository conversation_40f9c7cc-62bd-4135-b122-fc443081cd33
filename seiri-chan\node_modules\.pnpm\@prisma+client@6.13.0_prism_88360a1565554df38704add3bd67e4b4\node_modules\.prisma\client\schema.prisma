// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Config {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("configs")
}

model TaskBatch {
  id             String   @id @default(cuid())
  name           String
  status         String   @default("pending") // pending|processing|completed|failed|cancelled
  totalTasks     Int      @default(0)
  completedTasks Int      @default(0)
  failedTasks    Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  tasks Task[]

  @@map("task_batches")
}

model Task {
  id           String    @id @default(cuid())
  name         String
  type         String // tv|movie|anime|anime_movie
  status       String    @default("pending") // pending|processing|completed|failed|cancelled
  sourcePath   String
  sourceFiles  Json? // 源文件列表
  metadata     Json? // TMDB元数据
  mapping      Json? // 文件映射关系
  result       Json? // 处理结果
  progress     Float     @default(0)
  errorMessage String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  completedAt  DateTime?

  batchId String?
  batch   TaskBatch? @relation(fields: [batchId], references: [id], onDelete: Cascade)

  logs      TaskLog[]
  testCases AITestCase[]

  @@map("tasks")
}

model TaskLog {
  id        String   @id @default(cuid())
  taskId    String
  level     String // info|warn|error|debug
  message   String
  metadata  Json?
  createdAt DateTime @default(now())

  task Task @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("task_logs")
}

model TMDBCache {
  id        String   @id @default(cuid())
  type      String // tv|movie
  tmdbId    Int
  language  String   @default("zh-CN")
  data      Json
  createdAt DateTime @default(now())
  expiresAt DateTime

  @@unique([type, tmdbId, language])
  @@map("tmdb_cache")
}

model AITestCase {
  id             String   @id @default(cuid())
  name           String
  type           String // tv|movie|anime|anime_movie
  inputData      Json // 输入数据
  expectedOutput Json // 期望输出
  actualOutput   Json? // 实际输出
  score          Float? // 匹配分数
  status         String   @default("pending") // pending|passed|failed
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  taskId String?
  task   Task?   @relation(fields: [taskId], references: [id], onDelete: SetNull)

  @@map("ai_test_cases")
}
