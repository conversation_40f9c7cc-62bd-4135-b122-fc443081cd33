// 核心数据类型定义

export type MediaType = 'tv' | 'movie' | 'anime' | 'anime_movie';
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
export type LogLevel = 'info' | 'warn' | 'error' | 'debug';
export type FileOperationMode = 'hardlink' | 'symlink' | 'copy' | 'move';
export type AIProvider = 'openai' | 'gemini';
export type AIConfidence = 'high' | 'medium' | 'low';

// 任务相关类型
export interface Task {
  id: string;
  name: string;
  type: MediaType;
  status: TaskStatus;
  sourcePath: string;
  sourceFiles?: VideoFile[];
  metadata?: TMDBMetadata;
  mapping?: FileMapping[];
  result?: TaskResult;
  progress: number;
  errorMessage?: string;
  batchId?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface TaskBatch {
  id: string;
  name: string;
  status: TaskStatus;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  createdAt: Date;
  updatedAt: Date;
  tasks?: Task[];
}

export interface TaskLog {
  id: string;
  taskId: string;
  level: LogLevel;
  message: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export interface TaskResult {
  status: 'completed' | 'failed' | 'partial';
  processedFiles: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  operationType: FileOperationMode;
  errors?: TaskError[];
  warnings?: string[];
  duration: number; // 处理耗时（毫秒）
}

export interface TaskError {
  file: string;
  error: string;
  code?: string;
}

// 文件相关类型
export interface VideoFile {
  path: string;
  name: string;
  size: number;
  duration?: number;
  resolution?: string;
  codec?: string;
  bitrate?: number;
  frameRate?: number;
}

export interface FileMapping {
  sourceFile: string;
  targetPath: string;
  season?: number;
  episode?: number;
  episodeType: 'regular' | 'special' | 'movie' | 'trailer' | 'other';
  confidence: AIConfidence;
  method: 'traditional' | 'ai' | 'manual';
}

// TMDB相关类型
export interface TMDBMetadata {
  id: number;
  type: 'tv' | 'movie';
  title: string;
  originalTitle?: string;
  year?: number;
  language: string;
  genres?: string[];
  overview?: string;
  posterPath?: string;
  backdropPath?: string;
  seasons?: TMDBSeason[];
  episodes?: TMDBEpisode[];
  runtime?: number; // 电影时长
  status?: string;
  networks?: string[];
  productionCompanies?: string[];
}

export interface TMDBSeason {
  id: number;
  seasonNumber: number;
  name: string;
  episodeCount: number;
  airDate?: string;
  overview?: string;
  posterPath?: string;
}

export interface TMDBEpisode {
  id: number;
  seasonNumber: number;
  episodeNumber: number;
  name: string;
  airDate?: string;
  runtime?: number;
  overview?: string;
  stillPath?: string;
  voteAverage?: number;
}

// AI相关类型
export interface AIAnalysisResult {
  confidence: AIConfidence;
  reason: string;
  seasonMapping: SeasonMapping[];
  fileMapping: EpisodeMapping[];
  extraNotes?: string;
  provider: AIProvider;
  model: string;
  processingTime: number;
  tokensUsed?: number;
}

export interface SeasonMapping {
  localGroupName: string;
  mapsToTmdbSeasons: number[];
}

export interface EpisodeMapping {
  filePath: string;
  tmdbSeason: number;
  tmdbEpisode: number;
  episodeType: 'regular' | 'special' | 'movie';
  confidence: AIConfidence;
}

// 配置相关类型
export interface AppConfig {
  // TMDB配置
  tmdbApiKey: string;
  tmdbLanguage: string;
  
  // AI配置
  aiEnabled: boolean;
  aiProvider: AIProvider;
  aiConfidenceThreshold: AIConfidence;
  
  // OpenAI配置
  openaiApiKey: string;
  openaiBaseUrl: string;
  openaiModel: string;
  openaiOutputFormat: 'function_calling' | 'json_object' | 'structured_output' | 'text';
  
  // Gemini配置
  geminiApiKey: string;
  geminiBaseUrl: string;
  geminiModel: string;
  
  // 路径配置
  bangumiPath: string;
  moviePath: string;
  animePath: string;
  animeMoviePath: string;
  defaultSourcePath: string;
  
  // 文件操作配置
  fileOperationMode: FileOperationMode;
  
  // 其他配置
  defaultLocale: string;
  maxConcurrentTasks: number;
  logLevel: LogLevel;
  cacheExpiration: number; // TMDB缓存过期时间（小时）
}

// 任务创建参数
export interface CreateTaskParams {
  name: string;
  type: MediaType;
  sourcePath: string;
  options?: TaskOptions;
}

export interface CreateBatchTasksParams {
  name: string;
  paths: string[];
  type: MediaType;
  depth?: number;
  options?: TaskOptions;
}

export interface TaskOptions {
  operationMode?: FileOperationMode;
  useAI?: boolean;
  aiProvider?: AIProvider;
  skipExisting?: boolean;
  dryRun?: boolean;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 搜索和过滤参数
export interface TaskSearchParams {
  page?: number;
  limit?: number;
  status?: TaskStatus | 'all';
  type?: MediaType | 'all';
  batchId?: string;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'progress';
  sortOrder?: 'asc' | 'desc';
}

// 文件系统相关类型
export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modifiedAt?: Date;
  permissions?: {
    readable: boolean;
    writable: boolean;
    executable: boolean;
  };
}

export interface ScanResult {
  path: string;
  totalFiles: number;
  videoFiles: number;
  totalSize: number;
  structure: FileSystemItem[];
  errors?: string[];
}

// 实时事件类型
export interface SSEEvent {
  type: 'task_update' | 'task_log' | 'system_status' | 'batch_update';
  data: any;
  timestamp: string;
}

export interface TaskUpdateEvent {
  taskId: string;
  status: TaskStatus;
  progress: number;
  message?: string;
}

export interface TaskLogEvent {
  taskId: string;
  log: TaskLog;
}

// 统计信息类型
export interface TaskStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  cancelled: number;
  successRate: number;
}

export interface SystemStats {
  tasks: TaskStats;
  storage: {
    totalSpace: number;
    usedSpace: number;
    freeSpace: number;
  };
  performance: {
    avgProcessingTime: number;
    totalProcessedFiles: number;
    cacheHitRate: number;
  };
}
