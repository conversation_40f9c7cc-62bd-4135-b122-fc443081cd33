{"version": 3, "sources": ["../../../src/build/adapter/build-complete.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs/promises'\nimport { promisify } from 'util'\nimport { pathToFileURL } from 'url'\nimport * as Log from '../output/log'\nimport globOriginal from 'next/dist/compiled/glob'\nimport { interopDefault } from '../../lib/interop-default'\nimport type { AdapterOutputs, NextAdapter } from '../../server/config-shared'\nimport {\n  RouteType,\n  type FunctionsConfigManifest,\n  type PrerenderManifest,\n  type RoutesManifest,\n} from '..'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from '../webpack/plugins/middleware-plugin'\nimport { isMiddlewareFilename } from '../utils'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nconst glob = promisify(globOriginal)\n\nexport async function handleBuildComplete({\n  // dir,\n  distDir,\n  tracingRoot,\n  adapterPath,\n  pageKeys,\n  appPageKeys,\n  hasNodeMiddleware,\n  hasInstrumentationHook,\n  requiredServerFiles,\n  routesManifest,\n  // prerenderManifest,\n  middlewareManifest,\n}: {\n  dir: string\n  distDir: string\n  adapterPath: string\n  tracingRoot: string\n  hasNodeMiddleware: boolean\n  pageKeys: readonly string[]\n  hasInstrumentationHook: boolean\n  appPageKeys?: readonly string[] | undefined\n  requiredServerFiles: string[]\n  routesManifest: RoutesManifest\n  prerenderManifest: PrerenderManifest\n  middlewareManifest: MiddlewareManifest\n  functionsConfigManifest: FunctionsConfigManifest\n}) {\n  const adapterMod = interopDefault(\n    await import(pathToFileURL(require.resolve(adapterPath)).href)\n  ) as NextAdapter\n\n  if (typeof adapterMod.onBuildComplete === 'function') {\n    Log.info(`Running onBuildComplete from ${adapterMod.name}`)\n\n    try {\n      const outputs: AdapterOutputs = []\n\n      const staticFiles = await glob('**/*', {\n        cwd: path.join(distDir, 'static'),\n      })\n\n      for (const file of staticFiles) {\n        const pathname = path.posix.join('/_next/static', file)\n        const filePath = path.join(distDir, 'static', file)\n        outputs.push({\n          type: RouteType.STATIC_FILE,\n          id: path.join('static', file),\n          pathname,\n          filePath,\n        })\n      }\n\n      const sharedNodeAssets: Record<string, string> = {}\n\n      for (const file of requiredServerFiles) {\n        // add to shared node assets\n        const filePath = path.join(distDir, file)\n        const fileOutputPath = path.relative(tracingRoot, filePath)\n        sharedNodeAssets[fileOutputPath] = filePath\n      }\n\n      if (hasInstrumentationHook) {\n        const assets = await handleTraceFiles(\n          path.join(distDir, 'server', 'instrumentation.js.nft.json')\n        )\n        const fileOutputPath = path.relative(\n          tracingRoot,\n          path.join(distDir, 'server', 'instrumentation.js')\n        )\n        sharedNodeAssets[fileOutputPath] = path.join(\n          distDir,\n          'server',\n          'instrumentation.js'\n        )\n        Object.assign(sharedNodeAssets, assets)\n      }\n\n      async function handleTraceFiles(\n        traceFilePath: string\n      ): Promise<Record<string, string>> {\n        const assets: Record<string, string> = Object.assign(\n          {},\n          sharedNodeAssets\n        )\n        const traceData = JSON.parse(\n          await fs.readFile(traceFilePath, 'utf8')\n        ) as {\n          files: string[]\n        }\n        const traceFileDir = path.dirname(traceFilePath)\n\n        for (const relativeFile of traceData.files) {\n          const tracedFilePath = path.join(traceFileDir, relativeFile)\n          const fileOutputPath = path.relative(tracingRoot, tracedFilePath)\n          assets[fileOutputPath] = tracedFilePath\n        }\n        return assets\n      }\n\n      async function handleEdgeFunction(\n        page: EdgeFunctionDefinition,\n        isMiddleware: boolean = false\n      ) {\n        let type = RouteType.PAGES\n        const isAppPrefix = page.page.startsWith('app/')\n        const isAppPage = isAppPrefix && page.page.endsWith('/page')\n        const isAppRoute = isAppPrefix && page.page.endsWith('/route')\n\n        if (isMiddleware) {\n          type = RouteType.MIDDLEWARE\n        } else if (isAppPage) {\n          type = RouteType.APP_PAGE\n        } else if (isAppRoute) {\n          type = RouteType.APP_ROUTE\n        } else if (page.page.startsWith('/api')) {\n          type = RouteType.PAGES_API\n        }\n\n        const output: AdapterOutputs[0] = {\n          id: page.name,\n          runtime: 'edge',\n          pathname: isAppPrefix ? normalizeAppPath(page.name) : page.name,\n          filePath: path.join(\n            distDir,\n            'server',\n            page.files.find(\n              (item) =>\n                item.startsWith('server/app') || item.startsWith('server/pages')\n            ) || ''\n          ),\n          assets: {},\n          type,\n        }\n\n        function handleFile(file: string) {\n          const originalPath = path.join(distDir, file)\n          const fileOutputPath = path.join(\n            path.relative(tracingRoot, distDir),\n            file\n          )\n          if (!output.assets) {\n            output.assets = {}\n          }\n          output.assets[fileOutputPath] = originalPath\n        }\n        for (const file of page.files) {\n          handleFile(file)\n        }\n        for (const item of [...(page.wasm || []), ...(page.assets || [])]) {\n          handleFile(item.filePath)\n        }\n        outputs.push(output)\n      }\n\n      const edgeFunctionHandlers: Promise<any>[] = []\n\n      for (const middleware of Object.values(middlewareManifest.middleware)) {\n        if (isMiddlewareFilename(middleware.name)) {\n          edgeFunctionHandlers.push(handleEdgeFunction(middleware, true))\n        }\n      }\n\n      for (const page of Object.values(middlewareManifest.functions)) {\n        edgeFunctionHandlers.push(handleEdgeFunction(page))\n      }\n\n      for (const page of pageKeys) {\n        if (middlewareManifest.functions.hasOwnProperty(page)) {\n          continue\n        }\n        const route = normalizePagePath(page)\n\n        const pageFile = path.join(\n          distDir,\n          'server',\n          'pages',\n          `${normalizePagePath(page)}.js`\n        )\n        const pageTraceFile = `${pageFile}.nft.json`\n        const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n          if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n          }\n          return {} as Record<string, string>\n        })\n\n        outputs.push({\n          id: route,\n          type: page.startsWith('/api') ? RouteType.PAGES_API : RouteType.PAGES,\n          filePath: pageTraceFile.replace(/\\.nft\\.json$/, ''),\n          pathname: route,\n          assets,\n          runtime: 'nodejs',\n        })\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n        const middlewareTrace = `${middlewareFile}.nft.json`\n        const assets = await handleTraceFiles(middlewareTrace)\n\n        outputs.push({\n          pathname: '/_middleware',\n          id: '/_middleware',\n          assets,\n          type: RouteType.MIDDLEWARE,\n          runtime: 'nodejs',\n          filePath: middlewareFile,\n        })\n      }\n\n      if (appPageKeys) {\n        for (const page of appPageKeys) {\n          if (middlewareManifest.functions.hasOwnProperty(page)) {\n            continue\n          }\n          const normalizedPage = normalizeAppPath(page)\n          const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n          const pageTraceFile = `${pageFile}.nft.json`\n          const assets = await handleTraceFiles(pageTraceFile).catch((err) => {\n            Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n            return {} as Record<string, string>\n          })\n\n          outputs.push({\n            pathname: normalizedPage,\n            id: normalizedPage,\n            assets,\n            type: page.endsWith('/route')\n              ? RouteType.APP_ROUTE\n              : RouteType.APP_PAGE,\n            runtime: 'nodejs',\n            filePath: pageFile,\n          })\n        }\n      }\n\n      // TODO: prerender assets\n\n      await adapterMod.onBuildComplete({\n        routes: {\n          dynamicRoutes: routesManifest.dynamicRoutes,\n          rewrites: routesManifest.rewrites,\n          redirects: routesManifest.redirects,\n          headers: routesManifest.headers,\n        },\n        outputs,\n      })\n    } catch (err) {\n      Log.error(`Failed to run onBuildComplete from ${adapterMod.name}`)\n      throw err\n    }\n  }\n}\n"], "names": ["path", "fs", "promisify", "pathToFileURL", "Log", "globOriginal", "interopDefault", "RouteType", "isMiddlewareFilename", "normalizePagePath", "normalizeAppPath", "glob", "handleBuildComplete", "distDir", "tracingRoot", "adapterPath", "pageKeys", "appPageKeys", "hasNodeMiddleware", "hasInstrumentationHook", "requiredServerFiles", "routesManifest", "middlewareManifest", "adapterMod", "require", "resolve", "href", "onBuildComplete", "info", "name", "outputs", "staticFiles", "cwd", "join", "file", "pathname", "posix", "filePath", "push", "type", "STATIC_FILE", "id", "sharedNodeAssets", "fileOutputPath", "relative", "assets", "handleTraceFiles", "Object", "assign", "traceFilePath", "traceData", "JSON", "parse", "readFile", "traceFileDir", "dirname", "relativeFile", "files", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEdgeFunction", "page", "isMiddleware", "PAGES", "isAppPrefix", "startsWith", "isAppPage", "endsWith", "isAppRoute", "MIDDLEWARE", "APP_PAGE", "APP_ROUTE", "PAGES_API", "output", "runtime", "find", "item", "handleFile", "originalPath", "wasm", "edgeFunctionHandlers", "middleware", "values", "functions", "hasOwnProperty", "route", "pageFile", "pageTraceFile", "catch", "err", "code", "warn", "replace", "middlewareFile", "middlewareTrace", "normalizedPage", "routes", "dynamicRoutes", "rewrites", "redirects", "headers", "error"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,cAAa;AAC5B,SAASC,SAAS,QAAQ,OAAM;AAChC,SAASC,aAAa,QAAQ,MAAK;AACnC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,kBAAkB,0BAAyB;AAClD,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SACEC,SAAS,QAIJ,KAAI;AAKX,SAASC,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,gBAAgB,QAAQ,0CAAyC;AAE1E,MAAMC,OAAOT,UAAUG;AAEvB,OAAO,eAAeO,oBAAoB,EACxC,OAAO;AACPC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,WAAW,EACXC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,cAAc,EACd,qBAAqB;AACrBC,kBAAkB,EAenB;IACC,MAAMC,aAAajB,eACjB,MAAM,MAAM,CAACH,cAAcqB,QAAQC,OAAO,CAACV,cAAcW,IAAI;IAG/D,IAAI,OAAOH,WAAWI,eAAe,KAAK,YAAY;QACpDvB,IAAIwB,IAAI,CAAC,CAAC,6BAA6B,EAAEL,WAAWM,IAAI,EAAE;QAE1D,IAAI;YACF,MAAMC,UAA0B,EAAE;YAElC,MAAMC,cAAc,MAAMpB,KAAK,QAAQ;gBACrCqB,KAAKhC,KAAKiC,IAAI,CAACpB,SAAS;YAC1B;YAEA,KAAK,MAAMqB,QAAQH,YAAa;gBAC9B,MAAMI,WAAWnC,KAAKoC,KAAK,CAACH,IAAI,CAAC,iBAAiBC;gBAClD,MAAMG,WAAWrC,KAAKiC,IAAI,CAACpB,SAAS,UAAUqB;gBAC9CJ,QAAQQ,IAAI,CAAC;oBACXC,MAAMhC,UAAUiC,WAAW;oBAC3BC,IAAIzC,KAAKiC,IAAI,CAAC,UAAUC;oBACxBC;oBACAE;gBACF;YACF;YAEA,MAAMK,mBAA2C,CAAC;YAElD,KAAK,MAAMR,QAAQd,oBAAqB;gBACtC,4BAA4B;gBAC5B,MAAMiB,WAAWrC,KAAKiC,IAAI,CAACpB,SAASqB;gBACpC,MAAMS,iBAAiB3C,KAAK4C,QAAQ,CAAC9B,aAAauB;gBAClDK,gBAAgB,CAACC,eAAe,GAAGN;YACrC;YAEA,IAAIlB,wBAAwB;gBAC1B,MAAM0B,SAAS,MAAMC,iBACnB9C,KAAKiC,IAAI,CAACpB,SAAS,UAAU;gBAE/B,MAAM8B,iBAAiB3C,KAAK4C,QAAQ,CAClC9B,aACAd,KAAKiC,IAAI,CAACpB,SAAS,UAAU;gBAE/B6B,gBAAgB,CAACC,eAAe,GAAG3C,KAAKiC,IAAI,CAC1CpB,SACA,UACA;gBAEFkC,OAAOC,MAAM,CAACN,kBAAkBG;YAClC;YAEA,eAAeC,iBACbG,aAAqB;gBAErB,MAAMJ,SAAiCE,OAAOC,MAAM,CAClD,CAAC,GACDN;gBAEF,MAAMQ,YAAYC,KAAKC,KAAK,CAC1B,MAAMnD,GAAGoD,QAAQ,CAACJ,eAAe;gBAInC,MAAMK,eAAetD,KAAKuD,OAAO,CAACN;gBAElC,KAAK,MAAMO,gBAAgBN,UAAUO,KAAK,CAAE;oBAC1C,MAAMC,iBAAiB1D,KAAKiC,IAAI,CAACqB,cAAcE;oBAC/C,MAAMb,iBAAiB3C,KAAK4C,QAAQ,CAAC9B,aAAa4C;oBAClDb,MAAM,CAACF,eAAe,GAAGe;gBAC3B;gBACA,OAAOb;YACT;YAEA,eAAec,mBACbC,IAA4B,EAC5BC,eAAwB,KAAK;gBAE7B,IAAItB,OAAOhC,UAAUuD,KAAK;gBAC1B,MAAMC,cAAcH,KAAKA,IAAI,CAACI,UAAU,CAAC;gBACzC,MAAMC,YAAYF,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBACpD,MAAMC,aAAaJ,eAAeH,KAAKA,IAAI,CAACM,QAAQ,CAAC;gBAErD,IAAIL,cAAc;oBAChBtB,OAAOhC,UAAU6D,UAAU;gBAC7B,OAAO,IAAIH,WAAW;oBACpB1B,OAAOhC,UAAU8D,QAAQ;gBAC3B,OAAO,IAAIF,YAAY;oBACrB5B,OAAOhC,UAAU+D,SAAS;gBAC5B,OAAO,IAAIV,KAAKA,IAAI,CAACI,UAAU,CAAC,SAAS;oBACvCzB,OAAOhC,UAAUgE,SAAS;gBAC5B;gBAEA,MAAMC,SAA4B;oBAChC/B,IAAImB,KAAK/B,IAAI;oBACb4C,SAAS;oBACTtC,UAAU4B,cAAcrD,iBAAiBkD,KAAK/B,IAAI,IAAI+B,KAAK/B,IAAI;oBAC/DQ,UAAUrC,KAAKiC,IAAI,CACjBpB,SACA,UACA+C,KAAKH,KAAK,CAACiB,IAAI,CACb,CAACC,OACCA,KAAKX,UAAU,CAAC,iBAAiBW,KAAKX,UAAU,CAAC,oBAChD;oBAEPnB,QAAQ,CAAC;oBACTN;gBACF;gBAEA,SAASqC,WAAW1C,IAAY;oBAC9B,MAAM2C,eAAe7E,KAAKiC,IAAI,CAACpB,SAASqB;oBACxC,MAAMS,iBAAiB3C,KAAKiC,IAAI,CAC9BjC,KAAK4C,QAAQ,CAAC9B,aAAaD,UAC3BqB;oBAEF,IAAI,CAACsC,OAAO3B,MAAM,EAAE;wBAClB2B,OAAO3B,MAAM,GAAG,CAAC;oBACnB;oBACA2B,OAAO3B,MAAM,CAACF,eAAe,GAAGkC;gBAClC;gBACA,KAAK,MAAM3C,QAAQ0B,KAAKH,KAAK,CAAE;oBAC7BmB,WAAW1C;gBACb;gBACA,KAAK,MAAMyC,QAAQ;uBAAKf,KAAKkB,IAAI,IAAI,EAAE;uBAAOlB,KAAKf,MAAM,IAAI,EAAE;iBAAE,CAAE;oBACjE+B,WAAWD,KAAKtC,QAAQ;gBAC1B;gBACAP,QAAQQ,IAAI,CAACkC;YACf;YAEA,MAAMO,uBAAuC,EAAE;YAE/C,KAAK,MAAMC,cAAcjC,OAAOkC,MAAM,CAAC3D,mBAAmB0D,UAAU,EAAG;gBACrE,IAAIxE,qBAAqBwE,WAAWnD,IAAI,GAAG;oBACzCkD,qBAAqBzC,IAAI,CAACqB,mBAAmBqB,YAAY;gBAC3D;YACF;YAEA,KAAK,MAAMpB,QAAQb,OAAOkC,MAAM,CAAC3D,mBAAmB4D,SAAS,EAAG;gBAC9DH,qBAAqBzC,IAAI,CAACqB,mBAAmBC;YAC/C;YAEA,KAAK,MAAMA,QAAQ5C,SAAU;gBAC3B,IAAIM,mBAAmB4D,SAAS,CAACC,cAAc,CAACvB,OAAO;oBACrD;gBACF;gBACA,MAAMwB,QAAQ3E,kBAAkBmD;gBAEhC,MAAMyB,WAAWrF,KAAKiC,IAAI,CACxBpB,SACA,UACA,SACA,GAAGJ,kBAAkBmD,MAAM,GAAG,CAAC;gBAEjC,MAAM0B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;gBAC5C,MAAMxC,SAAS,MAAMC,iBAAiBwC,eAAeC,KAAK,CAAC,CAACC;oBAC1D,IAAIA,IAAIC,IAAI,KAAK,YAAa7B,SAAS,UAAUA,SAAS,QAAS;wBACjExD,IAAIsF,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;oBAC1D;oBACA,OAAO,CAAC;gBACV;gBAEA1D,QAAQQ,IAAI,CAAC;oBACXG,IAAI2C;oBACJ7C,MAAMqB,KAAKI,UAAU,CAAC,UAAUzD,UAAUgE,SAAS,GAAGhE,UAAUuD,KAAK;oBACrEzB,UAAUiD,cAAcK,OAAO,CAAC,gBAAgB;oBAChDxD,UAAUiD;oBACVvC;oBACA4B,SAAS;gBACX;YACF;YAEA,IAAIvD,mBAAmB;gBACrB,MAAM0E,iBAAiB5F,KAAKiC,IAAI,CAACpB,SAAS,UAAU;gBACpD,MAAMgF,kBAAkB,GAAGD,eAAe,SAAS,CAAC;gBACpD,MAAM/C,SAAS,MAAMC,iBAAiB+C;gBAEtC/D,QAAQQ,IAAI,CAAC;oBACXH,UAAU;oBACVM,IAAI;oBACJI;oBACAN,MAAMhC,UAAU6D,UAAU;oBAC1BK,SAAS;oBACTpC,UAAUuD;gBACZ;YACF;YAEA,IAAI3E,aAAa;gBACf,KAAK,MAAM2C,QAAQ3C,YAAa;oBAC9B,IAAIK,mBAAmB4D,SAAS,CAACC,cAAc,CAACvB,OAAO;wBACrD;oBACF;oBACA,MAAMkC,iBAAiBpF,iBAAiBkD;oBACxC,MAAMyB,WAAWrF,KAAKiC,IAAI,CAACpB,SAAS,UAAU,OAAO,GAAG+C,KAAK,GAAG,CAAC;oBACjE,MAAM0B,gBAAgB,GAAGD,SAAS,SAAS,CAAC;oBAC5C,MAAMxC,SAAS,MAAMC,iBAAiBwC,eAAeC,KAAK,CAAC,CAACC;wBAC1DpF,IAAIsF,IAAI,CAAC,CAAC,gCAAgC,EAAEL,UAAU,EAAEG;wBACxD,OAAO,CAAC;oBACV;oBAEA1D,QAAQQ,IAAI,CAAC;wBACXH,UAAU2D;wBACVrD,IAAIqD;wBACJjD;wBACAN,MAAMqB,KAAKM,QAAQ,CAAC,YAChB3D,UAAU+D,SAAS,GACnB/D,UAAU8D,QAAQ;wBACtBI,SAAS;wBACTpC,UAAUgD;oBACZ;gBACF;YACF;YAEA,yBAAyB;YAEzB,MAAM9D,WAAWI,eAAe,CAAC;gBAC/BoE,QAAQ;oBACNC,eAAe3E,eAAe2E,aAAa;oBAC3CC,UAAU5E,eAAe4E,QAAQ;oBACjCC,WAAW7E,eAAe6E,SAAS;oBACnCC,SAAS9E,eAAe8E,OAAO;gBACjC;gBACArE;YACF;QACF,EAAE,OAAO0D,KAAK;YACZpF,IAAIgG,KAAK,CAAC,CAAC,mCAAmC,EAAE7E,WAAWM,IAAI,EAAE;YACjE,MAAM2D;QACR;IACF;AACF", "ignoreList": [0]}