import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/routing';

export default function Home() {
  const t = useTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Seiri-chan
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            基于TMDB元数据的智能动漫BD整理工具，支持传统识别和AI增强识别
          </p>
          <div className="flex gap-4 justify-center">
            <Link
              href="/tasks"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              {t('nav.tasks')}
            </Link>
            <Link
              href="/config"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              {t('nav.config')}
            </Link>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              智能识别
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              结合传统规则引擎和AI增强识别，准确识别动漫、电视剧和电影文件
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              批量处理
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              支持批量任务处理、深度扫描和实时状态跟踪
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              灵活整理
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              支持硬链接、软链接、复制、移动等多种文件操作方式
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
