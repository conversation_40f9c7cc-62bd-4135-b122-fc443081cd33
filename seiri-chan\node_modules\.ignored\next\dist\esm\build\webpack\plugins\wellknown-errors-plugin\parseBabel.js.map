{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts"], "sourcesContent": ["import { bold, cyan, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nexport function getBabelError(\n  fileName: string,\n  err: Error & {\n    code?: string | number\n    loc?: { line: number; column: number }\n  }\n): SimpleWebpackError | false {\n  if (err.code !== 'BABEL_PARSE_ERROR') {\n    return false\n  }\n\n  // https://github.com/babel/babel/blob/34693d6024da3f026534dd8d569f97ac0109602e/packages/babel-core/src/parser/index.js\n  if (err.loc) {\n    const lineNumber = Math.max(1, err.loc.line)\n    const column = Math.max(1, err.loc.column)\n\n    let message = err.message\n      // Remove file information, which instead is provided by webpack.\n      .replace(/^.+?: /, '')\n      // Remove column information from message\n      .replace(\n        new RegExp(`[^\\\\S\\\\r\\\\n]*\\\\(${lineNumber}:${column}\\\\)[^\\\\S\\\\r\\\\n]*`),\n        ''\n      )\n\n    return new SimpleWebpackError(\n      `${cyan(fileName)}:${yellow(lineNumber.toString())}:${yellow(\n        column.toString()\n      )}`,\n      red(bold('Syntax error')).concat(`: ${message}`)\n    )\n  }\n\n  return false\n}\n"], "names": ["bold", "cyan", "red", "yellow", "SimpleWebpackError", "getBabelError", "fileName", "err", "code", "loc", "lineNumber", "Math", "max", "line", "column", "message", "replace", "RegExp", "toString", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AACpE,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,OAAO,SAASC,cACdC,QAAgB,EAChBC,GAGC;IAED,IAAIA,IAAIC,IAAI,KAAK,qBAAqB;QACpC,OAAO;IACT;IAEA,uHAAuH;IACvH,IAAID,IAAIE,GAAG,EAAE;QACX,MAAMC,aAAaC,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACI,IAAI;QAC3C,MAAMC,SAASH,KAAKC,GAAG,CAAC,GAAGL,IAAIE,GAAG,CAACK,MAAM;QAEzC,IAAIC,UAAUR,IAAIQ,OAAO,AACvB,iEAAiE;SAChEC,OAAO,CAAC,UAAU,GACnB,yCAAyC;SACxCA,OAAO,CACN,IAAIC,OAAO,CAAC,gBAAgB,EAAEP,WAAW,CAAC,EAAEI,OAAO,gBAAgB,CAAC,GACpE;QAGJ,OAAO,IAAIV,mBACT,GAAGH,KAAKK,UAAU,CAAC,EAAEH,OAAOO,WAAWQ,QAAQ,IAAI,CAAC,EAAEf,OACpDW,OAAOI,QAAQ,KACd,EACHhB,IAAIF,KAAK,iBAAiBmB,MAAM,CAAC,CAAC,EAAE,EAAEJ,SAAS;IAEnD;IAEA,OAAO;AACT", "ignoreList": [0]}