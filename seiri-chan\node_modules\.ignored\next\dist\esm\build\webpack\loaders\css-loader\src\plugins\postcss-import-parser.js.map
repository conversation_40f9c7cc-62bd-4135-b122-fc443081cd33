{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/postcss-import-parser.ts"], "sourcesContent": ["import valueParser from 'next/dist/compiled/postcss-value-parser'\n\nimport {\n  normalizeUrl,\n  resolveRequests,\n  isUrlRequestable,\n  requestify,\n  // @ts-expect-error TODO: this export doesn't exist? Double check.\n  WEBPACK_IGNORE_COMMENT_REGEXP,\n} from '../utils'\n\nfunction parseNode(atRule: any, key: any) {\n  // Convert only top-level @import\n  if (atRule.parent.type !== 'root') {\n    return\n  }\n\n  if (\n    atRule.raws &&\n    atRule.raws.afterName &&\n    atRule.raws.afterName.trim().length > 0\n  ) {\n    const lastCommentIndex = atRule.raws.afterName.lastIndexOf('/*')\n    const matched = atRule.raws.afterName\n      .slice(lastCommentIndex)\n      .match(WEBPACK_IGNORE_COMMENT_REGEXP)\n\n    if (matched && matched[2] === 'true') {\n      return\n    }\n  }\n\n  const prevNode = atRule.prev()\n\n  if (prevNode && prevNode.type === 'comment') {\n    const matched = prevNode.text.match(WEBPACK_IGNORE_COMMENT_REGEXP)\n\n    if (matched && matched[2] === 'true') {\n      return\n    }\n  }\n\n  // Nodes do not exists - `@import url('http://') :root {}`\n  if (atRule.nodes) {\n    const error: any = new Error(\n      \"It looks like you didn't end your @import statement correctly. Child nodes are attached to it.\"\n    )\n\n    error.node = atRule\n\n    throw error\n  }\n\n  const { nodes: paramsNodes } = valueParser(atRule[key])\n\n  // No nodes - `@import ;`\n  // Invalid type - `@import foo-bar;`\n  if (\n    paramsNodes.length === 0 ||\n    (paramsNodes[0].type !== 'string' && paramsNodes[0].type !== 'function')\n  ) {\n    const error: any = new Error(`Unable to find uri in \"${atRule.toString()}\"`)\n\n    error.node = atRule\n\n    throw error\n  }\n\n  let isStringValue\n  let url: any\n\n  if (paramsNodes[0].type === 'string') {\n    isStringValue = true\n    url = paramsNodes[0].value\n  } else {\n    // Invalid function - `@import nourl(test.css);`\n    if (paramsNodes[0].value.toLowerCase() !== 'url') {\n      const error: any = new Error(\n        `Unable to find uri in \"${atRule.toString()}\"`\n      )\n\n      error.node = atRule\n\n      throw error\n    }\n\n    isStringValue =\n      paramsNodes[0].nodes.length !== 0 &&\n      paramsNodes[0].nodes[0].type === 'string'\n    url = isStringValue\n      ? paramsNodes[0].nodes[0].value\n      : valueParser.stringify(paramsNodes[0].nodes)\n  }\n\n  url = normalizeUrl(url, isStringValue)\n\n  const isRequestable = isUrlRequestable(url)\n  let prefix\n\n  if (isRequestable) {\n    const queryParts = url.split('!')\n\n    if (queryParts.length > 1) {\n      url = queryParts.pop()\n      prefix = queryParts.join('!')\n    }\n  }\n\n  // Empty url - `@import \"\";` or `@import url();`\n  if (url.trim().length === 0) {\n    const error: any = new Error(`Unable to find uri in \"${atRule.toString()}\"`)\n\n    error.node = atRule\n\n    throw error\n  }\n\n  const mediaNodes = paramsNodes.slice(1)\n  let media\n\n  if (mediaNodes.length > 0) {\n    media = valueParser.stringify(mediaNodes).trim().toLowerCase()\n  }\n\n  // eslint-disable-next-line consistent-return\n  return { atRule, prefix, url, media, isRequestable }\n}\n\nconst plugin = (options: any = {}) => {\n  return {\n    postcssPlugin: 'postcss-import-parser',\n    prepare(result: any) {\n      const parsedAtRules: any[] = []\n\n      return {\n        AtRule: {\n          import(atRule: any) {\n            let parsedAtRule\n\n            try {\n              // @ts-expect-error TODO: there is no third argument?\n              parsedAtRule = parseNode(atRule, 'params', result)\n            } catch (error: any) {\n              result.warn(error.message, { node: error.node })\n            }\n\n            if (!parsedAtRule) {\n              return\n            }\n\n            parsedAtRules.push(parsedAtRule)\n          },\n        },\n        async OnceExit() {\n          if (parsedAtRules.length === 0) {\n            return\n          }\n\n          const resolvedAtRules = await Promise.all(\n            parsedAtRules.map(async (parsedAtRule) => {\n              const { atRule, isRequestable, prefix, url, media } = parsedAtRule\n\n              if (options.filter) {\n                const needKeep = await options.filter(url, media)\n\n                if (!needKeep) {\n                  return\n                }\n              }\n\n              if (isRequestable) {\n                const request = requestify(url, options.rootContext)\n\n                const { resolver, context } = options\n                const resolvedUrl = await resolveRequests(resolver, context, [\n                  ...new Set([request, url]),\n                ])\n\n                if (!resolvedUrl) {\n                  return\n                }\n\n                if (resolvedUrl === options.resourcePath) {\n                  atRule.remove()\n\n                  return\n                }\n\n                atRule.remove()\n\n                // eslint-disable-next-line consistent-return\n                return { url: resolvedUrl, media, prefix, isRequestable }\n              }\n\n              atRule.remove()\n\n              // eslint-disable-next-line consistent-return\n              return { url, media, prefix, isRequestable }\n            })\n          )\n\n          const urlToNameMap = new Map()\n\n          for (let index = 0; index <= resolvedAtRules.length - 1; index++) {\n            const resolvedAtRule = resolvedAtRules[index]\n\n            if (!resolvedAtRule) {\n              // eslint-disable-next-line no-continue\n              continue\n            }\n\n            const { url, isRequestable, media } = resolvedAtRule\n\n            if (!isRequestable) {\n              options.api.push({ url, media, index })\n\n              // eslint-disable-next-line no-continue\n              continue\n            }\n\n            const { prefix } = resolvedAtRule\n            const newUrl = prefix ? `${prefix}!${url}` : url\n            let importName = urlToNameMap.get(newUrl)\n\n            if (!importName) {\n              importName = `___CSS_LOADER_AT_RULE_IMPORT_${urlToNameMap.size}___`\n              urlToNameMap.set(newUrl, importName)\n\n              options.imports.push({\n                type: 'rule_import',\n                importName,\n                url: options.urlHandler(newUrl),\n                index,\n              })\n            }\n\n            options.api.push({ importName, media, index })\n          }\n        },\n      }\n    },\n  }\n}\n\nplugin.postcss = true\n\nexport default plugin\n"], "names": ["valueParser", "normalizeUrl", "resolveRequests", "isUrlRequestable", "requestify", "WEBPACK_IGNORE_COMMENT_REGEXP", "parseNode", "atRule", "key", "parent", "type", "raws", "after<PERSON>ame", "trim", "length", "lastCommentIndex", "lastIndexOf", "matched", "slice", "match", "prevNode", "prev", "text", "nodes", "error", "Error", "node", "paramsNodes", "toString", "isStringValue", "url", "value", "toLowerCase", "stringify", "isRequestable", "prefix", "queryParts", "split", "pop", "join", "mediaNodes", "media", "plugin", "options", "postcssPlugin", "prepare", "result", "parsedAtRules", "AtRule", "import", "parsedAtRule", "warn", "message", "push", "OnceExit", "resolvedAtRules", "Promise", "all", "map", "filter", "<PERSON><PERSON><PERSON>", "request", "rootContext", "resolver", "context", "resolvedUrl", "Set", "resourcePath", "remove", "urlToNameMap", "Map", "index", "resolvedAtRule", "api", "newUrl", "importName", "get", "size", "set", "imports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postcss"], "mappings": "AAAA,OAAOA,iBAAiB,0CAAyC;AAEjE,SACEC,YAAY,EACZC,eAAe,EACfC,gBAAgB,EAChBC,UAAU,EAEVC,AADA,kEAAkE;AAClEA,6BAA6B,QACxB,WAAU;AAEjB,SAASC,UAAUC,MAAW,EAAEC,GAAQ;IACtC,iCAAiC;IACjC,IAAID,OAAOE,MAAM,CAACC,IAAI,KAAK,QAAQ;QACjC;IACF;IAEA,IACEH,OAAOI,IAAI,IACXJ,OAAOI,IAAI,CAACC,SAAS,IACrBL,OAAOI,IAAI,CAACC,SAAS,CAACC,IAAI,GAAGC,MAAM,GAAG,GACtC;QACA,MAAMC,mBAAmBR,OAAOI,IAAI,CAACC,SAAS,CAACI,WAAW,CAAC;QAC3D,MAAMC,UAAUV,OAAOI,IAAI,CAACC,SAAS,CAClCM,KAAK,CAACH,kBACNI,KAAK,CAACd;QAET,IAAIY,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,MAAMG,WAAWb,OAAOc,IAAI;IAE5B,IAAID,YAAYA,SAASV,IAAI,KAAK,WAAW;QAC3C,MAAMO,UAAUG,SAASE,IAAI,CAACH,KAAK,CAACd;QAEpC,IAAIY,WAAWA,OAAO,CAAC,EAAE,KAAK,QAAQ;YACpC;QACF;IACF;IAEA,0DAA0D;IAC1D,IAAIV,OAAOgB,KAAK,EAAE;QAChB,MAAMC,QAAa,qBAElB,CAFkB,IAAIC,MACrB,mGADiB,qBAAA;mBAAA;wBAAA;0BAAA;QAEnB;QAEAD,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,MAAM,EAAED,OAAOI,WAAW,EAAE,GAAG3B,YAAYO,MAAM,CAACC,IAAI;IAEtD,yBAAyB;IACzB,oCAAoC;IACpC,IACEmB,YAAYb,MAAM,KAAK,KACtBa,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,YAAYiB,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,YAC7D;QACA,MAAMc,QAAa,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;QAE3EJ,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,IAAIK;IACJ,IAAIC;IAEJ,IAAIH,WAAW,CAAC,EAAE,CAACjB,IAAI,KAAK,UAAU;QACpCmB,gBAAgB;QAChBC,MAAMH,WAAW,CAAC,EAAE,CAACI,KAAK;IAC5B,OAAO;QACL,gDAAgD;QAChD,IAAIJ,WAAW,CAAC,EAAE,CAACI,KAAK,CAACC,WAAW,OAAO,OAAO;YAChD,MAAMR,QAAa,qBAElB,CAFkB,IAAIC,MACrB,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC,GAD7B,qBAAA;uBAAA;4BAAA;8BAAA;YAEnB;YAEAJ,MAAME,IAAI,GAAGnB;YAEb,MAAMiB;QACR;QAEAK,gBACEF,WAAW,CAAC,EAAE,CAACJ,KAAK,CAACT,MAAM,KAAK,KAChCa,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACb,IAAI,KAAK;QACnCoB,MAAMD,gBACFF,WAAW,CAAC,EAAE,CAACJ,KAAK,CAAC,EAAE,CAACQ,KAAK,GAC7B/B,YAAYiC,SAAS,CAACN,WAAW,CAAC,EAAE,CAACJ,KAAK;IAChD;IAEAO,MAAM7B,aAAa6B,KAAKD;IAExB,MAAMK,gBAAgB/B,iBAAiB2B;IACvC,IAAIK;IAEJ,IAAID,eAAe;QACjB,MAAME,aAAaN,IAAIO,KAAK,CAAC;QAE7B,IAAID,WAAWtB,MAAM,GAAG,GAAG;YACzBgB,MAAMM,WAAWE,GAAG;YACpBH,SAASC,WAAWG,IAAI,CAAC;QAC3B;IACF;IAEA,gDAAgD;IAChD,IAAIT,IAAIjB,IAAI,GAAGC,MAAM,KAAK,GAAG;QAC3B,MAAMU,QAAa,qBAAyD,CAAzD,IAAIC,MAAM,CAAC,uBAAuB,EAAElB,OAAOqB,QAAQ,GAAG,CAAC,CAAC,GAAxD,qBAAA;mBAAA;wBAAA;0BAAA;QAAwD;QAE3EJ,MAAME,IAAI,GAAGnB;QAEb,MAAMiB;IACR;IAEA,MAAMgB,aAAab,YAAYT,KAAK,CAAC;IACrC,IAAIuB;IAEJ,IAAID,WAAW1B,MAAM,GAAG,GAAG;QACzB2B,QAAQzC,YAAYiC,SAAS,CAACO,YAAY3B,IAAI,GAAGmB,WAAW;IAC9D;IAEA,6CAA6C;IAC7C,OAAO;QAAEzB;QAAQ4B;QAAQL;QAAKW;QAAOP;IAAc;AACrD;AAEA,MAAMQ,SAAS,CAACC,UAAe,CAAC,CAAC;IAC/B,OAAO;QACLC,eAAe;QACfC,SAAQC,MAAW;YACjB,MAAMC,gBAAuB,EAAE;YAE/B,OAAO;gBACLC,QAAQ;oBACNC,QAAO1C,MAAW;wBAChB,IAAI2C;wBAEJ,IAAI;4BACF,qDAAqD;4BACrDA,eAAe5C,UAAUC,QAAQ,UAAUuC;wBAC7C,EAAE,OAAOtB,OAAY;4BACnBsB,OAAOK,IAAI,CAAC3B,MAAM4B,OAAO,EAAE;gCAAE1B,MAAMF,MAAME,IAAI;4BAAC;wBAChD;wBAEA,IAAI,CAACwB,cAAc;4BACjB;wBACF;wBAEAH,cAAcM,IAAI,CAACH;oBACrB;gBACF;gBACA,MAAMI;oBACJ,IAAIP,cAAcjC,MAAM,KAAK,GAAG;wBAC9B;oBACF;oBAEA,MAAMyC,kBAAkB,MAAMC,QAAQC,GAAG,CACvCV,cAAcW,GAAG,CAAC,OAAOR;wBACvB,MAAM,EAAE3C,MAAM,EAAE2B,aAAa,EAAEC,MAAM,EAAEL,GAAG,EAAEW,KAAK,EAAE,GAAGS;wBAEtD,IAAIP,QAAQgB,MAAM,EAAE;4BAClB,MAAMC,WAAW,MAAMjB,QAAQgB,MAAM,CAAC7B,KAAKW;4BAE3C,IAAI,CAACmB,UAAU;gCACb;4BACF;wBACF;wBAEA,IAAI1B,eAAe;4BACjB,MAAM2B,UAAUzD,WAAW0B,KAAKa,QAAQmB,WAAW;4BAEnD,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE,GAAGrB;4BAC9B,MAAMsB,cAAc,MAAM/D,gBAAgB6D,UAAUC,SAAS;mCACxD,IAAIE,IAAI;oCAACL;oCAAS/B;iCAAI;6BAC1B;4BAED,IAAI,CAACmC,aAAa;gCAChB;4BACF;4BAEA,IAAIA,gBAAgBtB,QAAQwB,YAAY,EAAE;gCACxC5D,OAAO6D,MAAM;gCAEb;4BACF;4BAEA7D,OAAO6D,MAAM;4BAEb,6CAA6C;4BAC7C,OAAO;gCAAEtC,KAAKmC;gCAAaxB;gCAAON;gCAAQD;4BAAc;wBAC1D;wBAEA3B,OAAO6D,MAAM;wBAEb,6CAA6C;wBAC7C,OAAO;4BAAEtC;4BAAKW;4BAAON;4BAAQD;wBAAc;oBAC7C;oBAGF,MAAMmC,eAAe,IAAIC;oBAEzB,IAAK,IAAIC,QAAQ,GAAGA,SAAShB,gBAAgBzC,MAAM,GAAG,GAAGyD,QAAS;wBAChE,MAAMC,iBAAiBjB,eAAe,CAACgB,MAAM;wBAE7C,IAAI,CAACC,gBAAgB;4BAEnB;wBACF;wBAEA,MAAM,EAAE1C,GAAG,EAAEI,aAAa,EAAEO,KAAK,EAAE,GAAG+B;wBAEtC,IAAI,CAACtC,eAAe;4BAClBS,QAAQ8B,GAAG,CAACpB,IAAI,CAAC;gCAAEvB;gCAAKW;gCAAO8B;4BAAM;4BAGrC;wBACF;wBAEA,MAAM,EAAEpC,MAAM,EAAE,GAAGqC;wBACnB,MAAME,SAASvC,SAAS,GAAGA,OAAO,CAAC,EAAEL,KAAK,GAAGA;wBAC7C,IAAI6C,aAAaN,aAAaO,GAAG,CAACF;wBAElC,IAAI,CAACC,YAAY;4BACfA,aAAa,CAAC,6BAA6B,EAAEN,aAAaQ,IAAI,CAAC,GAAG,CAAC;4BACnER,aAAaS,GAAG,CAACJ,QAAQC;4BAEzBhC,QAAQoC,OAAO,CAAC1B,IAAI,CAAC;gCACnB3C,MAAM;gCACNiE;gCACA7C,KAAKa,QAAQqC,UAAU,CAACN;gCACxBH;4BACF;wBACF;wBAEA5B,QAAQ8B,GAAG,CAACpB,IAAI,CAAC;4BAAEsB;4BAAYlC;4BAAO8B;wBAAM;oBAC9C;gBACF;YACF;QACF;IACF;AACF;AAEA7B,OAAOuC,OAAO,GAAG;AAEjB,eAAevC,OAAM", "ignoreList": [0]}