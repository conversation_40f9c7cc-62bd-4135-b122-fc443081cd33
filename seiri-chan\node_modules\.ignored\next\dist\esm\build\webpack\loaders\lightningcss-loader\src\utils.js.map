{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/utils.ts"], "sourcesContent": ["let targetsCache: Record<string, any> = {}\n\n/**\n * Convert a version number to a single 24-bit number\n *\n * https://github.com/lumeland/lume/blob/4cc75599006df423a14befc06d3ed8493c645b09/plugins/lightningcss.ts#L160\n */\nfunction version(major: number, minor = 0, patch = 0): number {\n  return (major << 16) | (minor << 8) | patch\n}\n\nfunction parseVersion(v: string) {\n  return v.split('.').reduce(\n    (acc, val) => {\n      if (!acc) {\n        return null\n      }\n\n      const parsed = parseInt(val, 10)\n      if (isNaN(parsed)) {\n        return null\n      }\n      acc.push(parsed)\n      return acc\n    },\n    [] as Array<number> | null\n  )\n}\n\nfunction browserslistToTargets(targets: Array<string>) {\n  return targets.reduce(\n    (acc, value) => {\n      const [name, v] = value.split(' ')\n      const parsedVersion = parseVersion(v)\n\n      if (!parsedVersion) {\n        return acc\n      }\n      const versionDigit = version(\n        parsedVersion[0],\n        parsedVersion[1],\n        parsedVersion[2]\n      )\n\n      if (\n        name === 'and_qq' ||\n        name === 'and_uc' ||\n        name === 'baidu' ||\n        name === 'bb' ||\n        name === 'kaios' ||\n        name === 'op_mini'\n      ) {\n        return acc\n      }\n\n      if (acc[name] == null || versionDigit < acc[name]) {\n        acc[name] = versionDigit\n      }\n\n      return acc\n    },\n    {} as Record<string, number>\n  )\n}\n\nexport const getTargets = (opts: { targets?: string[]; key: any }) => {\n  const cache = targetsCache[opts.key]\n  if (cache) {\n    return cache\n  }\n\n  const result = browserslistToTargets(opts.targets ?? [])\n  return (targetsCache[opts.key] = result)\n}\n"], "names": ["targetsCache", "version", "major", "minor", "patch", "parseVersion", "v", "split", "reduce", "acc", "val", "parsed", "parseInt", "isNaN", "push", "browserslistToTargets", "targets", "value", "name", "parsedVersion", "versionDigit", "getTargets", "opts", "cache", "key", "result"], "mappings": "AAAA,IAAIA,eAAoC,CAAC;AAEzC;;;;CAIC,GACD,SAASC,QAAQC,KAAa,EAAEC,QAAQ,CAAC,EAAEC,QAAQ,CAAC;IAClD,OAAO,AAACF,SAAS,KAAOC,SAAS,IAAKC;AACxC;AAEA,SAASC,aAAaC,CAAS;IAC7B,OAAOA,EAAEC,KAAK,CAAC,KAAKC,MAAM,CACxB,CAACC,KAAKC;QACJ,IAAI,CAACD,KAAK;YACR,OAAO;QACT;QAEA,MAAME,SAASC,SAASF,KAAK;QAC7B,IAAIG,MAAMF,SAAS;YACjB,OAAO;QACT;QACAF,IAAIK,IAAI,CAACH;QACT,OAAOF;IACT,GACA,EAAE;AAEN;AAEA,SAASM,sBAAsBC,OAAsB;IACnD,OAAOA,QAAQR,MAAM,CACnB,CAACC,KAAKQ;QACJ,MAAM,CAACC,MAAMZ,EAAE,GAAGW,MAAMV,KAAK,CAAC;QAC9B,MAAMY,gBAAgBd,aAAaC;QAEnC,IAAI,CAACa,eAAe;YAClB,OAAOV;QACT;QACA,MAAMW,eAAenB,QACnBkB,aAAa,CAAC,EAAE,EAChBA,aAAa,CAAC,EAAE,EAChBA,aAAa,CAAC,EAAE;QAGlB,IACED,SAAS,YACTA,SAAS,YACTA,SAAS,WACTA,SAAS,QACTA,SAAS,WACTA,SAAS,WACT;YACA,OAAOT;QACT;QAEA,IAAIA,GAAG,CAACS,KAAK,IAAI,QAAQE,eAAeX,GAAG,CAACS,KAAK,EAAE;YACjDT,GAAG,CAACS,KAAK,GAAGE;QACd;QAEA,OAAOX;IACT,GACA,CAAC;AAEL;AAEA,OAAO,MAAMY,aAAa,CAACC;IACzB,MAAMC,QAAQvB,YAAY,CAACsB,KAAKE,GAAG,CAAC;IACpC,IAAID,OAAO;QACT,OAAOA;IACT;IAEA,MAAME,SAASV,sBAAsBO,KAAKN,OAAO,IAAI,EAAE;IACvD,OAAQhB,YAAY,CAACsB,KAAKE,GAAG,CAAC,GAAGC;AACnC,EAAC", "ignoreList": [0]}