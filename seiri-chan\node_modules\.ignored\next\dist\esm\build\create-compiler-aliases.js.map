{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "sourcesContent": ["import path from 'path'\nimport * as React from 'react'\nimport {\n  DOT_NEXT_ALIAS,\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  RSC_ACTION_PROXY_ALIAS,\n  RSC_ACTION_CLIENT_WRAPPER_ALIAS,\n  RSC_ACTION_VALIDATE_ALIAS,\n  RSC_ACTION_ENCRYPTION_ALIAS,\n  RSC_CACHE_WRAPPER_ALIAS,\n  type WebpackLayerName,\n  RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS,\n} from '../lib/constants'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { defaultOverrides } from '../server/require-hook'\nimport { hasExternalOtelApiPackage } from './webpack-config'\nimport { NEXT_PROJECT_ROOT } from './next-dir-paths'\nimport { shouldUseReactServerCondition } from './utils'\n\ninterface CompilerAliases {\n  [alias: string]: string | string[]\n}\n\nconst isReact19 = typeof React.use === 'function'\n\nexport function createWebpackAliases({\n  distDir,\n  isClient,\n  isEdgeServer,\n  dev,\n  config,\n  pagesDir,\n  appDir,\n  dir,\n  reactProductionProfiling,\n}: {\n  distDir: string\n  isClient: boolean\n  isEdgeServer: boolean\n  dev: boolean\n  config: NextConfigComplete\n  pagesDir: string | undefined\n  appDir: string | undefined\n  dir: string\n  reactProductionProfiling: boolean\n}): CompilerAliases {\n  const pageExtensions = config.pageExtensions\n  const customAppAliases: CompilerAliases = {}\n  const customDocumentAliases: CompilerAliases = {}\n\n  // tell webpack where to look for _app and _document\n  // using aliases to allow falling back to the default\n  // version when removed or not present\n  if (dev) {\n    const nextDistPath = 'next/dist/' + (isEdgeServer ? 'esm/' : '')\n    customAppAliases[`${PAGES_DIR_ALIAS}/_app`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_app.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_app.js`,\n    ]\n    customAppAliases[`${PAGES_DIR_ALIAS}/_error`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_error.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_error.js`,\n    ]\n    customDocumentAliases[`${PAGES_DIR_ALIAS}/_document`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_document.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_document.js`,\n    ]\n  }\n\n  return {\n    '@vercel/og$': 'next/dist/server/og/image-response',\n\n    // Avoid bundling both entrypoints in React 19 when we just need one.\n    // Also avoids bundler warnings in React 18 where react-dom/server.edge doesn't exist.\n    'next/dist/server/ReactDOMServerPages': isReact19\n      ? 'react-dom/server.edge'\n      : 'react-dom/server.browser',\n\n    // Alias next/dist imports to next/dist/esm assets,\n    // let this alias hit before `next` alias.\n    ...(isEdgeServer\n      ? {\n          'next/dist/api': 'next/dist/esm/api',\n          'next/dist/build': 'next/dist/esm/build',\n          'next/dist/client': 'next/dist/esm/client',\n          'next/dist/shared': 'next/dist/esm/shared',\n          'next/dist/pages': 'next/dist/esm/pages',\n          'next/dist/lib': 'next/dist/esm/lib',\n          'next/dist/server': 'next/dist/esm/server',\n\n          ...createNextApiEsmAliases(),\n        }\n      : undefined),\n\n    // For RSC server bundle\n    ...(!hasExternalOtelApiPackage() && {\n      '@opentelemetry/api': 'next/dist/compiled/@opentelemetry/api',\n    }),\n\n    ...(config.images.loaderFile\n      ? {\n          'next/dist/shared/lib/image-loader': config.images.loaderFile,\n          ...(isEdgeServer && {\n            'next/dist/esm/shared/lib/image-loader': config.images.loaderFile,\n          }),\n        }\n      : undefined),\n\n    'styled-jsx/style$': defaultOverrides['styled-jsx/style'],\n    'styled-jsx$': defaultOverrides['styled-jsx'],\n\n    ...customAppAliases,\n    ...customDocumentAliases,\n\n    ...(pagesDir ? { [PAGES_DIR_ALIAS]: pagesDir } : {}),\n    ...(appDir ? { [APP_DIR_ALIAS]: appDir } : {}),\n    [ROOT_DIR_ALIAS]: dir,\n    ...(isClient\n      ? {\n          'private-next-instrumentation-client': [\n            path.join(dir, 'src', 'instrumentation-client'),\n            path.join(dir, 'instrumentation-client'),\n            'private-next-empty-module',\n          ],\n\n          // disable typechecker, webpack5 allows aliases to be set to false to create a no-op module\n          'private-next-empty-module': false as any,\n        }\n      : {}),\n\n    [DOT_NEXT_ALIAS]: distDir,\n    ...(isClient || isEdgeServer ? getOptimizedModuleAliases() : {}),\n    ...(reactProductionProfiling ? getReactProfilingInProduction() : {}),\n\n    [RSC_ACTION_VALIDATE_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/action-validate',\n\n    [RSC_ACTION_CLIENT_WRAPPER_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper',\n\n    [RSC_ACTION_PROXY_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/server-reference',\n\n    [RSC_ACTION_ENCRYPTION_ALIAS]: 'next/dist/server/app-render/encryption',\n\n    [RSC_CACHE_WRAPPER_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/cache-wrapper',\n    [RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/track-dynamic-import',\n\n    '@swc/helpers/_': path.join(\n      path.dirname(require.resolve('@swc/helpers/package.json')),\n      '_'\n    ),\n\n    setimmediate: 'next/dist/compiled/setimmediate',\n  }\n}\n\nexport function createServerOnlyClientOnlyAliases(\n  isServer: boolean\n): CompilerAliases {\n  return isServer\n    ? {\n        'server-only$': 'next/dist/compiled/server-only/empty',\n        'client-only$': 'next/dist/compiled/client-only/error',\n        'next/dist/compiled/server-only$':\n          'next/dist/compiled/server-only/empty',\n        'next/dist/compiled/client-only$':\n          'next/dist/compiled/client-only/error',\n      }\n    : {\n        'server-only$': 'next/dist/compiled/server-only/index',\n        'client-only$': 'next/dist/compiled/client-only/index',\n        'next/dist/compiled/client-only$':\n          'next/dist/compiled/client-only/index',\n        'next/dist/compiled/server-only':\n          'next/dist/compiled/server-only/index',\n      }\n}\n\nexport function createNextApiEsmAliases() {\n  const mapping = {\n    head: 'next/dist/api/head',\n    image: 'next/dist/api/image',\n    constants: 'next/dist/api/constants',\n    router: 'next/dist/api/router',\n    dynamic: 'next/dist/api/dynamic',\n    script: 'next/dist/api/script',\n    link: 'next/dist/api/link',\n    form: 'next/dist/api/form',\n    navigation: 'next/dist/api/navigation',\n    headers: 'next/dist/api/headers',\n    og: 'next/dist/api/og',\n    server: 'next/dist/api/server',\n    // pages api\n    document: 'next/dist/api/document',\n    app: 'next/dist/api/app',\n  }\n  const aliasMap: Record<string, string> = {}\n  // Handle fully specified imports like `next/image.js`\n  for (const [key, value] of Object.entries(mapping)) {\n    const nextApiFilePath = path.join(NEXT_PROJECT_ROOT, key)\n    aliasMap[nextApiFilePath + '.js'] = value\n  }\n\n  return aliasMap\n}\n\nexport function createAppRouterApiAliases(isServerOnlyLayer: boolean) {\n  const mapping: Record<string, string> = {\n    head: 'next/dist/client/components/noop-head',\n    dynamic: 'next/dist/api/app-dynamic',\n    link: 'next/dist/client/app-dir/link',\n    form: 'next/dist/client/app-dir/form',\n  }\n\n  if (isServerOnlyLayer) {\n    mapping['navigation'] = 'next/dist/api/navigation.react-server'\n  }\n\n  const aliasMap: Record<string, string> = {}\n  for (const [key, value] of Object.entries(mapping)) {\n    const nextApiFilePath = path.join(NEXT_PROJECT_ROOT, key)\n    aliasMap[nextApiFilePath + '.js'] = value\n  }\n  return aliasMap\n}\n\n// file:///./../compiled/react/package.json\ntype ReactEntrypoint = 'jsx-runtime' | 'jsx-dev-runtime' | 'compiler-runtime'\n// file:///./../compiled/react-dom/package.json\ntype ReactDOMEntrypoint =\n  | 'client'\n  | 'server'\n  | 'server.edge'\n  | 'server.browser'\n  // TODO: server.node\n  | 'static'\n  | 'static.browser'\n  | 'static.edge'\n// TODO: static.node\n\n// file:///./../compiled/react-server-dom-webpack/package.json\ntype ReactServerDOMWebpackEntrypoint =\n  | 'client'\n  // TODO: client.browser\n  // TODO: client.edge\n  // TODO: client.node\n  | 'server'\n  // TODO: server.browser\n  // TODO: server.edge\n  | 'server.node'\n  | 'static'\n// TODO: static.browser\n// TODO: static.edge\n// TODO: static.node\n\ntype ReactPackagesEntryPoint =\n  | 'react'\n  | `react/${ReactEntrypoint}`\n  | 'react-dom'\n  | `react-dom/${ReactDOMEntrypoint}`\n  | `react-server-dom-webpack/${ReactServerDOMWebpackEntrypoint}`\n\ntype BundledReactChannel = '' | '-experimental'\n\ntype ReactAliases = {\n  [K in `${ReactPackagesEntryPoint}$`]: string\n} & {\n  // Edge Runtime does not use next-server runtime.\n  // This means we rely on rewritten import sources in compiled React.\n  // We need to alias those rewritten import sources.\n  [K in\n    | `next/dist/compiled/react${BundledReactChannel}$`\n    | `next/dist/compiled/react${BundledReactChannel}/${ReactEntrypoint}$`\n    | `next/dist/compiled/react-dom${BundledReactChannel}$`]?: string\n}\n\nexport function createVendoredReactAliases(\n  bundledReactChannel: BundledReactChannel,\n  {\n    layer,\n    isBrowser,\n    isEdgeServer,\n    reactProductionProfiling,\n  }: {\n    layer: WebpackLayerName\n    isBrowser: boolean\n    isEdgeServer: boolean\n    reactProductionProfiling: boolean\n  }\n): CompilerAliases {\n  const environmentCondition = isBrowser\n    ? 'browser'\n    : isEdgeServer\n      ? 'edge'\n      : 'nodejs'\n  const reactCondition = shouldUseReactServerCondition(layer)\n    ? 'server'\n    : 'client'\n\n  // ✅ Correct alias\n  // ❌ Incorrect alias i.e. importing this entrypoint should throw an error.\n  // ❔ Alias that may produce correct code in certain conditions.Keep until react-markup is available.\n\n  let reactAlias: ReactAliases\n  if (environmentCondition === 'browser' && reactCondition === 'client') {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                  /* ✅ */ `next/dist/compiled/react${bundledReactChannel}`,\n      'react/compiler-runtime$':               /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n      'react/jsx-dev-runtime$':                /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime`,\n      'react/jsx-runtime$':                    /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-runtime`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                            /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}`,\n      'react-dom/client$':                     /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                     /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      'react-dom/server.browser$':             /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':                /* ❌ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                     /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.browser$':             /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':                /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.browser`,\n      'react-server-dom-webpack/server$':      /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.browser`,\n      'react-server-dom-webpack/server.node$': /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/static$':      /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.browser`,\n    }\n  } else if (\n    environmentCondition === 'browser' &&\n    reactCondition === 'server'\n  ) {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                  /* ❌ */ `next/dist/compiled/react${bundledReactChannel}`,\n      'react/compiler-runtime$':               /* ❌ */ `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n      'react/jsx-dev-runtime$':                /* ❌ */ `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime`,\n      'react/jsx-runtime$':                    /* ❌ */ `next/dist/compiled/react${bundledReactChannel}/jsx-runtime`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                            /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}`,\n      'react-dom/client$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      'react-dom/server.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':                /* ❌ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':                /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.browser`,\n      'react-server-dom-webpack/server$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.browser`,\n      'react-server-dom-webpack/server.node$': /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/static$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.browser`,\n    }\n  } else if (environmentCondition === 'nodejs' && reactCondition === 'client') {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                 /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react`,\n      'react/compiler-runtime$':              /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react-compiler-runtime`,\n      'react/jsx-dev-runtime$':               /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime`,\n      'react/jsx-runtime$':                   /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                           /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react-dom`,\n      'react-dom/client$':                    /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                    /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.node`,\n      'react-dom/server.browser$':            /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':               /* ✅ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                    /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.node`,\n      'react-dom/static.browser$':            /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':               /* ❔ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':     /* ✅ */ `next/dist/server/route-modules/app-page/vendored/ssr/react-server-dom-webpack-client`,\n      'react-server-dom-webpack/server$':     /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/server.node$':/* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/static$':     /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.node`,\n    }\n  } else if (environmentCondition === 'nodejs' && reactCondition === 'server') {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                  /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react`,\n      'react/compiler-runtime$':               /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-compiler-runtime`,\n      'react/jsx-dev-runtime$':                /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime`,\n      'react/jsx-runtime$':                    /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                            /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-dom`,\n      'react-dom/client$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.node`,\n      'react-dom/server.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':                /* ❌ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.node`,\n      'react-dom/static.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':                /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':      /* ❔ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.node`,\n      'react-server-dom-webpack/server$':      /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server`,\n      'react-server-dom-webpack/server.node$': /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server`,\n      'react-server-dom-webpack/static$':      /* ✅ */ `next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-static`,\n    }\n  } else if (environmentCondition === 'edge' && reactCondition === 'client') {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                  /* ✅ */ `next/dist/compiled/react${bundledReactChannel}`,\n      'react/compiler-runtime$':               /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n      'react/jsx-dev-runtime$':                /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime`,\n      'react/jsx-runtime$':                    /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-runtime`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                            /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}`,\n      'react-dom/client$':                     /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                     /* ✅ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/server.browser$':             /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':                /* ✅ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                     /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      'react-dom/static.browser$':             /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':                /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.edge`,\n      'react-server-dom-webpack/server$':      /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.edge`,\n      'react-server-dom-webpack/server.node$': /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/static$':      /* ❌ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.edge`,\n    }\n  } else if (environmentCondition === 'edge' && reactCondition === 'server') {\n    // prettier-ignore\n    reactAlias = {\n      // file:///./../compiled/react/package.json\n      react$:                                  /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/react.react-server`,\n      'react/compiler-runtime$':               /* ❌ */ `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n      'react/jsx-dev-runtime$':                /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime.react-server`,\n      'react/jsx-runtime$':                    /* ✅ */ `next/dist/compiled/react${bundledReactChannel}/jsx-runtime.react-server`,\n      // file:///./../compiled/react-dom/package.json\n      'react-dom$':                            /* ✅ */ `next/dist/compiled/react-dom${bundledReactChannel}/react-dom.react-server`,\n      'react-dom/client$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n      'react-dom/server$':                     /* ❌ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/server.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n      // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n      'react-dom/server.edge$':                /* ❌ */ `next/dist/build/webpack/alias/react-dom-server${bundledReactChannel}.js`,\n      'react-dom/static$':                     /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      'react-dom/static.browser$':             /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n      'react-dom/static.edge$':                /* ❌ */ `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n      // file:///./../compiled/react-server-dom-webpack/package.json\n      'react-server-dom-webpack/client$':      /* ❔ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.edge`,\n      'react-server-dom-webpack/server$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.edge`,\n      'react-server-dom-webpack/server.node$': /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n      'react-server-dom-webpack/static$':      /* ✅ */ `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.edge`,\n    }\n\n    // prettier-ignore\n    reactAlias[`next/dist/compiled/react${bundledReactChannel}$`                 ] = reactAlias[`react$`]\n    // prettier-ignore\n    reactAlias[`next/dist/compiled/react${bundledReactChannel}/compiler-runtime$`] = reactAlias[`react/compiler-runtime$`]\n    // prettier-ignore\n    reactAlias[`next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime$` ] = reactAlias[`react/jsx-dev-runtime$`]\n    // prettier-ignore\n    reactAlias[`next/dist/compiled/react${bundledReactChannel}/jsx-runtime$`     ] = reactAlias[`react/jsx-runtime$`]\n    // prettier-ignore\n    reactAlias[`next/dist/compiled/react-dom${bundledReactChannel}$`             ] = reactAlias[`react-dom$`]\n  } else {\n    throw new Error(\n      `Unsupported environment condition \"${environmentCondition}\" and react condition \"${reactCondition}\". This is a bug in Next.js.`\n    )\n  }\n\n  if (reactProductionProfiling) {\n    reactAlias['react-dom/client$'] =\n      `next/dist/compiled/react-dom${bundledReactChannel}/profiling`\n  }\n\n  const alias: CompilerAliases = reactAlias\n\n  alias[\n    '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\n  ] = `next/dist/client/dev/noop-turbopack-hmr`\n\n  return alias\n}\n\n// Insert aliases for Next.js stubs of fetch, object-assign, and url\n// Keep in sync with insert_optimized_module_aliases in import_map.rs\nexport function getOptimizedModuleAliases(): CompilerAliases {\n  return {\n    unfetch: require.resolve('next/dist/build/polyfills/fetch/index.js'),\n    'isomorphic-unfetch': require.resolve(\n      'next/dist/build/polyfills/fetch/index.js'\n    ),\n    'whatwg-fetch': require.resolve(\n      'next/dist/build/polyfills/fetch/whatwg-fetch.js'\n    ),\n    'object-assign': require.resolve(\n      'next/dist/build/polyfills/object-assign.js'\n    ),\n    'object.assign/auto': require.resolve(\n      'next/dist/build/polyfills/object.assign/auto.js'\n    ),\n    'object.assign/implementation': require.resolve(\n      'next/dist/build/polyfills/object.assign/implementation.js'\n    ),\n    'object.assign/polyfill': require.resolve(\n      'next/dist/build/polyfills/object.assign/polyfill.js'\n    ),\n    'object.assign/shim': require.resolve(\n      'next/dist/build/polyfills/object.assign/shim.js'\n    ),\n    url: require.resolve('next/dist/compiled/native-url'),\n  }\n}\n\nfunction getReactProfilingInProduction(): CompilerAliases {\n  return {\n    'react-dom/client$': 'react-dom/profiling',\n  }\n}\n"], "names": ["path", "React", "DOT_NEXT_ALIAS", "PAGES_DIR_ALIAS", "ROOT_DIR_ALIAS", "APP_DIR_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "defaultOverrides", "hasExternalOtelApiPackage", "NEXT_PROJECT_ROOT", "shouldUseReactServerCondition", "isReact19", "use", "createWebpackAliases", "distDir", "isClient", "isEdgeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "pageExtensions", "customAppAliases", "customDocumentAliases", "nextDistPath", "reduce", "prev", "ext", "push", "join", "createNextApiEsmAliases", "undefined", "images", "loaderFile", "getOptimizedModuleAliases", "getReactProfilingInProduction", "dirname", "require", "resolve", "setimmediate", "createServerOnlyClientOnlyAliases", "isServer", "mapping", "head", "image", "constants", "router", "dynamic", "script", "link", "form", "navigation", "headers", "og", "server", "document", "app", "aliasMap", "key", "value", "Object", "entries", "nextApiFilePath", "createAppRouterApiAliases", "isServerOnlyLayer", "createVendoredReactAliases", "bundledReactChannel", "layer", "<PERSON><PERSON><PERSON><PERSON>", "environmentCondition", "reactCondition", "reactAlias", "react$", "Error", "alias", "unfetch", "url"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,YAAYC,WAAW,QAAO;AAC9B,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,aAAa,EACbC,sBAAsB,EACtBC,+BAA+B,EAC/BC,yBAAyB,EACzBC,2BAA2B,EAC3BC,uBAAuB,EAEvBC,gCAAgC,QAC3B,mBAAkB;AAEzB,SAASC,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,yBAAyB,QAAQ,mBAAkB;AAC5D,SAASC,iBAAiB,QAAQ,mBAAkB;AACpD,SAASC,6BAA6B,QAAQ,UAAS;AAMvD,MAAMC,YAAY,OAAOf,MAAMgB,GAAG,KAAK;AAEvC,OAAO,SAASC,qBAAqB,EACnCC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EAWzB;IACC,MAAMC,iBAAiBL,OAAOK,cAAc;IAC5C,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIR,KAAK;QACP,MAAMS,eAAe,eAAgBV,CAAAA,eAAe,SAAS,EAAC;QAC9DQ,gBAAgB,CAAC,GAAG1B,gBAAgB,KAAK,CAAC,CAAC,GAAG;eACxCqB,WACAI,eAAeI,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAACZ,UAAU,CAAC,KAAK,EAAEU,KAAK;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGF,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,GAAG1B,gBAAgB,OAAO,CAAC,CAAC,GAAG;eAC1CqB,WACAI,eAAeI,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAACZ,UAAU,CAAC,OAAO,EAAEU,KAAK;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGF,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,GAAG3B,gBAAgB,UAAU,CAAC,CAAC,GAAG;eAClDqB,WACAI,eAAeI,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACnC,KAAKoC,IAAI,CAACZ,UAAU,CAAC,UAAU,EAAEU,KAAK;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGF,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,eAAe;QAEf,qEAAqE;QACrE,sFAAsF;QACtF,wCAAwCf,YACpC,0BACA;QAEJ,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIK,eACA;YACE,iBAAiB;YACjB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,GAAGgB,yBAAyB;QAC9B,IACAC,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAACzB,+BAA+B;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIU,OAAOgB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCjB,OAAOgB,MAAM,CAACC,UAAU;YAC7D,GAAInB,gBAAgB;gBAClB,yCAAyCE,OAAOgB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAF,SAAS;QAEb,qBAAqB1B,gBAAgB,CAAC,mBAAmB;QACzD,eAAeA,gBAAgB,CAAC,aAAa;QAE7C,GAAGiB,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIN,WAAW;YAAE,CAACrB,gBAAgB,EAAEqB;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAACpB,cAAc,EAAEoB;QAAO,IAAI,CAAC,CAAC;QAC7C,CAACrB,eAAe,EAAEsB;QAClB,GAAIN,WACA;YACE,uCAAuC;gBACrCpB,KAAKoC,IAAI,CAACV,KAAK,OAAO;gBACtB1B,KAAKoC,IAAI,CAACV,KAAK;gBACf;aACD;YAED,2FAA2F;YAC3F,6BAA6B;QAC/B,IACA,CAAC,CAAC;QAEN,CAACxB,eAAe,EAAEiB;QAClB,GAAIC,YAAYC,eAAeoB,8BAA8B,CAAC,CAAC;QAC/D,GAAId,2BAA2Be,kCAAkC,CAAC,CAAC;QAEnE,CAAClC,0BAA0B,EACzB;QAEF,CAACD,gCAAgC,EAC/B;QAEF,CAACD,uBAAuB,EACtB;QAEF,CAACG,4BAA4B,EAAE;QAE/B,CAACC,wBAAwB,EACvB;QACF,CAACC,iCAAiC,EAChC;QAEF,kBAAkBX,KAAKoC,IAAI,CACzBpC,KAAK2C,OAAO,CAACC,QAAQC,OAAO,CAAC,+BAC7B;QAGFC,cAAc;IAChB;AACF;AAEA,OAAO,SAASC,kCACdC,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEA,OAAO,SAASX;IACd,MAAMY,UAAU;QACdC,MAAM;QACNC,OAAO;QACPC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,MAAM;QACNC,MAAM;QACNC,YAAY;QACZC,SAAS;QACTC,IAAI;QACJC,QAAQ;QACR,YAAY;QACZC,UAAU;QACVC,KAAK;IACP;IACA,MAAMC,WAAmC,CAAC;IAC1C,sDAAsD;IACtD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACnB,SAAU;QAClD,MAAMoB,kBAAkBrE,KAAKoC,IAAI,CAACtB,mBAAmBmD;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IAEA,OAAOF;AACT;AAEA,OAAO,SAASM,0BAA0BC,iBAA0B;IAClE,MAAMtB,UAAkC;QACtCC,MAAM;QACNI,SAAS;QACTE,MAAM;QACNC,MAAM;IACR;IAEA,IAAIc,mBAAmB;QACrBtB,OAAO,CAAC,aAAa,GAAG;IAC1B;IAEA,MAAMe,WAAmC,CAAC;IAC1C,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACnB,SAAU;QAClD,MAAMoB,kBAAkBrE,KAAKoC,IAAI,CAACtB,mBAAmBmD;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IACA,OAAOF;AACT;AAoDA,OAAO,SAASQ,2BACdC,mBAAwC,EACxC,EACEC,KAAK,EACLC,SAAS,EACTtD,YAAY,EACZM,wBAAwB,EAMzB;IAED,MAAMiD,uBAAuBD,YACzB,YACAtD,eACE,SACA;IACN,MAAMwD,iBAAiB9D,8BAA8B2D,SACjD,WACA;IAEJ,kBAAkB;IAClB,0EAA0E;IAC1E,oGAAoG;IAEpG,IAAII;IACJ,IAAIF,yBAAyB,aAAaC,mBAAmB,UAAU;QACrE,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEN,qBAAqB;YACjG,2BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;YAClH,0BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;YACjH,sBAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;YAC7G,+CAA+C;YAC/C,cAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,qBAAqB;YACrG,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;YAC5G,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,sFAAsF;YACtF,0BAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,0BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,8DAA8D;YAC9D,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;YACnI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;YACnI,yCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;QACrI;IACF,OAAO,IACLG,yBAAyB,aACzBC,mBAAmB,UACnB;QACA,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEN,qBAAqB;YACjG,2BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;YAClH,0BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;YACjH,sBAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;YAC7G,+CAA+C;YAC/C,cAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,qBAAqB;YACrG,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;YAC5G,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,sFAAsF;YACtF,0BAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,0BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,8DAA8D;YAC9D,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;YACnI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;YACnI,yCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,eAAe,CAAC;QACrI;IACF,OAAO,IAAIG,yBAAyB,YAAYC,mBAAmB,UAAU;QAC3E,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAwC,KAAK,GAAG,CAAC,0DAA0D,CAAC;YAC5G,2BAAwC,KAAK,GAAG,CAAC,2EAA2E,CAAC;YAC7H,0BAAwC,KAAK,GAAG,CAAC,0EAA0E,CAAC;YAC5H,sBAAwC,KAAK,GAAG,CAAC,sEAAsE,CAAC;YACxH,+CAA+C;YAC/C,cAAwC,KAAK,GAAG,CAAC,8DAA8D,CAAC;YAChH,qBAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEN,oBAAoB,OAAO,CAAC;YAC3G,qBAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YAChH,6BAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACnH,sFAAsF;YACtF,0BAAwC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YACzH,qBAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YAChH,6BAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACnH,0BAAwC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YAChH,8DAA8D;YAC9D,oCAAwC,KAAK,GAAG,CAAC,oFAAoF,CAAC;YACtI,oCAAwC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAC/H,yCAAwC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAC/H,oCAAwC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACjI;IACF,OAAO,IAAIG,yBAAyB,YAAYC,mBAAmB,UAAU;QAC3E,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAyC,KAAK,GAAG,CAAC,0DAA0D,CAAC;YAC7G,2BAAyC,KAAK,GAAG,CAAC,2EAA2E,CAAC;YAC9H,0BAAyC,KAAK,GAAG,CAAC,0EAA0E,CAAC;YAC7H,sBAAyC,KAAK,GAAG,CAAC,sEAAsE,CAAC;YACzH,+CAA+C;YAC/C,cAAyC,KAAK,GAAG,CAAC,8DAA8D,CAAC;YACjH,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEN,oBAAoB,OAAO,CAAC;YAC5G,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,sFAAsF;YACtF,0BAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,0BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,8DAA8D;YAC9D,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,oFAAoF,CAAC;YACvI,yCAAyC,KAAK,GAAG,CAAC,oFAAoF,CAAC;YACvI,oCAAyC,KAAK,GAAG,CAAC,oFAAoF,CAAC;QACzI;IACF,OAAO,IAAIG,yBAAyB,UAAUC,mBAAmB,UAAU;QACzE,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEN,qBAAqB;YACjG,2BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;YAClH,0BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;YACjH,sBAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;YAC7G,+CAA+C;YAC/C,cAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,qBAAqB;YACrG,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;YAC5G,qBAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,sFAAsF;YACtF,0BAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,0BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,8DAA8D;YAC9D,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,yCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QAClI;IACF,OAAO,IAAIG,yBAAyB,UAAUC,mBAAmB,UAAU;QACzE,kBAAkB;QAClBC,aAAa;YACX,2CAA2C;YAC3CC,QAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEN,oBAAoB,mBAAmB,CAAC;YACpH,2BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;YAClH,0BAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,6BAA6B,CAAC;YAC9H,sBAAyC,KAAK,GAAG,CAAC,wBAAwB,EAAEA,oBAAoB,yBAAyB,CAAC;YAC1H,+CAA+C;YAC/C,cAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,uBAAuB,CAAC;YAC5H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;YAC5G,qBAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,sFAAsF;YACtF,0BAAyC,KAAK,GAAG,CAAC,8CAA8C,EAAEA,oBAAoB,GAAG,CAAC;YAC1H,qBAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,6BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;YACpH,0BAAyC,KAAK,GAAG,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;YACjH,8DAA8D;YAC9D,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,yCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;YAChI,oCAAyC,KAAK,GAAG,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QAClI;QAEA,kBAAkB;QAClBK,UAAU,CAAC,CAAC,wBAAwB,EAAEL,oBAAoB,CAAC,CAAC,CAAkB,GAAGK,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;QACrG,kBAAkB;QAClBA,UAAU,CAAC,CAAC,wBAAwB,EAAEL,oBAAoB,kBAAkB,CAAC,CAAC,GAAGK,UAAU,CAAC,CAAC,uBAAuB,CAAC,CAAC;QACtH,kBAAkB;QAClBA,UAAU,CAAC,CAAC,wBAAwB,EAAEL,oBAAoB,iBAAiB,CAAC,CAAE,GAAGK,UAAU,CAAC,CAAC,sBAAsB,CAAC,CAAC;QACrH,kBAAkB;QAClBA,UAAU,CAAC,CAAC,wBAAwB,EAAEL,oBAAoB,aAAa,CAAC,CAAM,GAAGK,UAAU,CAAC,CAAC,kBAAkB,CAAC,CAAC;QACjH,kBAAkB;QAClBA,UAAU,CAAC,CAAC,4BAA4B,EAAEL,oBAAoB,CAAC,CAAC,CAAc,GAAGK,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;IAC3G,OAAO;QACL,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,mCAAmC,EAAEJ,qBAAqB,uBAAuB,EAAEC,eAAe,4BAA4B,CAAC,GAD5H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIlD,0BAA0B;QAC5BmD,UAAU,CAAC,oBAAoB,GAC7B,CAAC,4BAA4B,EAAEL,oBAAoB,UAAU,CAAC;IAClE;IAEA,MAAMQ,QAAyBH;IAE/BG,KAAK,CACH,4EACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,OAAO,SAASxC;IACd,OAAO;QACLyC,SAAStC,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEFsC,KAAKvC,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,SAASH;IACP,OAAO;QACL,qBAAqB;IACvB;AACF", "ignoreList": [0]}