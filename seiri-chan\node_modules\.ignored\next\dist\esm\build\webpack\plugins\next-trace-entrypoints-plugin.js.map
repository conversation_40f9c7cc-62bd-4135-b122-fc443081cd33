{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport type { Span } from '../../../trace'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport swcLoader, { type SWCLoaderOptions } from '../loaders/next-swc-loader'\nimport { isMetadataRouteFile } from '../../../lib/metadata/is-metadata-route'\nimport { getCompilationSpan } from '../utils'\nimport { isClientComponentEntryModule } from '../loaders/utils'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n    absolutePathByEntryName: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private compilerType: CompilerNameValues\n  private swcLoaderConfig: {\n    loader: string\n    options: Partial<SWCLoaderOptions>\n  }\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n    swcLoaderConfig,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n    swcLoaderConfig: TraceEntryPointsPlugin['swcLoaderConfig']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.compilerType = compilerType\n    this.swcLoaderConfig = swcLoaderConfig\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(compilation: webpack.Compilation, span: Span) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of entrypoint\n          .getEntrypointChunk()\n          .getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/') && this.appDir) {\n          const appDirRelativeEntryPath =\n            this.buildTraceContext.entriesTrace?.absolutePathByEntryName[\n              entrypoint.name\n            ]?.replace(this.appDir, '')\n\n          const entryIsStaticMetadataRoute =\n            appDirRelativeEntryPath &&\n            isMetadataRouteFile(appDirRelativeEntryPath, [], true)\n\n          // Include the client reference manifest in the trace, but not for\n          // static metadata routes, for which we don't generate those.\n          if (!entryIsStaticMetadataRoute) {\n            entryFiles.add(\n              nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n            )\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        compilation.emitAsset(\n          traceOutputName,\n          new sources.RawSource(\n            JSON.stringify({\n              version: TRACE_OUTPUT_VERSION,\n              files: finalFiles,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n            const absolutePathByEntryName = new Map<string, string>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                            absolutePathByEntryName.set(name, absolutePath)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readOriginalSource = (path: string) => {\n              return new Promise<string | Buffer>((resolve) => {\n                compilation.inputFileSystem.readFile(path, (err, result) => {\n                  if (err) {\n                    // we can't throw here as that crashes build un-necessarily\n                    return resolve('')\n                  }\n                  resolve(result || '')\n                })\n              })\n            }\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n\n              try {\n                // fallback to reading raw source file, this may fail\n                // due to unsupported syntax but best effort attempt\n                let usingOriginalSource = false\n                if (!source || isClientComponentEntryModule(mod)) {\n                  source = await readOriginalSource(path)\n                  usingOriginalSource = true\n                }\n                const sourceString = source.toString()\n\n                // If this is a client component we need to trace the\n                // original transpiled source not the client proxy which is\n                // applied before this plugin is run due to the\n                // client-module-loader\n                if (\n                  usingOriginalSource &&\n                  // don't attempt transpiling CSS or image imports\n                  path.match(/\\.(tsx|ts|js|cjs|mjs|jsx)$/)\n                ) {\n                  let transformResolve: (result: string) => void\n                  let transformReject: (error: unknown) => void\n                  const transformPromise = new Promise<string>(\n                    (resolve, reject) => {\n                      transformResolve = resolve\n                      transformReject = reject\n                    }\n                  )\n\n                  // TODO: should we apply all loaders except the\n                  // client-module-loader?\n                  swcLoader.apply(\n                    {\n                      resourcePath: path,\n                      getOptions: () => {\n                        return this.swcLoaderConfig.options\n                      },\n                      async: () => {\n                        return (err: unknown, result: string) => {\n                          if (err) {\n                            return transformReject(err)\n                          }\n                          return transformResolve(result)\n                        }\n                      },\n                    },\n                    [sourceString, undefined]\n                  )\n                  source = await transformPromise\n                }\n              } catch {\n                /* non-fatal */\n              }\n\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              absolutePathByEntryName: Object.fromEntries(\n                absolutePathByEntryName\n              ),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const compilationSpan =\n        getCompilationSpan(compilation) || getCompilationSpan(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (_, callback: any) => {\n            this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["nodePath", "isError", "nodeFileTrace", "CLIENT_REFERENCE_MANIFEST", "TRACE_OUTPUT_VERSION", "webpack", "sources", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "picomatch", "getModuleBuildInfo", "getPageFilePath", "resolveExternal", "sw<PERSON><PERSON><PERSON><PERSON>", "isMetadataRouteFile", "getCompilationSpan", "isClientComponentEntryModule", "PLUGIN_NAME", "TRACE_IGNORES", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "getFilesMapFromReasons", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "TraceEntryPointsPlugin", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "swcLoaderConfig", "buildTraceContext", "entryTraces", "tracingRoot", "createTraceAssets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "appDirRelativeEntryPath", "entriesTrace", "absolutePathByEntryName", "replace", "entryIsStaticMetadataRoute", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "emitAsset", "RawSource", "JSON", "stringify", "version", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "route", "absolutePath", "absolutePagePath", "request", "curMap", "readOriginalSource", "resolve", "inputFileSystem", "readFile", "err", "result", "mod", "source", "originalSource", "buffer", "usingOriginalSource", "sourceString", "toString", "match", "transformResolve", "transformReject", "transformPromise", "reject", "apply", "resourcePath", "getOptions", "options", "async", "undefined", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "chunks", "depModArray", "ignores", "isIgnoreMatcher", "contains", "dot", "traceEntryCount", "base", "id", "job", "isCjs", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "compiler", "tap", "compilationSpan", "link", "e", "code", "stats", "traceFn", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "_", "catch", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resRequest"], "mappings": "AAAA,OAAOA,cAAc,OAAM;AAE3B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,aAAa,QAAQ,iCAAgC;AAE9D,SACEC,yBAAyB,EACzBC,oBAAoB,QAEf,gCAA+B;AACtC,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,wBAAwB,EACxBC,oBAAoB,QACf,uBAAsB;AAE7B,OAAOC,eAAe,+BAA8B;AACpD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,eAAe,QAAQ,gBAAe;AAC/C,SAASC,eAAe,QAAQ,yBAAwB;AACxD,OAAOC,eAA0C,6BAA4B;AAC7E,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,kBAAkB,QAAQ,WAAU;AAC7C,SAASC,4BAA4B,QAAQ,mBAAkB;AAE/D,MAAMC,cAAc;AACpB,OAAO,MAAMC,gBAAgB;IAC3B;IACA;CACD,CAAA;AAED,MAAMC,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,OAAO,SAASG,uBACdC,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA2BA,OAAO,MAAMsB;IAiBXC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EACrBC,eAAe,EAWhB,CAAE;aApCIC,oBAAuC,CAAC;QAqC7C,IAAI,CAACT,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACQ,WAAW,GAAG,IAAIjC;QACvB,IAAI,CAAC6B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACM,WAAW,GAAGJ,yBAAyBP;QAC5C,IAAI,CAACG,YAAY,GAAGA;QACpB,IAAI,CAACK,eAAe,GAAGA;IACzB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMI,kBAAkB5C,WAAgC,EAAE6C,IAAU,EAAE;QACpE,MAAMC,aAAa9C,YAAY+C,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAI1C;YAC1B,MAAM2C,gBAAgB,IAAItC;YAC1B,MAAMuC,oBAAoB,IAAI5C;YAE9B,MAAM6C,cAAc,CAAC1C,OACnB,CAACd,cAAcyD,IAAI,CAAC,CAACC;oBACnB,OAAO5C,KAAK6C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAc1D,YAAY2D,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI/C;gBAEvB,KAAK,MAAMgD,SAASJ,WACjBK,kBAAkB,GAClBC,sBAAsB,GAAI;oBAC3B,KAAK,MAAMpD,QAAQkD,MAAMG,KAAK,CAAE;wBAC9B,IAAIX,YAAY1C,OAAO;4BACrB,MAAMsD,WAAWvF,SAASwF,IAAI,CAACrB,YAAYlC;4BAC3CwC,cAAcnC,GAAG,CAACiD;4BAClBL,WAAW5C,GAAG,CAACiD;wBACjB;oBACF;oBACA,KAAK,MAAMtD,QAAQkD,MAAMM,cAAc,CAAE;wBACvC,IAAId,YAAY1C,OAAO;4BACrB,MAAMsD,WAAWvF,SAASwF,IAAI,CAACrB,YAAYlC;4BAC3CwC,cAAcnC,GAAG,CAACiD;4BAClBL,WAAW5C,GAAG,CAACiD;wBACjB;oBACF;gBACF;gBACAf,cAAc/B,GAAG,CAACsC,YAAYG;gBAC9BR,kBAAkBjC,GAAG,CAACsC,WAAWW,IAAI,IAAI,IAAI;uBAAIR;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACpB,iBAAiB,CAAC6B,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIpB;qBAAc;oBACzBqB,kBAAkB,IAAI,CAAC9B,WAAW;oBAClC+B,YAAY,IAAI,CAAC1C,OAAO;gBAC1B;gBACAc;gBACAO,mBAAmBsB,OAAOC,WAAW,CAACvB;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAMwB,eAAe,IAAI,CAAC1C,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAACuB,YAAYG,WAAW,IAAIV,cAAe;oBA2C1C;gBA1CV,MAAM2B,kBAAkB,GAAGD,eAAenB,WAAWW,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBpG,SAASqG,OAAO,CACtCrG,SAASwF,IAAI,CAACrB,YAAYgC;gBAG5B,8CAA8C;gBAC9CjB,WAAWoB,MAAM,CACftG,SAASwF,IAAI,CAACrB,YAAY,GAAG+B,eAAenB,WAAWW,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIX,WAAWW,IAAI,CAACa,UAAU,CAAC,WAAW,IAAI,CAACjD,MAAM,EAAE;wBAEnD,8EAAA;oBADF,MAAMkD,2BACJ,uCAAA,IAAI,CAAC1C,iBAAiB,CAAC2C,YAAY,sBAAnC,+EAAA,qCAAqCC,uBAAuB,CAC1D3B,WAAWW,IAAI,CAChB,qBAFD,6EAEGiB,OAAO,CAAC,IAAI,CAACrD,MAAM,EAAE;oBAE1B,MAAMsD,6BACJJ,2BACA1F,oBAAoB0F,yBAAyB,EAAE,EAAE;oBAEnD,kEAAkE;oBAClE,6DAA6D;oBAC7D,IAAI,CAACI,4BAA4B;wBAC/B1B,WAAW5C,GAAG,CACZtC,SAASwF,IAAI,CACXrB,YACA+B,cACAnB,WAAWW,IAAI,CAACiB,OAAO,CAAC,QAAQ,OAC9B,MACAxG,4BACA;oBAGR;gBACF;gBAEA,MAAM0G,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAI5E,IAAI;2BACN+C;2BACC,EAAA,wBAAA,IAAI,CAACnB,WAAW,CAACvB,GAAG,CAACuC,WAAWW,IAAI,sBAApC,sBAAuCsB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAOhF;wBACM;oBAAjB,MAAMiF,YAAW,wBAAA,IAAI,CAACnD,WAAW,CAACvB,GAAG,CAACuC,WAAWW,IAAI,sBAApC,sBAAuClD,GAAG,CAACP;oBAE5D,MAAMkF,eAAenH,SAClBoH,QAAQ,CAAChB,iBAAiBnE,MAC1B0E,OAAO,CAAC,OAAO;oBAElB,IAAI1E,MAAM;wBACR,IAAI,EAACiF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGF9F,YAAYkG,SAAS,CACnBpB,iBACA,IAAI7F,QAAQkH,SAAS,CACnBC,KAAKC,SAAS,CAAC;oBACbC,SAASvH;oBACTkF,OAAOuB;gBACT;YAGN;QACF;IACF;IAEAe,iBACEvG,WAAgC,EAChCwG,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACA3G,YAAY4G,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtClH,aACA,OAAOmH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2BvD,UAAU,CAAC;YACxC,MAAMgE,kBACH/D,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMgE,eAAe,IAAIzG;gBACzB,MAAM0G,cAAc,IAAI1G;gBACxB,MAAM2G,oBAAoB,IAAI3G;gBAC9B,MAAM4E,0BAA0B,IAAI5E;gBAEpC,MAAM4G,YAAY,IAAI5G;gBAEtB,MAAMwG,kBACHhE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACmB,MAAMiD,MAAM,IAAItH,YAAYuH,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBnD,wBAAAA,KAAMiB,OAAO,CAAC,OAAO;wBAE5C,MAAMmC,SAASD,eAAetC,UAAU,CAAC;wBACzC,MAAMwC,QACJ,IAAI,CAACtF,aAAa,IAAIoF,eAAetC,UAAU,CAAC;wBAElD,IAAIwC,SAASD,QAAQ;4BACnB,KAAK,MAAMxH,OAAOqH,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAAC1H,KAAK;gCACV,MAAM2H,WAAW7H,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAI2H,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBzI,mBAAmBuI;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBC,KAAK,EAAE;wCACzB,MAAMC,eAAe1I,gBAAgB;4CACnC2I,kBACEH,gBAAgBC,KAAK,CAACE,gBAAgB;4CACxCjG,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZ8F,aAAa9C,UAAU,CAAC,IAAI,CAAChD,QAAQ,KACtC,IAAI,CAACD,MAAM,IACV+F,aAAa9C,UAAU,CAAC,IAAI,CAACjD,MAAM,GACrC;4CACAkF,YAAY/F,GAAG,CAAC4G,cAAcJ;4CAC9BV,aAAa9F,GAAG,CAAC4G,cAAc3D;4CAC/BgB,wBAAwBjE,GAAG,CAACiD,MAAM2D;wCACpC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIJ,SAASM,OAAO,EAAE;wCACpB,IAAIC,SAASf,kBAAkBjG,GAAG,CAACkD;wCAEnC,IAAI,CAAC8D,QAAQ;4CACXA,SAAS,IAAI1H;4CACb2G,kBAAkBhG,GAAG,CAACiD,MAAM8D;wCAC9B;wCACAd,UAAUjG,GAAG,CAACwG,SAASM,OAAO,EAAEN;wCAChCO,OAAO/G,GAAG,CAACwG,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAa9F,GAAG,CAACwG,SAASC,QAAQ,EAAExD;oCACpC8C,YAAY/F,GAAG,CAACwG,SAASC,QAAQ,EAAED;oCAEnC,IAAIO,SAASf,kBAAkBjG,GAAG,CAACkD;oCAEnC,IAAI,CAAC8D,QAAQ;wCACXA,SAAS,IAAI1H;wCACb2G,kBAAkBhG,GAAG,CAACiD,MAAM8D;oCAC9B;oCACAd,UAAUjG,GAAG,CAACwG,SAASC,QAAQ,EAAED;oCACjCO,OAAO/G,GAAG,CAACwG,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMQ,qBAAqB,CAACpF;oBAC1B,OAAO,IAAIyC,QAAyB,CAAC4C;wBACnCrI,YAAYsI,eAAe,CAACC,QAAQ,CAACvF,MAAM,CAACwF,KAAKC;4BAC/C,IAAID,KAAK;gCACP,2DAA2D;gCAC3D,OAAOH,QAAQ;4BACjB;4BACAA,QAAQI,UAAU;wBACpB;oBACF;gBACF;gBAEA,MAAMF,WAAW,OACfvF;wBAM8B0F,qBAAAA;oBAJ9B,MAAMA,MAAMrB,UAAUlG,GAAG,CAAC6B,SAASmE,YAAYhG,GAAG,CAAC6B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAI2F,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAE7D,IAAI;wBACF,qDAAqD;wBACrD,oDAAoD;wBACpD,IAAIC,sBAAsB;wBAC1B,IAAI,CAACH,UAAUhJ,6BAA6B+I,MAAM;4BAChDC,SAAS,MAAMP,mBAAmBpF;4BAClC8F,sBAAsB;wBACxB;wBACA,MAAMC,eAAeJ,OAAOK,QAAQ;wBAEpC,qDAAqD;wBACrD,2DAA2D;wBAC3D,+CAA+C;wBAC/C,uBAAuB;wBACvB,IACEF,uBACA,iDAAiD;wBACjD9F,KAAKiG,KAAK,CAAC,+BACX;4BACA,IAAIC;4BACJ,IAAIC;4BACJ,MAAMC,mBAAmB,IAAI3D,QAC3B,CAAC4C,SAASgB;gCACRH,mBAAmBb;gCACnBc,kBAAkBE;4BACpB;4BAGF,+CAA+C;4BAC/C,wBAAwB;4BACxB7J,UAAU8J,KAAK,CACb;gCACEC,cAAcvG;gCACdwG,YAAY;oCACV,OAAO,IAAI,CAAChH,eAAe,CAACiH,OAAO;gCACrC;gCACAC,OAAO;oCACL,OAAO,CAAClB,KAAcC;wCACpB,IAAID,KAAK;4CACP,OAAOW,gBAAgBX;wCACzB;wCACA,OAAOU,iBAAiBT;oCAC1B;gCACF;4BACF,GACA;gCAACM;gCAAcY;6BAAU;4BAE3BhB,SAAS,MAAMS;wBACjB;oBACF,EAAE,OAAM;oBACN,aAAa,GACf;oBAEA,OAAOT,UAAU;gBACnB;gBAEA,MAAMiB,aAAaC,MAAMC,IAAI,CAAC3C,YAAYxB,IAAI;gBAE9C,MAAMoE,sBAAsB,OAAOrB,KAAU3H;oBAC3C,IAAI,CAAC2H,OAAO,CAACA,IAAIf,YAAY,EAAE;oBAE/B,KAAK,MAAM1H,OAAOyI,IAAIf,YAAY,CAAE;wBAClC,MAAMqC,SAASjK,wBAAwBC,aAAaC;wBAEpD,IAAI+J,CAAAA,0BAAAA,OAAQnC,QAAQ,KAAI,CAACR,UAAUlG,GAAG,CAAC6I,OAAOnC,QAAQ,GAAG;4BACvDR,UAAUjG,GAAG,CAAC4I,OAAOnC,QAAQ,EAAEmC;4BAC/B,MAAMD,oBAAoBC,QAAQjJ;wBACpC;oBACF;gBACF;gBACA,MAAMkJ,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAMtC,SAASsC,WAAY;oBAC9B,MAAMG,oBAAoB5C,YAAYhG,GAAG,CAACmG,QAAQA;oBAClD,MAAM4C,YAAYhD,aAAa/F,GAAG,CAACmG;oBACnC,MAAM6C,kBAAkB/C,kBAAkBjG,GAAG,CAAC+I;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAehE,IAAI,IAAIkE,gBAAgBxE,IAAI;oBAC7C;gBACF;gBAEA,MAAMlB,mBAAmB,IAAI,CAAC9B,WAAW;gBACzC,MAAMyH,SAAS;uBAAIH;iBAAe;gBAElC,IAAI,CAACxH,iBAAiB,CAAC2C,YAAY,GAAG;oBACpCb,QAAQ;wBACNA,QAAQ;wBACRC,OAAO4F;wBACP3F;wBACAC,YAAY,IAAI,CAAC1C,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpBqI,aAAaR,MAAMC,IAAI,CAACzC,UAAU1B,IAAI;oBACtCuB,cAAcvC,OAAOC,WAAW,CAACsC;oBACjC7B,yBAAyBV,OAAOC,WAAW,CACzCS;oBAEFvC,YAAY9C,YAAY+C,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAI3C;gBACJ,IAAIC;gBACJ,MAAMgK,UAAU;uBACXzK;uBACA,IAAI,CAACwC,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAMkI,kBAAkBnL,UAAUkL,SAAS;oBACzCE,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAMlK,WAAW,CAACyC;oBAChB,OAAOuH,gBAAgBvH;gBACzB;gBAEA,MAAMiE,kBACHhE,UAAU,CAAC,0BAA0B;oBACpCyH,iBAAiBT,eAAetI,MAAM,GAAG;gBAC3C,GACCuB,YAAY,CAAC;oBACZ,MAAMuF,SAAS,MAAM5J,cAAcoL,gBAAgB;wBACjDU,MAAM,IAAI,CAAChI,WAAW;wBACtB+B,YAAY,IAAI,CAAC1C,OAAO;wBACxBuG;wBACA7B;wBACAC;wBACA0B,SAAS5B,YACL,OAAOmE,IAAI7J,QAAQ8J,KAAKC;4BACtB,OAAOrE,UAAUmE,IAAI7J,QAAQ8J,KAAK,CAACC;wBACrC,IACAnB;wBACJoB,QAAQxK;wBACRyK,cAAc;oBAChB;oBACA,aAAa;oBACb3K,WAAWoI,OAAOpI,QAAQ;oBAC1BoI,OAAOwC,WAAW,CAACC,OAAO,CAAC,CAACtK,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAUmI,OAAOnI,OAAO;gBAC1B;gBAEF,MAAM2G,kBACHhE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAM1C,iBAAiBJ,uBACrBC,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAOjC,SAASwF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE/B;wBACvC,MAAMoJ,SAAS3C,UAAUlG,GAAG,CAACP;wBAC7B,MAAMuK,WAAU7K,eAAAA,QACba,GAAG,CAACxC,SAASoH,QAAQ,CAAC,IAAI,CAACpD,WAAW,EAAE/B,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACuJ,WACDtB,MAAMuB,OAAO,CAACpB,0BAAAA,OAAQqB,OAAO,KAC7BrB,OAAOqB,OAAO,CAAC1J,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAM2F,SAASsC,WAAY;4BAeJpJ;wBAd1B,MAAM0J,YAAYhD,aAAa/F,GAAG,CAACmG;wBACnC,MAAMgE,kBAAkB3M,SAASoH,QAAQ,CACvC,IAAI,CAACpD,WAAW,EAChB2E;wBAEF,MAAM6C,kBAAkB/C,kBAAkBjG,GAAG,CAAC+I;wBAC9C,MAAMqB,YAAY,IAAI9K;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClB8K,UAAUnK,GAAG,CAACkG,OAAO;4BACnBtB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAAC/F,KAAKuL,KAAK,IAAIhL,EAAAA,sBAAAA,eACvBW,GAAG,CAACmK,qCADmB9K,oBAEtB+G,OAAO,OAAM,EAAE,CAAE;4BACnBgE,UAAUnK,GAAG,CAACzC,SAASwF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE1C,MAAM;gCAClD+F,SAASwF,KAAKnK,OAAO;4BACvB;wBACF;wBAEA,IAAI8I,iBAAiB;4BACnB,KAAK,MAAMsB,cAActB,gBAAgBxE,IAAI,GAAI;oCAOrBnF;gCAN1B,MAAMkL,uBAAuB/M,SAASoH,QAAQ,CAC5C,IAAI,CAACpD,WAAW,EAChB8I;gCAEFF,UAAUnK,GAAG,CAACqK,YAAY;oCAAEzF,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAAC/F,KAAKuL,KAAK,IAAIhL,EAAAA,uBAAAA,eACvBW,GAAG,CAACuK,0CADmBlL,qBAEtB+G,OAAO,OAAM,EAAE,CAAE;oCACnBgE,UAAUnK,GAAG,CAACzC,SAASwF,IAAI,CAAC,IAAI,CAACxB,WAAW,EAAE1C,MAAM;wCAClD+F,SAASwF,KAAKnK,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAACqB,WAAW,CAACtB,GAAG,CAAC8I,WAAWqB;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAM3E,YACN,CAACwB,MAAQxB,SAASwB;QAExB;IAEJ;IAEAc,MAAMsC,QAA0B,EAAE;QAChCA,SAAShF,KAAK,CAAC5G,WAAW,CAAC6L,GAAG,CAACjM,aAAa,CAACI;YAC3C,MAAM8L,kBACJpM,mBAAmBM,gBAAgBN,mBAAmBkM;YACxD,MAAMpF,6BAA6BsF,gBAAgB7I,UAAU,CAC3D;YAGF,MAAMyD,WAAW,OAAO1D;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAAC4C,SAASgB;;wBAE/BrJ,YAAYsI,eAAe,CACxB5B,QAAQ,CACX1D,MAAM,CAACwF,KAAKuD;4BACZ,IAAIvD,KAAK,OAAOa,OAAOb;4BACvBH,QAAQ0D;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACEpN,QAAQoN,MACPA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YACA,MAAMrF,OAAO,OAAO3D;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAIyC,QAAQ,CAAC4C,SAASgB;;wBAC/BrJ,YAAYsI,eAAe,CAAC3B,IAAI,CAChC3D,MACA,CAACwF,KAAK0D;4BACJ,IAAI1D,KAAK,OAAOa,OAAOb;4BACvBH,QAAQ6D;wBACV;oBAEJ;gBACF,EAAE,OAAOF,GAAG;oBACV,IAAIpN,QAAQoN,MAAOA,CAAAA,EAAEC,IAAI,KAAK,YAAYD,EAAEC,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEAxF,2BAA2B2F,OAAO,CAAC;gBACjCnM,YAAY4G,KAAK,CAACwF,aAAa,CAACtF,QAAQ,CACtC;oBACEzC,MAAMzE;oBACNyM,OAAOrN,QAAQsN,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACC,GAAGxF;oBACF,IAAI,CAACpE,iBAAiB,CAAC5C,aAAawG,4BACjCmF,IAAI,CAAC,IAAM3E,YACXyF,KAAK,CAAC,CAACjE,MAAQxB,SAASwB;gBAC7B;gBAGF,IAAIkE,WAAW1M,YAAY2M,eAAe,CAACxL,GAAG,CAAC;gBAE/C,SAASyL,WAAWvI,IAAY;oBAC9B,MAAMwI,WAAWxI,KAAKyI,KAAK,CAAC;oBAC5B,IAAIzI,IAAI,CAAC,EAAE,KAAK,OAAOwI,SAASlL,MAAM,GAAG,GACvC,OAAOkL,SAASlL,MAAM,GAAG,IAAIkL,SAASE,KAAK,CAAC,GAAG,GAAG5I,IAAI,CAAC,OAAO;oBAChE,OAAO0I,SAASlL,MAAM,GAAGkL,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjBvD;oBAEA,MAAMwD,cAAcP,SAASQ,WAAW,CAACzD;oBAEzC,OAAO,CACL1I,QACAmH,SACA2C,MAEA,IAAIpF,QAA2B,CAAC4C,SAASgB;4BACvC,MAAM8D,UAAUxO,SAASqG,OAAO,CAACjE;4BAEjCkM,YAAY5E,OAAO,CACjB,CAAC,GACD8E,SACAjF,SACA;gCACEkF,kBAAkBpN,YAAYoN,gBAAgB;gCAC9CC,qBAAqBrN,YAAYqN,mBAAmB;gCACpDC,qBAAqBtN,YAAYsN,mBAAmB;4BACtD,GACA,OAAO9E,KAAUC,QAAS8E;gCACxB,IAAI/E,KAAK,OAAOa,OAAOb;gCAEvB,IAAI,CAACC,QAAQ;oCACX,OAAOY,OAAO,qBAA6B,CAA7B,IAAImE,MAAM,qBAAV,qBAAA;+CAAA;oDAAA;sDAAA;oCAA4B;gCAC5C;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAI/E,OAAO7G,QAAQ,CAAC,QAAQ6G,OAAO7G,QAAQ,CAAC,MAAM;oCAChD6G,SAAS8E,CAAAA,8BAAAA,WAAYvK,IAAI,KAAIyF;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAO7G,QAAQ,CAAC,iBAAiB;wCACnC,IAAI6L,cAAchF,OACfnD,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAAC3G,SAAS+O,UAAU,CAACxF,YACrBA,QAAQtG,QAAQ,CAAC,SACjB2L,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBf;4CAFhBa,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BzF,QAAQ6E,KAAK,CAACH,EAAAA,cAAAA,WAAW1E,6BAAX0E,YAAqBjL,MAAM,KAAI,KAC7ChD,SAASiP,GAAG,GACZ,cAAa,EAEZtI,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMuI,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYV,KAAK,CAAC,GAAGgB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAM5C,IAAIqD,MAAM,CAACD,qBAAqB;gDACxC,MAAMpD,IAAIsD,QAAQ,CAChB,MAAMtD,IAAIuD,QAAQ,CAACH,qBACnB,WACAlN;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAOsN,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACAhG,QAAQ;oCAACI;oCAAQgB,QAAQ6E,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGpP,oBAAoB;oBACvBqP,gBAAgB7E;oBAChB8E,SAAS9E;oBACT+E,YAAY/E;gBACd;gBACA,MAAMgF,2BAA2B;oBAC/B,GAAGJ,mBAAmB;oBACtBK,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAG3P,wBAAwB;oBAC3BsP,gBAAgB7E;oBAChB8E,SAAS9E;oBACT+E,YAAY/E;gBACd;gBACA,MAAMmF,2BAA2B;oBAC/B,GAAGD,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAMnI,YAAY,OAChByB,SACAnH,QACA8J,KACAkE;oBAEA,MAAM5B,UAAUxO,SAASqG,OAAO,CAACjE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEiO,GAAG,EAAE,GAAG,MAAMzP,gBACpB,IAAI,CAACyC,OAAO,EACZ,IAAI,CAACM,YAAY,EACjB6K,SACAjF,SACA6G,gBACA,CAACtF,UAAY,CAAC+C,GAAWyC;4BACvB,OAAOjC,WAAWvD,SAAS1I,QAAQkO,YAAYpE;wBACjD,GACAlB,WACAA,WACAkF,qBACAN,qBACAO,0BACAH;oBAGF,IAAI,CAACK,KAAK;wBACR,MAAM,qBAAwD,CAAxD,IAAIxB,MAAM,CAAC,kBAAkB,EAAEtF,QAAQ,MAAM,EAAEnH,QAAQ,GAAvD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAuD;oBAC/D;oBACA,OAAOiO,IAAI1J,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACiB,gBAAgB,CACnBvG,aACAwG,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF", "ignoreList": [0]}