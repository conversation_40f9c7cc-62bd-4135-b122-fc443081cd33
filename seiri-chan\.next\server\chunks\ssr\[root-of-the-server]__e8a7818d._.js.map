{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Reponsitories/Seiri-chan/seiri-chan/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import { useTranslations } from 'next-intl';\nimport { Link } from '@/i18n/routing';\n\nexport default function Home() {\n  const t = useTranslations();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Seiri-chan\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto\">\n            基于TMDB元数据的智能动漫BD整理工具，支持传统识别和AI增强识别\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Link\n              href=\"/tasks\"\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors\"\n            >\n              {t('nav.tasks')}\n            </Link>\n            <Link\n              href=\"/config\"\n              className=\"bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors\"\n            >\n              {t('nav.config')}\n            </Link>\n          </div>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n            <h3 className=\"text-xl font-semibold mb-4 text-gray-900 dark:text-white\">\n              智能识别\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              结合传统规则引擎和AI增强识别，准确识别动漫、电视剧和电影文件\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n            <h3 className=\"text-xl font-semibold mb-4 text-gray-900 dark:text-white\">\n              批量处理\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              支持批量任务处理、深度扫描和实时状态跟踪\n            </p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\">\n            <h3 className=\"text-xl font-semibold mb-4 text-gray-900 dark:text-white\">\n              灵活整理\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              支持硬链接、软链接、复制、移动等多种文件操作方式\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,6VAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6WAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAG/E,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,sHAAA,CAAA,OAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6WAAC,sHAAA,CAAA,OAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;8BAKT,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,6WAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,6WAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,6WAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D", "debugId": null}}]}