{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Reponsitories/Seiri-chan/seiri-chan/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing'\nimport { createNavigation } from 'next-intl/navigation'\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: ['en', 'zh-CN', 'ja'],\n\n  // Used when no locale matches\n  defaultLocale: 'zh-C<PERSON>'\n})\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing)\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,iVAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAS;KAAK;IAE9B,8BAA8B;IAC9B,eAAe;AACjB;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,6WAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Reponsitories/Seiri-chan/seiri-chan/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server'\nimport { routing } from './routing'\n\nexport default getRequestConfig(async ({ requestLocale }) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale\n\n  // Ensure that a valid locale is used\n  if (!locale || !routing.locales.includes(locale as any)) {\n    locale = routing.defaultLocale\n  }\n\n  return {\n    locale,\n    messages: (await import(`../../messages/${locale}.json`)).default\n  }\n})\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,yWAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,sHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAgB;QACvD,SAAS,sHAAA,CAAA,UAAO,CAAC,aAAa;IAChC;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Reponsitories/Seiri-chan/seiri-chan/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\nimport { routing } from '@/i18n/routing';\n\nexport default async function LocaleLayout({\n  children,\n  params,\n}: {\n  children: React.ReactNode;\n  params: { locale: string };\n}) {\n  // Ensure that the incoming `locale` is valid\n  if (!routing.locales.includes(params.locale as any)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <NextIntlClientProvider messages={messages}>\n      {children}\n    </NextIntlClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,6CAA6C;IAC7C,IAAI,CAAC,sHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,MAAM,GAAU;QACnD,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,+VAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,6WAAC,iXAAA,CAAA,yBAAsB;QAAC,UAAU;kBAC/B;;;;;;AAGP", "debugId": null}}]}