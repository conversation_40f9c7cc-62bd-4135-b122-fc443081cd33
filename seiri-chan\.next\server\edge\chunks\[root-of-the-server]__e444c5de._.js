(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__e444c5de._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/messages/en.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"Loading...\",\"save\":\"Save\",\"cancel\":\"Cancel\",\"delete\":\"Delete\",\"edit\":\"Edit\",\"add\":\"Add\",\"search\":\"Search\",\"filter\":\"Filter\",\"refresh\":\"Refresh\",\"back\":\"Back\",\"next\":\"Next\",\"previous\":\"Previous\",\"confirm\":\"Confirm\",\"success\":\"Success\",\"error\":\"Error\",\"warning\":\"Warning\",\"info\":\"Info\"},\"nav\":{\"dashboard\":\"Dashboard\",\"tasks\":\"Tasks\",\"config\":\"Configuration\",\"logs\":\"Logs\",\"about\":\"About\"},\"tasks\":{\"title\":\"Task Management\",\"addTask\":\"Add Task\",\"addBatchTask\":\"Batch Add\",\"taskList\":\"Task List\",\"taskDetails\":\"Task Details\",\"status\":{\"pending\":\"Pending\",\"processing\":\"Processing\",\"completed\":\"Completed\",\"failed\":\"Failed\",\"cancelled\":\"Cancelled\"},\"type\":{\"tv\":\"TV Series\",\"movie\":\"Movie\",\"anime\":\"Anime\",\"anime_movie\":\"Anime Movie\"}},\"config\":{\"title\":\"Configuration\",\"tmdb\":\"TMDB Settings\",\"ai\":\"AI Settings\",\"paths\":\"Path Settings\",\"general\":\"General Settings\"}}"));}),
"[project]/messages/ja.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"読み込み中...\",\"save\":\"保存\",\"cancel\":\"キャンセル\",\"delete\":\"削除\",\"edit\":\"編集\",\"add\":\"追加\",\"search\":\"検索\",\"filter\":\"フィルター\",\"refresh\":\"更新\",\"back\":\"戻る\",\"next\":\"次へ\",\"previous\":\"前へ\",\"confirm\":\"確認\",\"success\":\"成功\",\"error\":\"エラー\",\"warning\":\"警告\",\"info\":\"情報\"},\"nav\":{\"dashboard\":\"ダッシュボード\",\"tasks\":\"タスク管理\",\"config\":\"設定\",\"logs\":\"ログ\",\"about\":\"について\"},\"tasks\":{\"title\":\"タスク管理\",\"addTask\":\"タスク追加\",\"addBatchTask\":\"一括追加\",\"taskList\":\"タスクリスト\",\"taskDetails\":\"タスク詳細\",\"status\":{\"pending\":\"待機中\",\"processing\":\"処理中\",\"completed\":\"完了\",\"failed\":\"失敗\",\"cancelled\":\"キャンセル\"},\"type\":{\"tv\":\"テレビシリーズ\",\"movie\":\"映画\",\"anime\":\"アニメ\",\"anime_movie\":\"アニメ映画\"}},\"config\":{\"title\":\"設定\",\"tmdb\":\"TMDB設定\",\"ai\":\"AI設定\",\"paths\":\"パス設定\",\"general\":\"一般設定\"}}"));}),
"[project]/messages/zh-CN.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"common\":{\"loading\":\"加载中...\",\"save\":\"保存\",\"cancel\":\"取消\",\"delete\":\"删除\",\"edit\":\"编辑\",\"add\":\"添加\",\"search\":\"搜索\",\"filter\":\"筛选\",\"refresh\":\"刷新\",\"back\":\"返回\",\"next\":\"下一步\",\"previous\":\"上一步\",\"confirm\":\"确认\",\"success\":\"成功\",\"error\":\"错误\",\"warning\":\"警告\",\"info\":\"信息\"},\"nav\":{\"dashboard\":\"仪表板\",\"tasks\":\"任务管理\",\"config\":\"配置\",\"logs\":\"日志\",\"about\":\"关于\"},\"tasks\":{\"title\":\"任务管理\",\"addTask\":\"添加任务\",\"addBatchTask\":\"批量添加\",\"taskList\":\"任务列表\",\"taskDetails\":\"任务详情\",\"status\":{\"pending\":\"等待中\",\"processing\":\"处理中\",\"completed\":\"已完成\",\"failed\":\"失败\",\"cancelled\":\"已取消\"},\"type\":{\"tv\":\"电视剧\",\"movie\":\"电影\",\"anime\":\"动画\",\"anime_movie\":\"动画电影\"}},\"config\":{\"title\":\"系统配置\",\"tmdb\":\"TMDB配置\",\"ai\":\"AI配置\",\"paths\":\"路径配置\",\"general\":\"常规设置\"}}"));}),
"[project]/src/i18n/request.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-intl@4.3.4_next@15.4.4_e8f837a7d2cd03b4fccf9058e0458a3e/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [middleware-edge] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ requestLocale })=>{
    // This typically corresponds to the `[locale]` segment
    let locale = await requestLocale;
    // Ensure that a valid locale is used
    if (!locale || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].locales.includes(locale)) {
        locale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"].defaultLocale;
    }
    return {
        locale,
        messages: (await __turbopack_context__.f({
            "../../messages/en.json": {
                id: ()=>"[project]/messages/en.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/en.json (json)"))
            },
            "../../messages/ja.json": {
                id: ()=>"[project]/messages/ja.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/ja.json (json)"))
            },
            "../../messages/zh-CN.json": {
                id: ()=>"[project]/messages/zh-CN.json (json)",
                module: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/messages/zh-CN.json (json)"))
            }
        }).import(`../../messages/${locale}.json`)).default
    };
});
}),
"[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Link": ()=>Link,
    "redirect": ()=>redirect,
    "routing": ()=>routing,
    "usePathname": ()=>usePathname,
    "useRouter": ()=>useRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-intl@4.3.4_next@15.4.4_e8f837a7d2cd03b4fccf9058e0458a3e/node_modules/next-intl/dist/esm/development/routing/defineRouting.js [middleware-edge] (ecmascript) <export default as defineRouting>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$server$2f$createNavigation$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-intl@4.3.4_next@15.4.4_e8f837a7d2cd03b4fccf9058e0458a3e/node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js [middleware-edge] (ecmascript) <export default as createNavigation>");
;
;
const routing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$routing$2f$defineRouting$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__defineRouting$3e$__["defineRouting"])({
    // A list of all locales that are supported
    locales: [
        'en',
        'zh-CN',
        'ja'
    ],
    // Used when no locale matches
    defaultLocale: 'zh-CN'
});
const { Link, redirect, usePathname, useRouter } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$navigation$2f$react$2d$server$2f$createNavigation$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$export__default__as__createNavigation$3e$__["createNavigation"])(routing);
}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>config,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-intl@4.3.4_next@15.4.4_e8f837a7d2cd03b4fccf9058e0458a3e/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [middleware-edge] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$4_next$40$15$2e$4$2e$4_e8f837a7d2cd03b4fccf9058e0458a3e$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["routing"]);
const config = {
    // Match only internationalized pathnames
    matcher: [
        '/',
        '/(zh-CN|en|ja)/:path*'
    ]
};
}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__e444c5de._.js.map