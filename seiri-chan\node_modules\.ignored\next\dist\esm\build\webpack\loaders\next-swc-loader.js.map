{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "sourcesContent": ["/*\nCopyright (c) 2017 The swc Project Developers\n\nPermission is hereby granted, free of charge, to any\nperson obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the\nSoftware without restriction, including without\nlimitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software\nis furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions\nof the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF\nANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED\nTO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\nPARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT\nSHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\nIN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n*/\n\nimport type { NextConfig } from '../../../types'\nimport { type WebpackLayerName, WEBPACK_LAYERS } from '../../../lib/constants'\nimport { isWasm, transform } from '../../swc'\nimport { getLoaderSWCOptions } from '../../swc/options'\nimport path, { isAbsolute } from 'path'\nimport { babelIncludeRegexes } from '../../webpack-config'\nimport { isResourceInPackages } from '../../handle-externals'\nimport type { TelemetryLoaderContext } from '../plugins/telemetry-plugin/telemetry-plugin'\nimport {\n  updateTelemetryLoaderCtxFromTransformOutput,\n  type SwcTransformTelemetryOutput,\n} from '../plugins/telemetry-plugin/update-telemetry-loader-context-from-swc'\nimport type { LoaderContext } from 'webpack'\nimport {\n  COMPILER_NAMES,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\n\nconst maybeExclude = (\n  excludePath: string,\n  transpilePackages: string[]\n): boolean => {\n  if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n    return false\n  }\n\n  const shouldBeBundled = isResourceInPackages(excludePath, transpilePackages)\n  if (shouldBeBundled) return false\n\n  return excludePath.includes('node_modules')\n}\n\nexport interface SWCLoaderOptions {\n  rootDir: string\n  isServer: boolean\n  compilerType: CompilerNameValues\n  pagesDir?: string\n  appDir?: string\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  nextConfig: NextConfig\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  esm?: boolean\n  transpilePackages?: string[]\n}\n\n// these are exact code conditions checked\n// for to force transpiling a `node_module`\nconst FORCE_TRANSPILE_CONDITIONS =\n  /next\\/font|next\\/dynamic|use server|use client|use cache/\n// same as above, but including `import(...)`.\n// (note the optional whitespace: `import  (...)` is also syntactically valid)\nconst FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT = new RegExp(\n  String.raw`(?:${FORCE_TRANSPILE_CONDITIONS.source})|import\\s*\\(`\n)\n\nasync function loaderTransform(\n  this: LoaderContext<SWCLoaderOptions> & TelemetryLoaderContext,\n  source?: string,\n  inputSourceMap?: any\n) {\n  // Make the loader async\n  const filename = this.resourcePath\n\n  // Ensure `.d.ts` are not processed.\n  if (filename.endsWith('.d.ts')) {\n    return [source, inputSourceMap]\n  }\n\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n  const shouldMaybeExclude = maybeExclude(\n    filename,\n    loaderOptions.transpilePackages || []\n  )\n\n  const trackDynamicImports = shouldTrackDynamicImports(loaderOptions)\n\n  if (shouldMaybeExclude) {\n    if (!source) {\n      throw new Error(`Invariant might be excluded but missing source`)\n    }\n\n    const forceTranspileConditions = trackDynamicImports\n      ? FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT\n      : FORCE_TRANSPILE_CONDITIONS\n\n    if (!forceTranspileConditions.test(source)) {\n      return [source, inputSourceMap]\n    }\n  }\n\n  const {\n    isServer,\n    rootDir,\n    pagesDir,\n    appDir,\n    hasReactRefresh,\n    nextConfig,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n  } = loaderOptions\n  const isPageFile = pagesDir ? filename.startsWith(pagesDir) : false\n  const relativeFilePathFromRoot = path.relative(rootDir, filename)\n\n  const swcOptions = getLoaderSWCOptions({\n    pagesDir,\n    appDir,\n    filename,\n    isServer,\n    isPageFile,\n    development:\n      this.mode === 'development' ||\n      !!nextConfig.experimental?.allowDevelopmentBuild,\n    isDynamicIo: nextConfig.experimental?.dynamicIO,\n    hasReactRefresh,\n    modularizeImports: nextConfig?.modularizeImports,\n    optimizePackageImports: nextConfig?.experimental?.optimizePackageImports,\n    swcPlugins: nextConfig?.experimental?.swcPlugins,\n    compilerOptions: nextConfig?.compiler,\n    optimizeServerReact: nextConfig?.experimental?.optimizeServerReact,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    relativeFilePathFromRoot,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n    cacheHandlers: nextConfig.experimental?.cacheHandlers,\n    useCacheEnabled: nextConfig.experimental?.useCache,\n    trackDynamicImports,\n  })\n\n  const programmaticOptions = {\n    ...swcOptions,\n    filename,\n    inputSourceMap: inputSourceMap ? JSON.stringify(inputSourceMap) : undefined,\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    sourceMaps: this.sourceMap,\n    inlineSourcesContent: this.sourceMap,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n  }\n\n  if (!programmaticOptions.inputSourceMap) {\n    delete programmaticOptions.inputSourceMap\n  }\n\n  // auto detect development mode\n  if (\n    this.mode &&\n    programmaticOptions.jsc &&\n    programmaticOptions.jsc.transform &&\n    programmaticOptions.jsc.transform.react &&\n    !Object.prototype.hasOwnProperty.call(\n      programmaticOptions.jsc.transform.react,\n      'development'\n    )\n  ) {\n    programmaticOptions.jsc.transform.react.development =\n      this.mode === 'development'\n  }\n\n  return transform(source as any, programmaticOptions).then(\n    (\n      output: {\n        code: string\n        map?: string\n      } & SwcTransformTelemetryOutput\n    ) => {\n      updateTelemetryLoaderCtxFromTransformOutput(this, output)\n      return [output.code, output.map ? JSON.parse(output.map) : undefined]\n    }\n  )\n}\n\nfunction shouldTrackDynamicImports(loaderOptions: SWCLoaderOptions): boolean {\n  // we only need to track `import()` 1. in dynamicIO, 2. on the server (RSC and SSR)\n  // (Note: logic duplicated in crates/next-core/src/next_server/transforms.rs)\n  const { nextConfig, bundleLayer, compilerType } = loaderOptions\n  return (\n    !!nextConfig.experimental?.dynamicIO &&\n    // NOTE: `server` means nodejs. `dynamicIO` is not supported in the edge runtime, so we want to exclude it.\n    // (also, the code generated by the dynamic imports transform relies on `CacheSignal`, which uses nodejs-specific APIs)\n    compilerType === COMPILER_NAMES.server &&\n    (bundleLayer === WEBPACK_LAYERS.reactServerComponents ||\n      bundleLayer === WEBPACK_LAYERS.serverSideRendering)\n  )\n}\n\nconst EXCLUDED_PATHS =\n  /[\\\\/](cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules|__virtual__)[\\\\/]/g\n\nexport function pitch(this: any) {\n  const callback = this.async()\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n\n  const shouldMaybeExclude = maybeExclude(\n    this.resourcePath,\n    loaderOptions.transpilePackages || []\n  )\n\n  ;(async () => {\n    if (\n      // if it might be excluded/no-op we can't use pitch loader\n      !shouldMaybeExclude &&\n      // TODO: investigate swc file reading in PnP mode?\n      !process.versions.pnp &&\n      !EXCLUDED_PATHS.test(this.resourcePath) &&\n      this.loaders.length - 1 === this.loaderIndex &&\n      isAbsolute(this.resourcePath) &&\n      !(await isWasm())\n    ) {\n      this.addDependency(this.resourcePath)\n      return loaderTransform.call(this)\n    }\n  })().then((r) => {\n    if (r) return callback(null, ...r)\n    callback()\n  }, callback)\n}\n\nexport default function swcLoader(\n  this: any,\n  inputSource: string,\n  inputSourceMap: any\n) {\n  const callback = this.async()\n  loaderTransform.call(this, inputSource, inputSourceMap).then(\n    ([transformedSource, outputSourceMap]: any) => {\n      callback(null, transformedSource, outputSourceMap || inputSourceMap)\n    },\n    (err: Error) => {\n      callback(err)\n    }\n  )\n}\n\n// accept Buffers instead of strings\nexport const raw = true\n"], "names": ["WEBPACK_LAYERS", "isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "babelIncludeRegexes", "isResourceInPackages", "updateTelemetryLoaderCtxFromTransformOutput", "COMPILER_NAMES", "maybeExclude", "excludePath", "transpilePackages", "some", "r", "test", "shouldBeBundled", "includes", "FORCE_TRANSPILE_CONDITIONS", "FORCE_TRANSPILE_CONDITIONS_WITH_IMPORT", "RegExp", "String", "raw", "source", "loaderTransform", "inputSourceMap", "nextConfig", "filename", "resourcePath", "endsWith", "loaderOptions", "getOptions", "shouldMaybeExclude", "trackDynamicImports", "shouldTrackDynamicImports", "Error", "forceTranspileConditions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "experimental", "allowDevelopmentBuild", "isDynamicIo", "dynamicIO", "modularizeImports", "optimizePackageImports", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "cacheHandlers", "useCacheEnabled", "useCache", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "then", "output", "code", "map", "parse", "compilerType", "server", "reactServerComponents", "serverSideRendering", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "loaders", "length", "loaderIndex", "addDependency", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAGA,SAAgCA,cAAc,QAAQ,yBAAwB;AAC9E,SAASC,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AACvC,SAASC,mBAAmB,QAAQ,uBAAsB;AAC1D,SAASC,oBAAoB,QAAQ,yBAAwB;AAE7D,SACEC,2CAA2C,QAEtC,uEAAsE;AAE7E,SACEC,cAAc,QAET,gCAA+B;AAEtC,MAAMC,eAAe,CACnBC,aACAC;IAEA,IAAIN,oBAAoBO,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACJ,eAAe;QACxD,OAAO;IACT;IAEA,MAAMK,kBAAkBT,qBAAqBI,aAAaC;IAC1D,IAAII,iBAAiB,OAAO;IAE5B,OAAOL,YAAYM,QAAQ,CAAC;AAC9B;AAqBA,0CAA0C;AAC1C,2CAA2C;AAC3C,MAAMC,6BACJ;AACF,8CAA8C;AAC9C,8EAA8E;AAC9E,MAAMC,yCAAyC,IAAIC,OACjDC,OAAOC,GAAG,CAAC,GAAG,EAAEJ,2BAA2BK,MAAM,CAAC,aAAa,CAAC;AAGlE,eAAeC,gBAEbD,MAAe,EACfE,cAAoB;QA0DdC,0BACSA,2BAGWA,2BACZA,2BAESA,2BASNA,2BACEA;IAzEnB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,oCAAoC;IACpC,IAAID,SAASE,QAAQ,CAAC,UAAU;QAC9B,OAAO;YAACN;YAAQE;SAAe;IACjC;IAEA,IAAIK,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAC5D,MAAMC,qBAAqBtB,aACzBiB,UACAG,cAAclB,iBAAiB,IAAI,EAAE;IAGvC,MAAMqB,sBAAsBC,0BAA0BJ;IAEtD,IAAIE,oBAAoB;QACtB,IAAI,CAACT,QAAQ;YACX,MAAM,qBAA2D,CAA3D,IAAIY,MAAM,CAAC,8CAA8C,CAAC,GAA1D,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,MAAMC,2BAA2BH,sBAC7Bd,yCACAD;QAEJ,IAAI,CAACkB,yBAAyBrB,IAAI,CAACQ,SAAS;YAC1C,OAAO;gBAACA;gBAAQE;aAAe;QACjC;IACF;IAEA,MAAM,EACJY,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACff,UAAU,EACVgB,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,GAAG,EACJ,GAAGlB;IACJ,MAAMmB,aAAaV,WAAWZ,SAASuB,UAAU,CAACX,YAAY;IAC9D,MAAMY,2BAA2B/C,KAAKgD,QAAQ,CAACd,SAASX;IAExD,MAAM0B,aAAalD,oBAAoB;QACrCoC;QACAC;QACAb;QACAU;QACAY;QACAK,aACE,IAAI,CAACC,IAAI,KAAK,iBACd,CAAC,GAAC7B,2BAAAA,WAAW8B,YAAY,qBAAvB9B,yBAAyB+B,qBAAqB;QAClDC,WAAW,GAAEhC,4BAAAA,WAAW8B,YAAY,qBAAvB9B,0BAAyBiC,SAAS;QAC/ClB;QACAmB,iBAAiB,EAAElC,8BAAAA,WAAYkC,iBAAiB;QAChDC,sBAAsB,EAAEnC,+BAAAA,4BAAAA,WAAY8B,YAAY,qBAAxB9B,0BAA0BmC,sBAAsB;QACxEC,UAAU,EAAEpC,+BAAAA,4BAAAA,WAAY8B,YAAY,qBAAxB9B,0BAA0BoC,UAAU;QAChDC,eAAe,EAAErC,8BAAAA,WAAYsC,QAAQ;QACrCC,mBAAmB,EAAEvC,+BAAAA,4BAAAA,WAAY8B,YAAY,qBAAxB9B,0BAA0BuC,mBAAmB;QAClEvB;QACAC;QACAC;QACAO;QACAN;QACAC;QACAC;QACAC;QACAkB,aAAa,GAAExC,4BAAAA,WAAW8B,YAAY,qBAAvB9B,0BAAyBwC,aAAa;QACrDC,eAAe,GAAEzC,4BAAAA,WAAW8B,YAAY,qBAAvB9B,0BAAyB0C,QAAQ;QAClDnC;IACF;IAEA,MAAMoC,sBAAsB;QAC1B,GAAGhB,UAAU;QACb1B;QACAF,gBAAgBA,iBAAiB6C,KAAKC,SAAS,CAAC9C,kBAAkB+C;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBjD;IAClB;IAEA,IAAI,CAAC0C,oBAAoB5C,cAAc,EAAE;QACvC,OAAO4C,oBAAoB5C,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAAC8B,IAAI,IACTc,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAAC3E,SAAS,IACjCmE,oBAAoBQ,GAAG,CAAC3E,SAAS,CAAC4E,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAAC3E,SAAS,CAAC4E,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAAC3E,SAAS,CAAC4E,KAAK,CAACxB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,OAAOrD,UAAUqB,QAAe8C,qBAAqBc,IAAI,CACvD,CACEC;QAKA5E,4CAA4C,IAAI,EAAE4E;QAClD,OAAO;YAACA,OAAOC,IAAI;YAAED,OAAOE,GAAG,GAAGhB,KAAKiB,KAAK,CAACH,OAAOE,GAAG,IAAId;SAAU;IACvE;AAEJ;AAEA,SAAStC,0BAA0BJ,aAA+B;QAK5DJ;IAJJ,mFAAmF;IACnF,6EAA6E;IAC7E,MAAM,EAAEA,UAAU,EAAEqB,WAAW,EAAEyC,YAAY,EAAE,GAAG1D;IAClD,OACE,CAAC,GAACJ,2BAAAA,WAAW8B,YAAY,qBAAvB9B,yBAAyBiC,SAAS,KACpC,2GAA2G;IAC3G,uHAAuH;IACvH6B,iBAAiB/E,eAAegF,MAAM,IACrC1C,CAAAA,gBAAgB/C,eAAe0F,qBAAqB,IACnD3C,gBAAgB/C,eAAe2F,mBAAmB,AAAD;AAEvD;AAEA,MAAMC,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IAC3B,IAAIjE,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAMC,qBAAqBtB,aACzB,IAAI,CAACkB,YAAY,EACjBE,cAAclB,iBAAiB,IAAI,EAAE;IAGrC,CAAA;QACA,IACE,0DAA0D;QAC1D,CAACoB,sBACD,kDAAkD;QAClD,CAACgE,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAe7E,IAAI,CAAC,IAAI,CAACa,YAAY,KACtC,IAAI,CAACuE,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5ChG,WAAW,IAAI,CAACuB,YAAY,KAC5B,CAAE,MAAM3B,UACR;YACA,IAAI,CAACqG,aAAa,CAAC,IAAI,CAAC1E,YAAY;YACpC,OAAOJ,gBAAgB0D,IAAI,CAAC,IAAI;QAClC;IACF,CAAA,IAAKC,IAAI,CAAC,CAACrE;QACT,IAAIA,GAAG,OAAOgF,SAAS,SAAShF;QAChCgF;IACF,GAAGA;AACL;AAEA,eAAe,SAASS,UAEtBC,WAAmB,EACnB/E,cAAmB;IAEnB,MAAMqE,WAAW,IAAI,CAACC,KAAK;IAC3BvE,gBAAgB0D,IAAI,CAAC,IAAI,EAAEsB,aAAa/E,gBAAgB0D,IAAI,CAC1D,CAAC,CAACsB,mBAAmBC,gBAAqB;QACxCZ,SAAS,MAAMW,mBAAmBC,mBAAmBjF;IACvD,GACA,CAACkF;QACCb,SAASa;IACX;AAEJ;AAEA,oCAAoC;AACpC,OAAO,MAAMrF,MAAM,KAAI", "ignoreList": [0]}