{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildInversePrefetchSegmentDataRoute,\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\nimport { runAfterProductionCompile } from './after-production-compile'\nimport { generatePreviewKeys } from './preview-key-utils'\nimport { handleBuildComplete } from './adapter/build-complete'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport enum RouteType {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  // IMAGE = 'IMAGE',\n\n  /**\n   * `STATIC_FILE` represents a static file (ie /_next/static)\n   */\n  STATIC_FILE = 'STATIC_FILE',\n\n  MIDDLEWARE = 'MIDDLEWARE',\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<ManifestRedirectRoute>\n  rewrites: {\n    beforeFiles: Array<ManifestRewriteRoute>\n    afterFiles: Array<ManifestRewriteRoute>\n    fallback: Array<ManifestRewriteRoute>\n  }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string): ManifestRoute {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      regions?: string[] | string\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\nexport interface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\nexport type StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  options: {\n    debuggerPortOffset: number\n    progress?: {\n      run: () => void\n      clear: () => void\n    }\n  }\n): StaticWorker {\n  const { debuggerPortOffset, progress } = options\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    debuggerPortOffset,\n    enableSourceMaps: config.experimental.enablePrerenderSourceMaps,\n    // remove --max-old-space-size flag as it can cause memory issues.\n    isolatedMemory: true,\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = (require('../export') as typeof import('../export'))\n    .default as typeof import('../export').default\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  debugPrerender = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n    NextBuildContext.debugPrerender = debugPrerender\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n                debugPrerender,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo({\n        dir,\n        dev: false,\n        debugPrerender,\n      })\n\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = await generatePreviewKeys({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ManifestRoute> = []\n          const staticRoutes: Array<ManifestRoute> = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            rewrites: {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      routesManifest.rewrites = {\n        beforeFiles: rewrites.beforeFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        ),\n        afterFiles: rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        ),\n        fallback: rewrites.fallback.map((r) => buildCustomRoute('rewrite', r)),\n      }\n\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    debuggerPortOffset: -1,\n                    isolatedMemory: false,\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                    isTurbopack: false,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n        await runAfterProductionCompile({\n          config,\n          buildSpan: nextBuildSpan,\n          telemetry,\n          metadata: {\n            projectDir: dir,\n            distDir,\n          },\n        })\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config, { debuggerPortOffset: -1 })\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/builtin/not-found'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                if (staticInfo?.hadUnsupportedValue) {\n                  errorFromUnsupportedSegmentConfig()\n                }\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined' ||\n                  typeof staticInfo?.preferredRegion !== 'undefined'\n                ) {\n                  const regions = staticInfo?.preferredRegion\n                    ? typeof staticInfo.preferredRegion === 'string'\n                      ? [staticInfo.preferredRegion]\n                      : staticInfo.preferredRegion\n                    : undefined\n\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                    ...(regions && { regions }),\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  params: {},\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: [],\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: [],\n                                  throwOnEmptyStaticShell: true,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  originalAppPath,\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyStaticShell: undefined,\n                })\n              })\n            })\n        )\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              SERVER_FILES_MANIFEST,\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.hadUnsupportedValue) {\n          errorFromUnsupportedSegmentConfig()\n        }\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n          isTurbopack: true,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n\n          const exportApp = (require('../export') as typeof import('../export'))\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled: boolean = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : false\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                    _allowEmptyStaticShell: !route.throwOnEmptyStaticShell,\n                  }\n                })\n              })\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              debugPrerender,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getFallbackMode = (route: PrerenderedRoute) => {\n            const hasEmptyStaticShell = exportResult.byPath.get(\n              route.pathname\n            )?.hasEmptyStaticShell\n\n            // If the route has an empty static shell and is not configured to\n            // throw on empty static shell, then we should use the blocking\n            // static render mode.\n            if (\n              hasEmptyStaticShell &&\n              !route.throwOnEmptyStaticShell &&\n              route.fallbackMode === FallbackMode.PRERENDER\n            ) {\n              return FallbackMode.BLOCKING_STATIC_RENDER\n            }\n\n            // If the route has no fallback mode, then we should use the\n            // `NOT_FOUND` fallback mode.\n            if (!route.fallbackMode) {\n              return FallbackMode.NOT_FOUND\n            }\n\n            return route.fallbackMode\n          }\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                metadata = {},\n                hasEmptyStaticShell,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  params: {},\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: [],\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: [],\n                  throwOnEmptyStaticShell: true,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes ??= []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    dynamicRoute.prefetchSegmentDataRoutes.push(\n                      buildPrefetchSegmentDataRoute(route.pathname, segmentPath)\n                    )\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode = getFallbackMode(route)\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: fallback\n                    ? route.fallbackRootParams\n                    : undefined,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n\n                  // since the app router not found is prioritized over pages router,\n                  // we have to ensure the app router entries are available for all locales\n                  if (i18n) {\n                    for (const locale of i18n.locales) {\n                      const curPath = `/${locale}/404`\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                  }\n\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n\n          if (config.experimental.clientSegmentCache) {\n            for (const route of [\n              ...routesManifest.staticRoutes,\n              ...routesManifest.dynamicRoutes,\n            ]) {\n              // If the segment paths aren't defined, we need to insert a\n              // reverse routing rule so that there isn't any conflicts\n              // with other dynamic routes for the prefetch segment\n              // routes. This is true for any route that is not PPR-enabled,\n              // including all routes defined by Pages Router.\n\n              // We don't need to add the prefetch segment data routes if it was\n              // added due to a page that was already generated. This would have\n              // happened if the page was static or partially static.\n              if (route.prefetchSegmentDataRoutes) {\n                continue\n              }\n\n              route.prefetchSegmentDataRoutes = [\n                buildInversePrefetchSegmentDataRoute(\n                  route.page,\n                  // We use the special segment path of `/_tree` because it's\n                  // the first one sent by the client router so it's the only\n                  // one we need to rewrite to the regular prefetch RSC route.\n                  '/_tree'\n                ),\n              ]\n            }\n          }\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.experimental.adapterPath) {\n        await handleBuildComplete({\n          dir,\n          distDir,\n          tracingRoot: outputFileTracingRoot,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          adapterPath: config.experimental.adapterPath,\n          pageKeys: pageKeys.pages,\n          appPageKeys: denormalizedAppPages,\n          routesManifest,\n          prerenderManifest,\n          middlewareManifest,\n          functionsConfigManifest,\n          requiredServerFiles: requiredServerFilesManifest.files,\n        })\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    if (isTurbopack && !process.env.__NEXT_TEST_MODE) {\n      warnAboutTurbopackBuilds(loadedConfig)\n    }\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction errorFromUnsupportedSegmentConfig(): never {\n  Log.error(\n    `Invalid segment configuration export detected. This can cause unexpected behavior from the configs not being applied. You should see the relevant failures in the logs above. Please fix them to continue.`\n  )\n  process.exit(1)\n}\n\nfunction warnAboutTurbopackBuilds(config?: NextConfigComplete) {\n  let warningStr =\n    `Support for Turbopack builds is experimental. ` +\n    bold(\n      `We don't recommend deploying mission-critical applications to production.`\n    )\n  warningStr +=\n    '\\n\\n- ' +\n    bold(\n      'Turbopack currently always builds production source maps for the browser. This will include project source code if deployed to production.'\n    )\n  warningStr +=\n    '\\n- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.'\n\n  if (!config?.experimental.turbopackPersistentCaching) {\n    warningStr +=\n      '\\n- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.'\n  }\n\n  warningStr +=\n    '\\n- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.'\n  warningStr +=\n    '\\n\\nProvide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721'\n\n  Log.warn(warningStr)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["loadEnvConfig", "bold", "yellow", "makeRe", "existsSync", "promises", "fs", "os", "Worker", "defaultConfig", "devalue", "findUp", "nanoid", "path", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "MIDDLEWARE_FILENAME", "PAGES_DIR_ALIAS", "INSTRUMENTATION_HOOK_FILENAME", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "NEXT_RESUME_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "MATCHED_PATH_HEADER", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "FileType", "fileExists", "findPagesDir", "loadCustomRoutes", "normalizeRouteRegex", "nonNullable", "recursiveDelete", "verifyPartytownSetup", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_STATIC_FILES_PATH", "EXPORT_DETAIL", "EXPORT_MARKER", "IMAGES_MANIFEST", "PAGES_MANIFEST", "PHASE_PRODUCTION_BUILD", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "STATIC_STATUS_PAGES", "MIDDLEWARE_MANIFEST", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "APP_BUILD_MANIFEST", "RSC_MODULE_TYPES", "NEXT_FONT_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "UNDERSCORE_NOT_FOUND_ROUTE", "DYNAMIC_CSS_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "getSortedRoutes", "isDynamicRoute", "getSortedRouteObjects", "loadConfig", "normalizePagePath", "getPagePath", "ciEnvironment", "turborepoTraceAccess", "TurborepoAccessTraceResult", "writeTurborepoAccessTraceResult", "eventBuildOptimize", "eventCliSession", "eventBuildFeatureUsage", "eventNextPlugins", "EVENT_BUILD_FEATURE_USAGE", "eventPackageUsedInGetServerSideProps", "eventBuildCompleted", "eventBuildFailed", "Telemetry", "createPagesMapping", "getStaticInfoIncludingLayouts", "sortByPageExts", "PAGE_TYPES", "generateBuildId", "isWriteable", "Log", "createSpinner", "trace", "flushAllTraces", "setGlobal", "detectConflictingPaths", "computeFromManifest", "getJsPageSizeInKb", "printCustomRoutes", "printTreeView", "copyTracedFiles", "isReservedPage", "isAppBuiltinNotFoundPage", "collectRoutesUsingEdgeRuntime", "collectMeta", "writeBuildId", "normalizeLocalePath", "isError", "isEdgeRuntime", "recursiveCopy", "recursiveReadDir", "lockfilePatchPromise", "teardownTraceSubscriber", "getNamedRouteRegex", "getFilesInDir", "eventSwcPlugins", "normalizeAppPath", "ACTION_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "webpackBuild", "NextBuildContext", "normalizePathSep", "isAppRouteRoute", "createClientRouterFilter", "createValidFileMatcher", "startTypeChecking", "generateInterceptionRoutesRewrites", "buildDataRoute", "collectBuildTraces", "formatManifest", "recordFrameworkVersion", "updateBuildDiagnostics", "recordFetchMetrics", "getStartServerInfo", "logStartInfo", "hasCustomExportOutput", "buildCustomRoute", "traceMemoryUsage", "generateEncryptionKeyBase64", "uploadTrace", "checkIsAppPPREnabled", "checkIsRoutePPREnabled", "FallbackMode", "fallbackModeToFallbackField", "RenderingMode", "InvariantError", "HTML_LIMITED_BOT_UA_RE_STRING", "buildInversePrefetchSegmentDataRoute", "buildPrefetchSegmentDataRoute", "turbopackBuild", "isPersistentCachingEnabled", "inlineStaticEnv", "populateStaticEnv", "durationToString", "traceGlobals", "extractNextErrorCode", "runAfterProductionCompile", "generatePreviewKeys", "handleBuildComplete", "ALLOWED_HEADERS", "RouteType", "pageToRoute", "page", "routeRegex", "prefixRouteKeys", "regex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "join", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "console", "log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "writeFunctionsConfigManifest", "writeRequiredServerFilesManifest", "requiredServerFiles", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "port", "dot", "search", "localPatterns", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "createStaticWorker", "options", "debuggerPortOffset", "progress", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "enableSourceMaps", "enablePrerenderSourceMaps", "isolated<PERSON><PERSON><PERSON>", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "buildExport", "nextConfig", "silent", "outdir", "getBuildId", "isGenerateMode", "build", "reactProductionProfiling", "debugOutput", "debugPrerender", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "buildStartTime", "Date", "now", "loadedConfig", "undefined", "buildMode", "isTurboBuild", "String", "process", "env", "__NEXT_VERSION", "mappedPages", "traceFn", "turborepoAccessTraceResult", "NEXT_DEPLOYMENT_ID", "deploymentId", "exit", "info", "customRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "publicDir", "pagesDir", "app", "<PERSON><PERSON><PERSON>", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "cwd", "isCustomServer", "turboFlag", "then", "events", "envInfo", "experimentalFeatures", "dev", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "code", "Error", "cleanDistDir", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "payload", "validFile<PERSON><PERSON><PERSON>", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "instrumentationHookDetectionRegExp", "rootDir", "rootPaths", "Array", "from", "some", "include", "test", "hasMiddlewareFile", "previewProps", "isDev", "pagesType", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "pagePath", "appPath", "add", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "hasApp404", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "hasPublicPageFile", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "ppr", "routesManifestPath", "routesManifest", "sortedRoutes", "staticRoutes", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "dataRoutes", "i18n", "rsc", "header", "<PERSON><PERSON><PERSON><PERSON>", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "contentTypeHeader", "suffix", "prefetchSuffix", "prefetchSegmentHeader", "prefetchSegmentSuffix", "prefetchSegmentDirSuffix", "rewriteHeaders", "pathHeader", "query<PERSON>eader", "skipMiddlewareUrlNormalize", "chain", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "buildStage", "pagesManifestPath", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "NEXT_TURBOPACK_USE_WORKER", "durationString", "event", "bundler", "durationInSeconds", "round", "serverBuildPromise", "res", "buildTraceWorker", "edgeRuntimeRoutes", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "buildSpan", "metadata", "projectDir", "postCompileSpinner", "buildManifestPath", "appBuildManifestPath", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "key", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "gzipSize", "actionManifest", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "size", "totalSize", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isInsideAppDir", "staticInfo", "hadUnsupportedValue", "errorFromUnsupportedSegmentConfig", "runtime", "maxDuration", "preferredRegion", "regions", "pageRuntime", "client", "edgeInfo", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "params", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "throwOnEmptyStaticShell", "dynamic", "hasStaticProps", "isAmpOnly", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "delete", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyStaticShell", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "useStaticPages404", "pg", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "_allowEmptyStaticShell", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getFallbackMode", "by<PERSON><PERSON>", "NOT_FOUND", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "traces", "turborepoAccessTraceResults", "values", "ssgNotFoundPaths", "serverBundle", "unlink", "hasRevalidateZero", "isAppRouteHandler", "htmlBotsRegexString", "htmlLimitedBots", "bypassFor", "type", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "initialStatus", "status", "initialHeaders", "renderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "isDynamicAppRoute", "fallbackCacheControl", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "clientSegmentCache", "postBuildSpinner", "buildTracesSpinner", "end", "analysisEnd", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "previewModeId", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "adapterPath", "tracingRoot", "distPath", "e", "errorCode", "getErrorCodeForTelemetry", "cur", "__NEXT_TEST_MODE", "warnAboutTurbopackBuilds", "mode", "isTurboSession", "sync", "warningStr", "turbopackPersistentCaching", "NEXT_RSPACK", "name"], "mappings": "AAOA,OAAO,mCAAkC;AAEzC,SAASA,aAAa,QAA6B,YAAW;AAC9D,SAASC,IAAI,EAAEC,MAAM,QAAQ,oBAAmB;AAChD,SAASC,MAAM,QAAQ,+BAA8B;AACrD,SAASC,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,QAAQ,KAAI;AACnB,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,0BAAyB;AACvD,OAAOC,aAAa,6BAA4B;AAChD,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,sCAAqC;AAC5D,OAAOC,UAAU,OAAM;AACvB,SACEC,0CAA0C,EAC1CC,8BAA8B,EAC9BC,mBAAmB,EACnBC,eAAe,EACfC,6BAA6B,EAC7BC,mBAAmB,EACnBC,UAAU,EACVC,kBAAkB,EAClBC,2BAA2B,EAC3BC,0CAA0C,EAC1CC,sCAAsC,EACtCC,kCAAkC,EAClCC,mBAAmB,EACnBC,uBAAuB,EACvBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,QAAQ,EAAEC,UAAU,QAAQ,qBAAoB;AACzD,SAASC,YAAY,QAAQ,wBAAuB;AACpD,OAAOC,oBACLC,mBAAmB,QACd,4BAA2B;AAQlC,SAASC,WAAW,QAAQ,sBAAqB;AACjD,SAASC,eAAe,QAAQ,0BAAyB;AACzD,SAASC,oBAAoB,QAAQ,gCAA+B;AACpE,SACEC,aAAa,EACbC,cAAc,EACdC,wBAAwB,EACxBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,sBAAsB,EACtBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,mBAAmB,EACnBC,mBAAmB,EACnBC,kBAAkB,EAClBC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,EACzBC,gCAAgC,EAChCC,0BAA0B,EAC1BC,oBAAoB,EACpBC,oCAAoC,QAC/B,0BAAyB;AAChC,SACEC,eAAe,EACfC,cAAc,EACdC,qBAAqB,QAChB,6BAA4B;AAEnC,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,WAAW,QAAQ,oBAAmB;AAC/C,YAAYC,mBAAmB,oBAAmB;AAClD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,+BAA+B,QAC1B,2BAA0B;AAEjC,SACEC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,gBAAgB,EAChBC,yBAAyB,EACzBC,oCAAoC,EACpCC,mBAAmB,EACnBC,gBAAgB,QACX,sBAAqB;AAE5B,SAASC,SAAS,QAAQ,uBAAsB;AAChD,SACEC,kBAAkB,EAClBC,6BAA6B,EAC7BC,cAAc,QACT,YAAW;AAClB,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,YAAYC,SAAS,eAAc;AACnC,OAAOC,mBAAmB,YAAW;AACrC,SAASC,KAAK,EAAEC,cAAc,EAAEC,SAAS,QAAmB,WAAU;AACtE,SACEC,sBAAsB,EACtBC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,wBAAwB,EACxBC,6BAA6B,EAC7BC,WAAW,QACN,UAAS;AAIhB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,OAAOC,aAAa,kBAAiB;AAErC,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAuB;AACrD,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,QAAO;AACrE,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,aAAa,QAAQ,0BAAyB;AACvD,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SACEC,aAAa,EACbC,2BAA2B,EAC3BC,UAAU,EACVC,uBAAuB,EACvBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,mCAAmC,EACnCC,0BAA0B,EAC1BC,2BAA2B,QACtB,0CAAyC;AAChD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,gBAAgB,QAA0B,kBAAiB;AACpE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SAASC,eAAe,QAAQ,4BAA2B;AAC3D,SAASC,wBAAwB,QAAQ,qCAAoC;AAC7E,SAASC,sBAAsB,QAAQ,+BAA8B;AACrE,SAASC,iBAAiB,QAAQ,eAAc;AAChD,SAASC,kCAAkC,QAAQ,+CAA8C;AAEjG,SAASC,cAAc,QAAQ,8CAA6C;AAC5E,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,QACb,mCAAkC;AACzC,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,6BAA4B;AAE7E,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,2BAA2B,QAAQ,+CAA8C;AAE1F,OAAOC,iBAAiB,wBAAuB;AAC/C,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,iCAAgC;AACvC,SAASC,YAAY,EAAEC,2BAA2B,QAAQ,kBAAiB;AAC3E,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,6BAA6B,QAAQ,oCAAmC;AAEjF,SACEC,oCAAoC,EACpCC,6BAA6B,QAExB,+DAA8D;AAErE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,0BAA0B,QAAQ,gCAA+B;AAC1E,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,oBAAmB;AACrD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,+BAA8B;AACnE,SAASC,yBAAyB,QAAQ,6BAA4B;AACtE,SAASC,mBAAmB,QAAQ,sBAAqB;AACzD,SAASC,mBAAmB,QAAQ,2BAA0B;AA2H9D;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACA7I;IACAJ;IACAC;IACAE;IACAD;CACD;AAiBD,OAAO,IAAA,AAAKgJ,mCAAAA;IACV;;GAEC;IAED;;GAEC;IAED;;;GAGC;IAED;;;GAGC;IAGD;;GAEC,GACD,mBAAmB;IAEnB;;GAEC;;WA3BSA;MA+BX;AAgFD,SAASC,YAAYC,IAAY;IAC/B,MAAMC,aAAazD,mBAAmBwD,MAAM;QAC1CE,iBAAiB;IACnB;IACA,OAAO;QACLF;QACAG,OAAO5I,oBAAoB0I,WAAWG,EAAE,CAACC,MAAM;QAC/CC,WAAWL,WAAWK,SAAS;QAC/BC,YAAYN,WAAWM,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWvK,KAAKwK,IAAI,CAACF,SAAS;IACpC,IAAI3G,cAAc8G,IAAI,IAAI,CAAC9G,cAAc+G,cAAc,EAAE;QACvD,MAAMC,WAAWpL,WAAWgL;QAE5B,IAAI,CAACI,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBC,QAAQC,GAAG,CACT,GAAG/F,IAAIgG,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOR;AACT;AAEA,eAAeS,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMzL,GAAG0L,SAAS,CAACF,UAAUC,SAAS;AACxC;AAEA,SAASE,aAAaH,QAAgB;IACpC,OAAOxL,GAAG4L,QAAQ,CAACJ,UAAU;AAC/B;AAEA,eAAeK,cACbL,QAAgB,EAChBM,QAAW;IAEX,MAAMP,cAAcC,UAAUrD,eAAe2D;AAC/C;AAEA,eAAeC,aAA+BP,QAAgB;IAC5D,OAAOQ,KAAKC,KAAK,CAAC,MAAMN,aAAaH;AACvC;AAEA,eAAeU,uBACbrB,OAAe,EACfiB,QAAyC;IAEzC,MAAMD,cAActL,KAAKwK,IAAI,CAACF,SAAStI,qBAAqBuJ;AAC9D;AAEA,eAAeK,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACPxB,OAAO,EACPyB,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAK1G,oBAAoB0G,OAAOT,SAASU,QAAQ;WAC7DP,OAAOQ,IAAI,CAACb,kBAAkBc,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAEhN,QACtDmM,UACA,iDAAiD,CAAC;IAEpD,MAAMhB,cACJhL,KAAKwK,IAAI,CAACF,SAAS5I,0BAA0BoK,SAAS,oBACtDe;AAEJ;AAoBA,eAAeC,6BACbxC,OAAe,EACfiB,QAAiC;IAEjC,MAAMD,cACJtL,KAAKwK,IAAI,CAACF,SAASnI,kBAAkBa,4BACrCuI;AAEJ;AAWA,eAAewB,iCACbzC,OAAe,EACf0C,mBAAgD;IAEhD,MAAM1B,cACJtL,KAAKwK,IAAI,CAACF,SAASlI,wBACnB4K;AAEJ;AAEA,eAAeC,oBACb3C,OAAe,EACf4C,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGhB,GAAG,CAAC,CAACiB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUrO,OAAOkO,EAAEG,QAAQ,EAAEzD,MAAM;YACnC0D,MAAMJ,EAAEI,IAAI;YACZnB,UAAUnN,OAAOkO,EAAEf,QAAQ,IAAI,MAAM;gBAAEoB,KAAK;YAAK,GAAG3D,MAAM;YAC1D4D,QAAQN,EAAEM,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIZ,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBa,aAAa,EAAE;QACjCZ,OAAOY,aAAa,GAAGb,OAAOC,MAAM,CAACY,aAAa,CAACxB,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEf,UAAUnN,OAAOkO,EAAEf,QAAQ,IAAI,MAAM;oBAAEoB,KAAK;gBAAK,GAAG3D,MAAM;gBAC1D4D,QAAQN,EAAEM,MAAM;YAClB,CAAA;IACF;IAEA,MAAMxC,cAActL,KAAKwK,IAAI,CAACF,SAASzI,kBAAkB;QACvDmM,SAAS;QACTb;IACF;AACF;AAEA,MAAMc,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB7D,OAAe,EACf8D,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BtB,mBAAgD,EAChDuB,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMtJ,gBACJ,kFAAkF;QAClFwH,oBAAoB4B,MAAM,EAC1BtE,SACA8D,SAASW,KAAK,EACdV,sBACAC,uBACAtB,oBAAoBE,MAAM,EAC1BqB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMM,QAAQ;eACdhC,oBAAoBiC,KAAK;YAC5BjP,KAAKwK,IAAI,CAACwC,oBAAoBE,MAAM,CAAC5C,OAAO,EAAElI;eAC3CuM,eAAeO,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQpP,IAAI,GAAG;oBACtDmP,IAAIG,IAAI,CAACF,QAAQpP,IAAI;gBACvB;gBACA,OAAOmP;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAMlE,WAAWjL,KAAKwK,IAAI,CAACwC,oBAAoB4B,MAAM,EAAEI;YACvD,MAAMO,aAAavP,KAAKwK,IAAI,CAC1BF,SACA2D,sBACAjO,KAAKwP,QAAQ,CAAClB,uBAAuBrD;YAEvC,MAAMxL,GAAGgQ,KAAK,CAACzP,KAAK0P,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMlQ,GAAGmQ,QAAQ,CAAC3E,UAAUsE;QAC9B;QAEA,IAAIf,mBAAmB;YACrB,MAAMqB,mBAAmB7P,KAAKwK,IAAI,CAChCF,SACA2D,sBACAjO,KAAKwP,QAAQ,CAAClB,uBAAuBhE,UACrCnI,kBACA;YAGF,MAAM1C,GAAGgQ,KAAK,CAACzP,KAAK0P,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMlQ,GAAGmQ,QAAQ,CACf5P,KAAKwK,IAAI,CAACF,SAASnI,kBAAkB,kBACrC0N;QAEJ;QAEA,MAAM5J,cACJjG,KAAKwK,IAAI,CAACF,SAASnI,kBAAkB,UACrCnC,KAAKwK,IAAI,CACPF,SACA2D,sBACAjO,KAAKwP,QAAQ,CAAClB,uBAAuBhE,UACrCnI,kBACA,UAEF;YAAE2N,WAAW;QAAK;QAEpB,IAAIlB,QAAQ;YACV,MAAMmB,oBAAoB/P,KAAKwK,IAAI,CAACF,SAASnI,kBAAkB;YAC/D,IAAI5C,WAAWwQ,oBAAoB;gBACjC,MAAM9J,cACJ8J,mBACA/P,KAAKwK,IAAI,CACPF,SACA2D,sBACAjO,KAAKwP,QAAQ,CAAClB,uBAAuBhE,UACrCnI,kBACA,QAEF;oBAAE2N,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmB9C,MAA0B;IACpD,IACEA,OAAO+C,YAAY,CAACC,IAAI,IACxBhD,OAAO+C,YAAY,CAACC,IAAI,KAAKtQ,cAAcqQ,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOhD,OAAO+C,YAAY,CAACC,IAAI;IACjC;IAEA,IAAIhD,OAAO+C,YAAY,CAACE,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACpD,OAAO+C,YAAY,CAACC,IAAI,IAAI,GAAGE,KAAKG,KAAK,CAAC7Q,GAAG8Q,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAItD,OAAO+C,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOhD,OAAO+C,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMO,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAED,OAAO,SAASC,mBACd3D,MAA0B,EAC1B4D,OAMC;IAED,MAAM,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE,GAAGF;IACzC,OAAO,IAAInR,OAAO8Q,kBAAkB;QAClCQ,QAAQnM;QACRoM,YAAYlB,mBAAmB9C;QAC/BiE,YAAY;YACVH,4BAAAA,SAAUI,GAAG;QACf;QACAC,iBAAiB;YACfL,4BAAAA,SAAUM,KAAK;QACjB;QACAP;QACAQ,kBAAkBrE,OAAO+C,YAAY,CAACuB,yBAAyB;QAC/D,kEAAkE;QAClEC,gBAAgB;QAChBC,qBAAqBxE,OAAO+C,YAAY,CAAC0B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACb3E,MAA0B,EAC1B4E,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpB7D,aAAmB;IAEnB,MAAM8D,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;IAEV,MAAMD,UACJH,KACA;QACEK,aAAa;QACbC,YAAYlF;QACZ6E;QACAM,QAAQ;QACRC,QAAQtS,KAAKwK,IAAI,CAACsH,KAAKE;QACvBd,YAAYlB,mBAAmB9C;IACjC,GACAiB;AAEJ;AAEA,eAAeoE,WACbC,cAAuB,EACvBlI,OAAe,EACf6D,aAAmB,EACnBjB,MAA0B;IAE1B,IAAIsF,gBAAgB;QAClB,OAAO,MAAM/S,GAAG4L,QAAQ,CAACrL,KAAKwK,IAAI,CAACF,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM6D,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMlK,gBAAgBsI,OAAOtI,eAAe,EAAE7E;AAChE;AAEA,eAAe,eAAe0S,MAC5BX,GAAW,EACXY,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,iBAAiB,KAAK,EACtBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMT,iBAAiBS,0BAA0B;IACjD9L,iBAAiBgM,aAAa,GAAGA;IACjC,MAAMC,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAMpF,gBAAgBnJ,MAAM,cAAcwO,WAAW;YACnDC,WAAWR;YACXS,cAAcC,OAAOX;YACrBhF,SAAS4F,QAAQC,GAAG,CAACC,cAAc;QACrC;QAEA3M,iBAAiBgH,aAAa,GAAGA;QACjChH,iBAAiB2K,GAAG,GAAGA;QACvB3K,iBAAiB4L,UAAU,GAAGA;QAC9B5L,iBAAiBuL,wBAAwB,GAAGA;QAC5CvL,iBAAiB2L,UAAU,GAAGA;QAC9B3L,iBAAiByL,cAAc,GAAGA;QAElC,MAAMzE,cAAcW,YAAY,CAAC;gBAiYXiF;YAhYpB,4EAA4E;YAC5E,MAAM,EAAEpF,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACXmF,OAAO,CAAC,IAAM7U,cAAc2S,KAAK,OAAOhN;YAC3CqC,iBAAiBwH,cAAc,GAAGA;YAElC,MAAMsF,6BAA6B,IAAIpQ;YACvC,MAAMqJ,SAA6B,MAAMiB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZlL,qBACE,IACEJ,WAAWzB,wBAAwB+P,KAAK;wBACtC,sCAAsC;wBACtCO,QAAQ;wBACRK;wBACAE;oBACF,IACFqB;YAGNV,eAAerG;YAEf0G,QAAQC,GAAG,CAACK,kBAAkB,GAAGhH,OAAOiH,YAAY,IAAI;YACxDhN,iBAAiB+F,MAAM,GAAGA;YAE1B,IAAI8E,eAAe;YACnB,IAAI9J,sBAAsBgF,SAAS;gBACjC8E,eAAe9E,OAAO5C,OAAO;gBAC7B4C,OAAO5C,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUtK,KAAKwK,IAAI,CAACsH,KAAK5E,OAAO5C,OAAO;YAC7CnD,iBAAiBmD,OAAO,GAAGA;YAC3BpF,UAAU,SAASnD;YACnBmD,UAAU,WAAWoF;YAErB,MAAMwB,UAAU,MAAMyG,WACpBC,gBACAlI,SACA6D,eACAjB;YAEF/F,iBAAiB2E,OAAO,GAAGA;YAE3B,IAAImH,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACflO,IAAIiG,IAAI,CAAC;oBACT6I,QAAQQ,IAAI,CAAC;gBACf;gBACAtP,IAAIuP,IAAI,CAAC;gBACT,MAAMlG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAM5F,gBAAgB;wBACpBoB;wBACA4C;oBACF;gBACF;gBAEFpI,IAAIuP,IAAI,CAAC;gBACT,MAAMpP;gBACNmB;gBACAwN,QAAQQ,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAIjB,iBAAiBX,gBAAgB;gBACnCrJ,kBAAkB+D;YACpB;YAEA,MAAMoH,eAA6B,MAAMnG,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAM3N,iBAAiB+L;YAEvC,MAAM,EAAEqH,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;YACzC,MAAMI,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9C5N,iBAAiB2N,WAAW,GAAGA;YAC/B3N,iBAAiB6N,gBAAgB,GAAG9H,OAAO+H,iBAAiB;YAC5D9N,iBAAiB+N,iBAAiB,GAAGhI,OAAOiI,kBAAkB;YAE9D,MAAM5K,WAAWF,YAAYC;YAE7B,MAAM8K,YAAY,IAAI7Q,UAAU;gBAAE+F;YAAQ;YAE1CpF,UAAU,aAAakQ;YAEvB,MAAMC,YAAYrV,KAAKwK,IAAI,CAACsH,KAAK;YACjC,MAAM,EAAEwD,QAAQ,EAAE1G,MAAM,EAAE,GAAG1N,aAAa4Q;YAC1C3K,iBAAiBmO,QAAQ,GAAGA;YAC5BnO,iBAAiByH,MAAM,GAAGA;YAE1B,MAAMmD,qBAA6C;gBACjDwD,KAAK,OAAO3G,WAAW;gBACvBG,OAAO,OAAOuG,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAME,gBAAgB,MAAMnN,4BAA4B;gBACtDoN,SAAS;gBACTnL;YACF;YACAnD,iBAAiBqO,aAAa,GAAGA;YAEjC,MAAME,WAAW1V,KACdwP,QAAQ,CAACsC,KAAKwD,YAAY1G,UAAU,IACpC+G,UAAU,CAAC;YACd,MAAMC,eAAerW,WAAW8V;YAEhCD,UAAUS,MAAM,CACd7R,gBAAgB8N,KAAK5E,QAAQ;gBAC3B4I,gBAAgB;gBAChBC,YAAY;gBACZL;gBACAM,YAAY,CAAC,CAAE,MAAMlW,OAAO,YAAY;oBAAEmW,KAAKnE;gBAAI;gBACnDoE,gBAAgB;gBAChBC,WAAW;gBACXb,UAAU,CAAC,CAACA;gBACZ1G,QAAQ,CAAC,CAACA;YACZ;YAGF1K,iBAAiBlE,KAAK2Q,OAAO,CAACmB,MAAMsE,IAAI,CAAC,CAACC,SACxCjB,UAAUS,MAAM,CAACQ;YAGnB9P,gBAAgBvG,KAAK2Q,OAAO,CAACmB,MAAM5E,QAAQkJ,IAAI,CAAC,CAACC,SAC/CjB,UAAUS,MAAM,CAACQ;YAGnB,qDAAqD;YACrD,MAAM,EAAEC,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMvO,mBAAmB;gBACjE8J;gBACA0E,KAAK;gBACL5D;YACF;YAEA3K,aAAa;gBACXwO,YAAY;gBACZC,QAAQ;gBACRJ;gBACAC;YACF;YAEA,MAAMI,eAAeC,QAAQ1J,OAAO2J,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgB9D;YAEpC,MAAMmE,sBAA+D;gBACnElF;gBACAlD;gBACA0G;gBACAzC;gBACAkE;gBACAJ;gBACAvB;gBACAjH;gBACAjB;gBACA3C;YACF;YAEA,MAAM0M,iBAAiB,MAAM9I,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMrP,GAAGgQ,KAAK,CAACnF,SAAS;wBAAEqF,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOuH,KAAK;oBACZ,IAAInR,QAAQmR,QAAQA,IAAIC,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMD;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMpS,YAAYyF,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI8M,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIlK,OAAOmK,YAAY,IAAI,CAAC7E,gBAAgB;gBAC1C,MAAMlR,gBAAgBgJ,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACsE,UAAU,CAACuE,eACd,MAAM3L,kBAAkBwP;YAE1B,IAAIpI,UAAU,mBAAmB1B,QAAQ;gBACvCpI,IAAIwS,KAAK,CACP;gBAEF,MAAMlC,UAAUmC,KAAK;gBACrB3D,QAAQQ,IAAI,CAAC;YACf;YAEA,MAAMoD,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBX,aAAa,IAAI;YACpC;YACA3B,UAAUS,MAAM,CAAC;gBACf8B,WAAWxT;gBACXyT,SAASJ;YACX;YAEA,MAAMK,mBAAmBtQ,uBACvB2F,OAAO4K,cAAc,EACrBlJ;YAGF,MAAMmJ,oBAA8BtM,KAAKC,KAAK,CAC5CkI,QAAQC,GAAG,CAACmE,uBAAuB,IAAI;YAGzC,IAAIC,aAAarB,QAAQhD,QAAQC,GAAG,CAACmE,uBAAuB,IACxDD,oBACA,CAAChF,cAAcuC,WACb,MAAMnH,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3D5I,iBAAiBoP,UAAU;oBACzB4C,gBAAgBL,iBAAiBM,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAElY,oBAAoB,MAAM,EAAE+M,OAAO4K,cAAc,CAACtN,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAM8N,qCAAqC,IAAID,OAC7C,CAAC,CAAC,EAAEhY,8BAA8B,MAAM,EAAE6M,OAAO4K,cAAc,CAACtN,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAM+N,UAAUvY,KAAKwK,IAAI,CAAE8K,YAAY1G,QAAU;YACjD,MAAMS,WAAW;gBACf+I;gBACAE;aACD;YAED,MAAME,YAAYC,MAAMC,IAAI,CAAC,MAAMpS,cAAciS,UAC9ClM,MAAM,CAAC,CAAC2C,OAASK,SAASsJ,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAC7J,QACzDpC,IAAI,CAAClI,eAAewI,OAAO4K,cAAc,GACzCvL,GAAG,CAAC,CAACyC,OAAShP,KAAKwK,IAAI,CAAC+N,SAASvJ,MAAMtB,OAAO,CAACoE,KAAK;YAEvD,MAAMrD,yBAAyB+J,UAAUG,IAAI,CAAC,CAACnL,IAC7CA,EAAE6B,QAAQ,CAAChP;YAEb,MAAMyY,oBAAoBN,UAAUG,IAAI,CAAC,CAACnL,IACxCA,EAAE6B,QAAQ,CAAClP;YAGbgH,iBAAiBsH,sBAAsB,GAAGA;YAE1C,MAAMsK,eAAkC,MAAMvP,oBAAoB;gBAChEiM,SAAS;gBACTnL;YACF;YACAnD,iBAAiB4R,YAAY,GAAGA;YAEhC,MAAMhF,cAAc,MAAM5F,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZtK,mBAAmB;oBACjBwU,OAAO;oBACPlB,gBAAgB5K,OAAO4K,cAAc;oBACrCmB,WAAWtU,WAAWuU,KAAK;oBAC3BC,WAAWlB;oBACX3C;oBACA1G;gBACF;YAEJzH,iBAAiB4M,WAAW,GAAGA;YAE/B,IAAIqF;YACJ,IAAI/K;YAEJ,IAAIO,QAAQ;gBACV,MAAMyK,mBAA6B5N,KAAKC,KAAK,CAC3CkI,QAAQC,GAAG,CAACyF,sBAAsB,IAAI;gBAGxC,IAAIC,WAAW3C,QAAQhD,QAAQC,GAAG,CAACyF,sBAAsB,IACrDD,mBACA,MAAMlL,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZ5I,iBAAiB0I,QAAQ;wBACvBsJ,gBAAgB,CAACsB,eACf3B,iBAAiB4B,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChC3B,iBAAiB6B,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAKjE,UAAU,CAAC;oBAC9C;gBAGRyD,iBAAiB,MAAMjL,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZtK,mBAAmB;wBACjB2U,WAAWI;wBACXP,OAAO;wBACPC,WAAWtU,WAAWkV,GAAG;wBACzB/B,gBAAgB5K,OAAO4K,cAAc;wBACrCxC;wBACA1G;oBACF;gBAGJzH,iBAAiBiS,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAMtV,mBAAmB;gBAC/CwU,OAAO;gBACPlB,gBAAgB5K,OAAO4K,cAAc;gBACrCqB,WAAWX;gBACXS,WAAWtU,WAAWoV,IAAI;gBAC1BzE,UAAUA;gBACV1G;YACF;YACAzH,iBAAiB2S,eAAe,GAAGA;YAEnC,MAAME,gBAAgB9N,OAAOQ,IAAI,CAACqH;YAElC,MAAMkG,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAIjO;YACxB,IAAImN,gBAAgB;gBAClB/K,uBAAuBnC,OAAOQ,IAAI,CAAC0M;gBACnC,KAAK,MAAMe,UAAU9L,qBAAsB;oBACzC,MAAM+L,uBAAuB5T,iBAAiB2T;oBAC9C,MAAME,WAAWtG,WAAW,CAACqG,qBAAqB;oBAClD,IAAIC,UAAU;wBACZ,MAAMC,UAAUlB,cAAc,CAACe,OAAO;wBACtCF,wBAAwB3K,IAAI,CAAC;4BAC3B+K,SAAS3M,OAAO,CAAC,uBAAuB;4BACxC4M,QAAQ5M,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACAwM,YAAYK,GAAG,CAACH;gBAClB;YACF;YAEA,MAAMb,WAAWd,MAAMC,IAAI,CAACwB;YAC5B,2DAA2D;YAC3D1F,SAASG,WAAW,CAACrF,IAAI,IACpB7H,mCAAmC8R,UAAUrM,OAAOsN,QAAQ;YAGjErT,iBAAiBqN,QAAQ,GAAGA;YAE5B,MAAMiG,qBAAqBlB,SAASxE,MAAM;YAE1C,MAAM3G,WAAW;gBACfW,OAAOiL;gBACPzE,KAAKgE,SAASxE,MAAM,GAAG,IAAIwE,WAAW/F;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACR,aAAa;gBAChB,MAAM0H,yBAAyBT,wBAAwBlF,MAAM;gBAC7D,IAAIqE,kBAAkBsB,yBAAyB,GAAG;oBAChD5V,IAAIwS,KAAK,CACP,CAAC,6BAA6B,EAC5BoD,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACL,UAAUC,QAAQ,IAAIL,wBAAyB;wBACzDnV,IAAIwS,KAAK,CAAC,CAAC,GAAG,EAAE+C,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAMlF,UAAUmC,KAAK;oBACrB3D,QAAQQ,IAAI,CAAC;gBACf;YACF;YAEA,MAAMuG,yBAAmC,EAAE;YAC3C,MAAMC,eAAc7G,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB4B,UAAU,CAACvV;YACpD,MAAMya,YAAY,CAAC,EAACzB,kCAAAA,cAAgB,CAACnW,iCAAiC;YACtE,MAAM6X,qBACJ/G,WAAW,CAAC,UAAU,CAAC4B,UAAU,CAACvV;YAEpC,IAAIwV,cAAc;gBAChB,MAAMmF,6BAA6Bxb,WACjCS,KAAKwK,IAAI,CAAC6K,WAAW;gBAEvB,IAAI0F,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAI3D,MAAMlX,iCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAMiO,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMjF,QAAQkK,YAAa;oBAC9B,MAAMiH,oBAAoB,MAAM/Z,WAC9BjB,KAAKwK,IAAI,CAAC6K,WAAWxL,SAAS,MAAM,WAAWA,OAC/C7I,SAASia,IAAI;oBAEf,IAAID,mBAAmB;wBACrBL,uBAAuBrL,IAAI,CAACzF;oBAC9B;gBACF;gBAEA,MAAMqR,iBAAiBP,uBAAuB5F,MAAM;gBAEpD,IAAImG,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAI9D,MACR,CAAC,gCAAgC,EAC/B8D,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEP,uBAAuBnQ,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAM2Q,sBAAsB/M,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACxC;gBACjD,OACEA,KAAKuR,KAAK,CAAC,iCAAiCpb,KAAK0P,OAAO,CAAC7F,UAAU;YAEvE;YAEA,IAAIsR,oBAAoBpG,MAAM,EAAE;gBAC9BjQ,IAAIiG,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FoQ,oBAAoB3Q,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAM6Q,0BAA0B;gBAAC;aAAS,CAAC9O,GAAG,CAAC,CAACiB,IAC9CN,OAAOsN,QAAQ,GAAG,GAAGtN,OAAOsN,QAAQ,GAAGhN,GAAG,GAAGA;YAG/C,MAAM8N,wBAAwB1E,QAAQ1J,OAAO+C,YAAY,CAACsL,SAAS;YACnE,MAAMC,0BAA0B5E,QAC9B1J,OAAO+C,YAAY,CAACwL,cAAc;YAEpC,MAAMC,kBAAkBnT,qBAAqB2E,OAAO+C,YAAY,CAAC0L,GAAG;YAEpE,MAAMC,qBAAqB5b,KAAKwK,IAAI,CAACF,SAASpI;YAC9C,MAAM2Z,iBAAiC1N,cACpCU,UAAU,CAAC,4BACXmF,OAAO,CAAC;gBACP,MAAM8H,eAAezY,gBAAgB;uBAChC+K,SAASW,KAAK;uBACbX,SAASmH,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAM5I,gBAAsC,EAAE;gBAC9C,MAAMoP,eAAqC,EAAE;gBAE7C,KAAK,MAAMvP,SAASsP,aAAc;oBAChC,IAAIxY,eAAekJ,QAAQ;wBACzBG,cAAc2C,IAAI,CAAC1F,YAAY4C;oBACjC,OAAO,IAAI,CAAC/G,eAAe+G,QAAQ;wBACjCuP,aAAazM,IAAI,CAAC1F,YAAY4C;oBAChC;gBACF;gBAEA,OAAO;oBACLwB,SAAS;oBACTgO,UAAU;oBACVC,eAAe,CAAC,CAAC/O,OAAO+C,YAAY,CAACiM,mBAAmB;oBACxD1B,UAAUtN,OAAOsN,QAAQ;oBACzB/F,WAAWA,UAAUlI,GAAG,CAAC,CAAC4P,IACxBhU,iBAAiB,YAAYgU,GAAGd;oBAElC9G,SAASA,QAAQhI,GAAG,CAAC,CAAC4P,IAAMhU,iBAAiB,UAAUgU;oBACvD3H,UAAU;wBACRG,aAAa,EAAE;wBACfC,YAAY,EAAE;wBACdC,UAAU,EAAE;oBACd;oBACAlI;oBACAoP;oBACAK,YAAY,EAAE;oBACdC,MAAMnP,OAAOmP,IAAI,IAAI7I;oBACrB8I,KAAK;wBACHC,QAAQ5V;wBACR,yFAAyF;wBACzF,4DAA4D;wBAC5D6V,YAAY,GAAG7V,WAAW,EAAE,EAAEE,8BAA8B,EAAE,EAAEH,4BAA4B,EAAE,EAAEK,qCAAqC;wBACrI0V,gBAAgB/V;wBAChBgW,mBAAmB5V;wBACnB6V,mBAAmB/V;wBACnBgW,QAAQrc;wBACRsc,gBAAgBvc;wBAChBwc,uBAAuB/V;wBACvBgW,uBAAuBhc;wBACvBic,0BAA0Blc;oBAC5B;oBACAmc,gBAAgB;wBACdC,YAAYlW;wBACZmW,aAAalW;oBACf;oBACAmW,4BAA4BlQ,OAAOkQ,0BAA0B;oBAC7DzB,KAAKD,kBACD;wBACE2B,OAAO;4BACL9I,SAAS;gCACP,CAAC/T,mBAAmB,EAAE;4BACxB;wBACF;oBACF,IACAgT;gBACN;YACF;YAEFqI,eAAerH,QAAQ,GAAG;gBACxBG,aAAaH,SAASG,WAAW,CAACpI,GAAG,CAAC,CAAC4P,IACrChU,iBAAiB,WAAWgU;gBAE9BvH,YAAYJ,SAASI,UAAU,CAACrI,GAAG,CAAC,CAAC4P,IACnChU,iBAAiB,WAAWgU;gBAE9BtH,UAAUL,SAASK,QAAQ,CAACtI,GAAG,CAAC,CAAC4P,IAAMhU,iBAAiB,WAAWgU;YACrE;YAEA,IAAImB;YAIJ,IAAIpQ,OAAO+C,YAAY,CAACsN,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAACtQ,CAAAA,OAAOiI,kBAAkB,IAAI,EAAE,AAAD,EAAG9I,MAAM,CACnE,CAAC8P,IAAW,CAACA,EAAEsB,QAAQ;gBAEzBH,sBAAsBhW,yBACpB;uBAAIiS;iBAAS,EACbrM,OAAO+C,YAAY,CAACyN,2BAA2B,GAC3CF,uBACA,EAAE,EACNtQ,OAAO+C,YAAY,CAAC0N,6BAA6B;gBAEnDxW,iBAAiBmW,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAMtS,cACJhL,KAAKwK,IAAI,CAACF,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMzC,uBAAuB+L,QAAQC,GAAG,CAACC,cAAc;YACvD,MAAMhM,uBAAuB;gBAC3B8V,YAAY;YACd;YAEA,MAAMtP,wBAAwBpB,OAAOoB,qBAAqB,IAAIwD;YAE9D,MAAM+L,oBAAoB7d,KAAKwK,IAAI,CACjCF,SACAnI,kBACAL;YAGF,IAAIgc;YACJ,IAAIC,qBAA+CvK;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAMwK,iBACJ9Q,OAAO+C,YAAY,CAACgO,kBAAkB,IACrC/Q,OAAO+C,YAAY,CAACgO,kBAAkB,KAAKzK,aAC1C,CAACtG,OAAOgR,OAAO;YACnB,MAAMC,6BACJjR,OAAO+C,YAAY,CAACmO,sBAAsB;YAC5C,MAAMC,qCACJnR,OAAO+C,YAAY,CAACqO,yBAAyB,IAC5CpR,OAAO+C,YAAY,CAACqO,yBAAyB,KAAK9K,aACjDL;YAEJhF,cAAcoQ,YAAY,CACxB,6BACA5K,OAAO,CAAC,CAACzG,OAAOgR,OAAO;YAEzB/P,cAAcoQ,YAAY,CAAC,oBAAoB5K,OAAOqK;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAIjH,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAtS,IAAIuP,IAAI,CAAC;YACTjM,iBAAiB,kBAAkB+F;YAEnC,MAAMrG,uBAAuB;gBAC3B8V,YAAY;gBACZY,cAAc;oBACZR,gBAAgBrK,OAAOqK;gBACzB;YACF;YAEA,IAAIS,kBAAkBC,QAAQ/N,OAAO;YACrC,IAAI,CAAC6B,gBAAgB;gBACnB,IAAIQ,aAAa;oBACf,MAAM,EACJ2L,UAAUC,gBAAgB,EAC1BH,iBAAiBjR,CAAC,EAClB,GAAGqR,MACJ,GAAG,MAAM7V,eACR4K,QAAQC,GAAG,CAACiL,yBAAyB,KAAKtL,aACxCI,QAAQC,GAAG,CAACiL,yBAAyB,KAAK;oBAE9CL,kBAAkBjR;oBAClBpF,iBAAiB,kBAAkB+F;oBAEnC2P,oBAAoBe,KAAKf,iBAAiB;oBAE1C,MAAMiB,iBAAiB3V,iBAAiBwV;oBACxC9Z,IAAIka,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;oBAEtD3J,UAAUS,MAAM,CACdxR,oBAAoB4T,YAAY;wBAC9BgH,SAAS;wBACTC,mBAAmB9O,KAAK+O,KAAK,CAACP;wBAC9BnE;oBACF;gBAEJ,OAAO;oBACL,IACE0D,8BACAE,oCACA;wBACA,IAAIa,oBAAoB;wBAExB,MAAMpX,uBAAuB;4BAC3B8V,YAAY;wBACd;wBAEA,MAAMwB,qBAAqBlY,aAAa8W,gBAAgB;4BACtD;yBACD,EAAE5H,IAAI,CAAC,CAACiJ;4BACPjX,iBAAiB,+BAA+B+F;4BAChD2P,oBAAoBuB,IAAIvB,iBAAiB;4BACzCoB,qBAAqBG,IAAIV,QAAQ;4BAEjC,IAAIN,oCAAoC;gCACtC,MAAMiB,mBAAmB,IAAI3f,OAC3B+Q,QAAQC,OAAO,CAAC,2BAChB;oCACEI,oBAAoB,CAAC;oCACrBU,gBAAgB;oCAChBP,YAAY;oCACZU,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGFmM,qBAAqBuB,iBAClB3X,kBAAkB,CAAC;oCAClBmK;oCACA5E;oCACA5C;oCACA,+CAA+C;oCAC/CiV,mBAAmB5Z,8BAA8B,IAAI6Z;oCACrD9Q,aAAa,EAAE;oCACf+Q,gBAAgB;oCAChB3B;oCACAxP;oCACA0E,aAAa;gCACf,GACC0M,KAAK,CAAC,CAACxI;oCACNtM,QAAQ0M,KAAK,CAACJ;oCACdtD,QAAQQ,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAAC+J,4BAA4B;4BAC/B,MAAMiB;4BACN,MAAMtX,uBAAuB;gCAC3B8V,YAAY;4BACd;wBACF;wBAEA,MAAM+B,mBAAmBzY,aAAa8W,gBAAgB;4BACpD;yBACD,EAAE5H,IAAI,CAAC,CAACiJ;4BACPH,qBAAqBG,IAAIV,QAAQ;4BACjCvW,iBACE,oCACA+F;wBAEJ;wBACA,IAAIgQ,4BAA4B;4BAC9B,MAAMiB;4BACN,MAAMtX,uBAAuB;gCAC3B8V,YAAY;4BACd;wBACF;wBACA,MAAM+B;wBAEN,MAAM7X,uBAAuB;4BAC3B8V,YAAY;wBACd;wBAEA,MAAM1W,aAAa8W,gBAAgB;4BAAC;yBAAS,EAAE5H,IAAI,CAAC,CAACiJ;4BACnDH,qBAAqBG,IAAIV,QAAQ;4BACjCvW,iBAAiB,+BAA+B+F;wBAClD;wBAEA,MAAM4Q,iBAAiB3V,iBAAiB8V;wBACxCpa,IAAIka,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;wBAEtD3J,UAAUS,MAAM,CACdxR,oBAAoB4T,YAAY;4BAC9BgH,SAASW,uBAAuB5M;4BAChCkM;4BACAzE;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEkE,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAM3X,aACpD8W,gBACA;wBAEF5V,iBAAiB,kBAAkB+F;wBAEnC2P,oBAAoBe,KAAKf,iBAAiB;wBAE1C1I,UAAUS,MAAM,CACdxR,oBAAoB4T,YAAY;4BAC9BgH,SAASW,uBAAuB5M;4BAChCkM,mBAAmBN;4BACnBnE;wBACF;oBAEJ;gBACF;gBACA,MAAMlR,0BAA0B;oBAC9B2D;oBACA2S,WAAW1R;oBACXiH;oBACA0K,UAAU;wBACRC,YAAYjO;wBACZxH;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIsE,UAAU,CAACuE,iBAAiB,CAACX,gBAAgB;gBAC/C,MAAM1K,uBAAuB;oBAC3B8V,YAAY;gBACd;gBACA,MAAMpW,kBAAkBwP;gBACxB5O,iBAAiB,0BAA0B+F;YAC7C;YAEA,MAAM6R,qBAAqBjb,cAAc;YAEzC,MAAMkb,oBAAoBjgB,KAAKwK,IAAI,CAACF,SAAS7I;YAC7C,MAAMye,uBAAuBlgB,KAAKwK,IAAI,CAACF,SAAS7H;YAEhD,IAAI0d,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMtU,WAAW,IAAIC;YACrB,MAAMsU,yBAAyB,IAAItU;YACnC,MAAMuU,2BAA2B,IAAIvU;YACrC,MAAMyC,cAAc,IAAIzC;YACxB,MAAMwU,eAAe,IAAIxU;YACzB,MAAMyU,iBAAiB,IAAIzU;YAC3B,MAAM0U,mBAAmB,IAAI1U;YAC7B,MAAM2U,kBAAkB,IAAIpB;YAC5B,MAAMqB,cAAc,IAAIrB;YACxB,MAAMsB,qBAAqB,IAAItB;YAC/B,MAAMuB,gBAAgB,IAAIvB;YAC1B,MAAMwB,oBAAoB,IAAIxB;YAC9B,MAAMyB,YAAuB,IAAIzB;YACjC,IAAI0B,gBAAgB,MAAM1V,aAA4BqS;YACtD,MAAMsD,gBAAgB,MAAM3V,aAA4ByU;YACxD,MAAMmB,mBAAmBxS,SACrB,MAAMpD,aAA+B0U,wBACrC1M;YAEJ,MAAM6N,gBAAwC,CAAC;YAE/C,IAAIzS,QAAQ;gBACV,MAAM0S,mBAAmB,MAAM9V,aAC7BxL,KAAKwK,IAAI,CAACF,SAASnI,kBAAkBI;gBAGvC,IAAK,MAAMgf,OAAOD,iBAAkB;oBAClCD,aAAa,CAACE,IAAI,GAAG/a,iBAAiB+a;gBACxC;gBAEA,MAAMjW,cACJtL,KAAKwK,IAAI,CAACF,SAAS9H,2BACnB6e;YAEJ;YAEAzN,QAAQC,GAAG,CAAC2N,UAAU,GAAGzf;YAEzB,MAAM0f,SAAS5Q,mBAAmB3D,QAAQ;gBAAE6D,oBAAoB,CAAC;YAAE;YAEnE,MAAM2Q,gBAAgB9N,QAAQ+N,MAAM;YACpC,MAAMC,kBAAkBzT,cAAcU,UAAU,CAAC;YAEjD,MAAMgT,0BAAmD;gBACvD7T,SAAS;gBACT8T,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnBxC,cAAc,EACdyC,qBAAqB,EACtB,GAAG,MAAMN,gBAAgB9S,YAAY,CAAC;oBAcV5B;gBAb3B,IAAIiG,eAAe;oBACjB,OAAO;wBACL4O,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrBxC,gBAAgB,CAAC,CAACnK;wBAClB4M,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChEnV;gBACF,MAAMoV,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAa3L,SAAQ1J,2BAAAA,OAAO+C,YAAY,CAACuS,GAAG,qBAAvBtV,yBAAyBuV,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgB/S,UAAU,CACvD;gBAEF,MAAM8T,oCACJD,uBAAuB5T,YAAY,CACjC,UACEgM,sBACC,MAAM2G,OAAOmB,wBAAwB,CAAC;wBACrC/Y,MAAM;wBACNS;wBACAgY;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuB5T,YAAY,CAC/D;wBAWa5B,cACMA;2BAXjB4N,sBACA2G,OAAOsB,YAAY,CAAC;wBAClBjR;wBACAjI,MAAM;wBACNS;wBACA6X;wBACAG;wBACA/G,WAAWD;wBACXG,gBAAgBD;wBAChBwH,kBAAkB9V,OAAO8V,gBAAgB;wBACzCjX,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;wBAC7BkX,aAAa,GAAE/V,gBAAAA,OAAOmP,IAAI,qBAAXnP,cAAa+V,aAAa;wBACzCC,kBAAkBhW,OAAOiW,MAAM;wBAC/BC,WAAWlW,OAAO+C,YAAY,CAAC0L,GAAG;wBAClC0H,mBAAmBnW,OAAO+C,YAAY,CAACqT,SAAS;wBAChDxX;wBACAyW;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACE/Y,MAAM0Z;oBACNjZ;oBACAgY;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxD7Z,MAAM0Z;oBACNjZ;oBACAgY;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAIxC,iBAAiB;gBAErB,MAAMkE,uBAAuB,MAAMve,oBACjC;oBAAEqN,OAAO0O;oBAAe5L,KAAK6L;gBAAiB,GAC9C9W,SACA4C,OAAO+C,YAAY,CAAC2T,QAAQ;gBAG9B,MAAMrV,qBAAyCmC,QAC7C1Q,KAAKwK,IAAI,CAACF,SAASnI,kBAAkBG;gBAGvC,MAAMuhB,iBAAiBjV,SAClB8B,QACC1Q,KAAKwK,IAAI,CACPF,SACAnI,kBACAY,4BAA4B,YAGhC;gBACJ,MAAM+gB,oBAAoBD,iBAAiB,IAAI5X,QAAQ;gBACvD,IAAI4X,kBAAkBC,mBAAmB;oBACvC,IAAK,MAAMC,MAAMF,eAAeG,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASJ,eAAeG,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBvJ,GAAG,CAAC0J;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMF,eAAeM,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASJ,eAAeM,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBvJ,GAAG,CAAC0J;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM1C,OAAOrV,OAAOQ,IAAI,CAAC6B,sCAAAA,mBAAoBuT,SAAS,EAAG;oBAC5D,IAAIP,IAAI5L,UAAU,CAAC,SAAS;wBAC1B2K;oBACF;gBACF;gBAEA,MAAM5B,QAAQ0F,GAAG,CACflY,OAAOC,OAAO,CAACiC,UACZc,MAAM,CACL,CAACC,KAAK,CAACoS,KAAKtS,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAMkV,WAAW9C;oBAEjB,KAAK,MAAM1X,QAAQoF,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAE+U;4BAAUxa;wBAAK;oBAC5B;oBAEA,OAAOsF;gBACT,GACA,EAAE,EAEH5C,GAAG,CAAC,CAAC,EAAE8X,QAAQ,EAAExa,IAAI,EAAE;oBACtB,MAAMya,gBAAgB1C,gBAAgB/S,UAAU,CAAC,cAAc;wBAC7DhF;oBACF;oBACA,OAAOya,cAAcxV,YAAY,CAAC;wBAChC,MAAMyV,aAAa9gB,kBAAkBoG;wBACrC,MAAM,CAAC2a,MAAMC,UAAU,GAAG,MAAMpf,kBAC9Bgf,UACAE,YACAja,SACA6W,eACAC,kBACAlU,OAAO+C,YAAY,CAAC2T,QAAQ,EAC5BD;wBAGF,IAAIe,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAI1K,WAAW;wBAEf,IAAIgK,aAAa,SAAS;4BACxBhK,WACEpC,WAAW+M,IAAI,CAAC,CAACxX;gCACfA,IAAIpG,iBAAiBoG;gCACrB,OACEA,EAAEmI,UAAU,CAAC4O,aAAa,QAC1B/W,EAAEmI,UAAU,CAAC4O,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIU;wBAEJ,IAAIZ,aAAa,SAASjL,gBAAgB;4BACxC,KAAK,MAAM,CAAC8L,cAAcC,eAAe,IAAIjZ,OAAOC,OAAO,CACzDkV,eACC;gCACD,IAAI8D,mBAAmBtb,MAAM;oCAC3BwQ,WAAWjB,cAAc,CAAC8L,aAAa,CAACxX,OAAO,CAC7C,yBACA;oCAEFuX,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAe1f,yBAAyB2U,YAC1C3J,QAAQC,OAAO,CACb,mDAEF3Q,KAAKwK,IAAI,CACP,AAAC6Z,CAAAA,aAAa,UAAU/O,WAAW1G,MAAK,KAAM,IAC9CyL;wBAGN,MAAMgL,iBAAiBhB,aAAa;wBACpC,MAAMiB,aAAajL,WACf,MAAM5V,8BAA8B;4BAClC4gB;4BACAD;4BACAtN,gBAAgB5K,OAAO4K,cAAc;4BACrClJ;4BACA1B;4BACA8L,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChEnP,MAAMwb,iBAAiBJ,kBAAmBpb;wBAC5C,KACA2J;wBAEJ,IAAI8R,8BAAAA,WAAYC,mBAAmB,EAAE;4BACnCC;wBACF;wBAEA,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAOF,8BAAAA,WAAYG,OAAO,MAAK,eAC/B,QAAOH,8BAAAA,WAAYI,WAAW,MAAK,eACnC,QAAOJ,8BAAAA,WAAYK,eAAe,MAAK,aACvC;4BACA,MAAMC,UAAUN,CAAAA,8BAAAA,WAAYK,eAAe,IACvC,OAAOL,WAAWK,eAAe,KAAK,WACpC;gCAACL,WAAWK,eAAe;6BAAC,GAC5BL,WAAWK,eAAe,GAC5BnS;4BAEJqO,wBAAwBC,SAAS,CAACjY,KAAK,GAAG;gCACxC6b,WAAW,EAAEJ,8BAAAA,WAAYI,WAAW;gCACpC,GAAIE,WAAW;oCAAEA;gCAAQ,CAAC;4BAC5B;wBACF;wBAEA,MAAMC,cAActX,mBAAmBuT,SAAS,CAC9CmD,mBAAmBpb,KACpB,GACG,SACAyb,8BAAAA,WAAYG,OAAO;wBAEvB,IAAI,CAACtS,eAAe;4BAClB0R,oBACER,aAAa,SACbiB,CAAAA,8BAAAA,WAAYhJ,GAAG,MAAK5Z,iBAAiBojB,MAAM;4BAE7C,IAAIzB,aAAa,SAAS,CAAC5e,eAAeoE,OAAO;gCAC/C,IAAI;oCACF,IAAIkc;oCAEJ,IAAI/f,cAAc6f,cAAc;wCAC9B,IAAIxB,aAAa,OAAO;4CACtBhE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAM0F,cACJ3B,aAAa,UAAUxa,OAAOob,mBAAmB;wCAEnDc,WAAWxX,mBAAmBuT,SAAS,CAACkE,YAAY;oCACtD;oCAEA,IAAIC,mBACF3B,cAAczV,UAAU,CAAC;oCAC3B,IAAIqX,eAAe,MAAMD,iBAAiBnX,YAAY,CACpD;4CASa5B,cACMA;wCATjB,OAAOuU,OAAOsB,YAAY,CAAC;4CACzBjR;4CACAjI;4CACAob;4CACA3a;4CACA6X;4CACAG;4CACAU,kBAAkB9V,OAAO8V,gBAAgB;4CACzCjX,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;4CAC7BkX,aAAa,GAAE/V,gBAAAA,OAAOmP,IAAI,qBAAXnP,cAAa+V,aAAa;4CACzCkD,UAAUF,iBAAiBG,KAAK;4CAChCP;4CACAE;4CACA1B;4CACA9I,WAAWD;4CACXG,gBAAgBD;4CAChB6K,cAAcnZ,OAAOmZ,YAAY;4CACjCC,eAAepZ,OAAO+C,YAAY,CAACqW,aAAa;4CAChDC,gBAAgB5iB,cAAc+G,cAAc,GACxC,QACAwC,OAAO+C,YAAY,CAACsW,cAAc;4CACtCC,oBAAoBtZ,OAAOuZ,kBAAkB;4CAC7CvD,kBAAkBhW,OAAOiW,MAAM;4CAC/BC,WAAWlW,OAAO+C,YAAY,CAAC0L,GAAG;4CAClC0H,mBAAmBnW,OAAO+C,YAAY,CAACqT,SAAS;4CAChDxX;4CACAyW;wCACF;oCACF;oCAGF,IAAI8B,aAAa,SAASY,iBAAiB;wCACzCnE,mBAAmB4F,GAAG,CAACzB,iBAAiBpb;wCACxC,0CAA0C;wCAC1C,IAAI7D,cAAc6f,cAAc;4CAC9BjB,WAAW;4CACXD,QAAQ;4CAER7f,IAAI6hB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYtjB,eAAeuG;4CAEjC,IACE,OAAOqc,aAAaxB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoBwB,aAAaxB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIwB,aAAaxB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEX/D,YAAY6F,GAAG,CAACzB,iBAAiB,EAAE;4CACrC;4CAEA,IAAIiB,aAAaW,iBAAiB,EAAE;gDAClChG,YAAY6F,GAAG,CACbzB,iBACAiB,aAAaW,iBAAiB;gDAEhC9B,gBAAgBmB,aAAaW,iBAAiB,CAACta,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;gDAE3BkY,QAAQ;4CACV;4CAEA,MAAMmC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9R,MAAM,GAAG;gDAE1C,IACE7H,OAAOiW,MAAM,KAAK,YAClByD,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI5P,MACR,CAAC,MAAM,EAAEvN,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAAC+c,WAAW;oDACd/F,YAAY6F,GAAG,CAACzB,iBAAiB;wDAC/B;4DACEgC,QAAQ,CAAC;4DACTxa,UAAU5C;4DACVqd,iBAAiBrd;4DACjBsd,qBAAqB,EAAE;4DACvBC,cACElB,aAAamB,qBAAqB;4DACpCC,oBAAoB,EAAE;4DACtBC,yBAAyB;wDAC3B;qDACD;oDACD3C,WAAW;gDACb,OAAO,IACL,CAACoC,2BACAF,CAAAA,UAAUU,OAAO,KAAK,WACrBV,UAAUU,OAAO,KAAK,cAAa,GACrC;oDACA3G,YAAY6F,GAAG,CAACzB,iBAAiB,EAAE;oDACnCL,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIwB,aAAamB,qBAAqB,EAAE;gDACtCtG,cAAc2F,GAAG,CACfzB,iBACAiB,aAAamB,qBAAqB;4CAEtC;4CAEArG,kBAAkB0F,GAAG,CAACzB,iBAAiB6B;wCACzC;oCACF,OAAO;wCACL,IAAI9gB,cAAc6f,cAAc;4CAC9B,IAAIK,aAAauB,cAAc,EAAE;gDAC/B7c,QAAQG,IAAI,CACV,CAAC,kFAAkF,EAAElB,MAAM;4CAE/F;4CACAqc,aAAatB,QAAQ,GAAG;4CACxBsB,aAAauB,cAAc,GAAG;wCAChC;wCAEA,IACEvB,aAAatB,QAAQ,KAAK,SACzBsB,CAAAA,aAAapB,WAAW,IAAIoB,aAAawB,SAAS,AAAD,GAClD;4CACAjI,iBAAiB;wCACnB;wCAEA,IAAIyG,aAAapB,WAAW,EAAE;4CAC5BA,cAAc;4CACdpE,eAAenG,GAAG,CAAC1Q;wCACrB;wCAEA,IAAIqc,aAAajE,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAIiE,aAAauB,cAAc,EAAE;4CAC/Bzb,SAASuO,GAAG,CAAC1Q;4CACb8a,QAAQ;4CAER,IACEuB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC9R,MAAM,GAAG,GACxC;gDACA6L,gBAAgB8F,GAAG,CACjB7c,MACAqc,aAAaW,iBAAiB;gDAEhC9B,gBAAgBmB,aAAaW,iBAAiB,CAACta,GAAG,CAChD,CAACC,QAAUA,MAAMC,QAAQ;4CAE7B;4CAEA,IACEyZ,aAAamB,qBAAqB,KAClC5e,aAAakf,sBAAsB,EACnC;gDACAnH,yBAAyBjG,GAAG,CAAC1Q;4CAC/B,OAAO,IACLqc,aAAamB,qBAAqB,KAClC5e,aAAamf,SAAS,EACtB;gDACArH,uBAAuBhG,GAAG,CAAC1Q;4CAC7B;wCACF,OAAO,IAAIqc,aAAa2B,cAAc,EAAE;4CACtClH,iBAAiBpG,GAAG,CAAC1Q;wCACvB,OAAO,IACLqc,aAAatB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAMrB,oCAAqC,OAC5C;4CACA9U,YAAY6L,GAAG,CAAC1Q;4CAChB+a,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChD7Y,SAASuO,GAAG,CAAC1Q;4CACb8a,QAAQ;wCACV;wCAEA,IAAI/J,eAAe/Q,SAAS,QAAQ;4CAClC,IACE,CAACqc,aAAatB,QAAQ,IACtB,CAACsB,aAAauB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAIrQ,MACR,CAAC,cAAc,EAAEnX,4CAA4C,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAMujB,mCACP,CAAC0C,aAAauB,cAAc,EAC5B;gDACA/Y,YAAYoZ,MAAM,CAACje;4CACrB;wCACF;wCAEA,IACExH,oBAAoBgN,QAAQ,CAACxF,SAC7B,CAACqc,aAAatB,QAAQ,IACtB,CAACsB,aAAauB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAIrQ,MACR,CAAC,OAAO,EAAEvN,KAAK,GAAG,EAAE5J,4CAA4C,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAOiX,KAAK;oCACZ,IACE,CAACnR,QAAQmR,QACTA,IAAI6Q,OAAO,KAAK,0BAEhB,MAAM7Q;oCACRuJ,aAAalG,GAAG,CAAC1Q;gCACnB;4BACF;4BAEA,IAAIwa,aAAa,OAAO;gCACtB,IAAIM,SAASC,UAAU;oCACrBzE;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAa,UAAUyF,GAAG,CAAC7c,MAAM;4BAClBob;4BACAT;4BACAC;4BACAG;4BACAD;4BACAD;4BACAI;4BACAC;4BACAiD,qBAAqBxU;4BACrBiS,SAASI;4BACToC,cAAczU;4BACd0U,kBAAkB1U;4BAClB2U,qBAAqB3U;wBACvB;oBACF;gBACF;gBAGJ,MAAM4U,kBAAkB,MAAMtF;gBAC9B,MAAMuF,qBACJ,AAAC,MAAM1F,qCACNyF,mBAAmBA,gBAAgBP,cAAc;gBAEpD,MAAMS,cAAc;oBAClBvG,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACAxC;oBACAyC,uBAAuBmG;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAItI,oBAAoBA,mBAAmBuI,cAAc;YACzDngB,iBAAiB,iCAAiC+F;YAElD,IAAI4T,0BAA0B;gBAC5BnX,QAAQG,IAAI,CACV3L,KAAKC,OAAO,CAAC,SAAS,CAAC,KACrBA,OACE,CAAC,qJAAqJ,CAAC;gBAG7JuL,QAAQG,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEsb,YAAY,EAAE,GAAGnZ;YAEzB,MAAMsb,gCAA0C,EAAE;YAClD,IAAI/Z,wBAAwB;gBAC1B+Z,8BAA8BlZ,IAAI,CAChCtP,KAAKwK,IAAI,CAACrI,kBAAkB,GAAG9B,8BAA8B,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAAC2S,eAAgBqN,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClEkI,8BAA8BlZ,IAAI,CAChCtP,KAAKwK,IAAI,CACPrI,kBACA,CAAC,KAAK,EAAE9B,8BAA8B,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAMooB,8BAA8Bta,cACjCU,UAAU,CAAC,kCACXmF,OAAO,CAAC;gBACP,MAAM0U,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAACnH,KAAKoH,MAAM,IAAIzc,OAAOC,OAAO,CACvCe,OAAO+C,YAAY,CAACqW,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI/E,OAAOoH,OAAO;wBAChBD,uBAAuB,CAACnH,IAAI,GAAGvhB,KAAKwP,QAAQ,CAAClF,SAASqe;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvD5a,SAAS;oBACTd,QAAQ;wBACN,GAAGA,MAAM;wBACT2b,YAAYrV;wBACZ,GAAI7P,cAAc+G,cAAc,GAC5B;4BACEoe,UAAU;wBACZ,IACA,CAAC,CAAC;wBACNzC,cAAcA,eACVrmB,KAAKwP,QAAQ,CAAClF,SAAS+b,gBACvBnZ,OAAOmZ,YAAY;wBACvBpW,cAAc;4BACZ,GAAG/C,OAAO+C,YAAY;4BACtBqW,eAAeoC;4BACfK,iBAAiBplB,cAAc+G,cAAc;4BAC7Cse,uBAAuB7V;wBACzB;oBACF;oBACAvE,QAAQkD;oBACRmX,gBAAgBjpB,KAAKwP,QAAQ,CAAClB,uBAAuBwD;oBACrD7C,OAAO;wBACL/M;wBACAlC,KAAKwP,QAAQ,CAAClF,SAASuT;wBACvBpc;wBACAO;wBACAhC,KAAKwK,IAAI,CAACrI,kBAAkBa;wBAC5BhD,KAAKwK,IAAI,CAACrI,kBAAkBG;wBAC5BtC,KAAKwK,IAAI,CAACrI,kBAAkBU,4BAA4B;2BACpD,CAACmQ,cACD;4BACEhT,KAAKwK,IAAI,CACPrI,kBACAW,qCAAqC;4BAEvCb;yBACD,GACD,EAAE;2BACF2M,SACA;+BACM1B,OAAO+C,YAAY,CAACuS,GAAG,GACvB;gCACExiB,KAAKwK,IAAI,CACPrI,kBACAS,iCAAiC;gCAEnC5C,KAAKwK,IAAI,CACPrI,kBACAS,iCAAiC;6BAEpC,GACD,EAAE;4BACN5C,KAAKwK,IAAI,CAACrI,kBAAkBI;4BAC5BvC,KAAKwK,IAAI,CAAChI;4BACVC;4BACAzC,KAAKwK,IAAI,CACPrI,kBACAY,4BAA4B;4BAE9B/C,KAAKwK,IAAI,CACPrI,kBACAY,4BAA4B;yBAE/B,GACD,EAAE;2BACFuS,YAAY,CAACtC,cACb;4BACE7P,uBAAuB;4BACvBnD,KAAKwK,IAAI,CAACrI,kBAAkBgB,uBAAuB;yBACpD,GACD,EAAE;wBACN3B;wBACAxB,KAAKwK,IAAI,CAACrI,kBAAkBQ,qBAAqB;wBACjD3C,KAAKwK,IAAI,CAACrI,kBAAkBQ,qBAAqB;wBACjDP;2BACGomB;qBACJ,CACEnc,MAAM,CAAChL,aACPkL,GAAG,CAAC,CAACyC,OAAShP,KAAKwK,IAAI,CAAC0C,OAAO5C,OAAO,EAAE0E;oBAC3Cka,QAAQ,EAAE;gBACZ;gBAEA,OAAON;YACT;YAEF,IAAI,CAACnJ,gBAAgB;gBACnBgJ,4BAA4BS,MAAM,CAAC5Z,IAAI,CACrCtP,KAAKwP,QAAQ,CACXsC,KACA9R,KAAKwK,IAAI,CACPxK,KAAK0P,OAAO,CACVgB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMwY,iBAAiB3Q,UAAUwM,IAAI,CAAC,CAACxX,IACrCA,EAAE6B,QAAQ,CAAClP;YAEb,IAAIqO,oBAAoB;YAExB,IAAI2a,gBAAgB;gBAClB,MAAM7D,aAAa,MAAM7gB,8BAA8B;oBACrD4gB,gBAAgB;oBAChBD,cAAcplB,KAAKwK,IAAI,CAACsH,KAAKqX;oBAC7Bjc;oBACA0B;oBACAkJ,gBAAgB5K,OAAO4K,cAAc;oBACrCkB,OAAO;oBACPnP,MAAM;gBACR;gBAEA,IAAIyb,WAAWC,mBAAmB,EAAE;oBAClCC;gBACF;gBAEA,IAAIF,WAAWG,OAAO,KAAK,UAAU;wBAIvBH;oBAHZ9W,oBAAoB;oBACpBqT,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClD2D,SAASH,WAAWG,OAAO;wBAC3B2D,UAAU9D,EAAAA,yBAAAA,WAAW+D,UAAU,qBAArB/D,uBAAuB8D,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIvW,aAAa;wBACf,MAAM1H,cACJtL,KAAKwK,IAAI,CACPF,SACA,UACAwB,SACA1I,uCAEFye,wBAAwBC,SAAS,CAAC,eAAe,CAACsH,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAMtc,6BAA6BxC,SAASuX;YAE5C,IAAI,CAACrP,kBAAkB,CAACuL,oBAAoB;gBAC1CA,qBAAqBpW,mBAAmB;oBACtCmK;oBACA5E;oBACA5C;oBACAiV,mBAAmB5Z,8BAA8Bsb;oBACjDvS,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACAsR;oBACA3B;oBACAxP;oBACA0E,aAAa;gBACf,GAAG0M,KAAK,CAAC,CAACxI;oBACRtM,QAAQ0M,KAAK,CAACJ;oBACdtD,QAAQQ,IAAI,CAAC;gBACf;YACF;YAEA,IAAIuM,iBAAiB6D,IAAI,GAAG,KAAKxY,SAASwY,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/D3I,eAAeO,UAAU,GAAG/Y,gBAAgB;uBACvCsd;uBACA3U;iBACJ,EAAEO,GAAG,CAAC,CAAC1C;oBACN,OAAOnC,eAAemC,MAAMiC;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAMqC,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAcsQ,oBAAoBC;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAM2N,oBACJ,CAACzH,4BAA6B,CAAA,CAACG,yBAAyBtH,WAAU;YAEpE,IAAI6F,aAAa+D,IAAI,GAAG,GAAG;gBACzB,MAAMtN,MAAM,qBAQX,CARW,IAAIE,MACd,CAAC,qCAAqC,EACpCqJ,aAAa+D,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAI/D;iBAAa,CACnElU,GAAG,CAAC,CAACkd,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxBjf,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACA0M,IAAIC,IAAI,GAAG;gBACX,MAAMD;YACR;YAEA,MAAMrR,aAAayE,SAASwB;YAE5B,IAAIoB,OAAO+C,YAAY,CAACyZ,WAAW,EAAE;gBACnC,MAAMC,WACJjZ,QAAQ;gBAEV,MAAMkZ,eAAe,MAAM,IAAIlL,QAAkB,CAAC/N,SAASkZ;oBACzDF,SACE,YACA;wBAAE1T,KAAKjW,KAAKwK,IAAI,CAACF,SAAS;oBAAU,GACpC,CAAC4M,KAAKjI;wBACJ,IAAIiI,KAAK;4BACP,OAAO2S,OAAO3S;wBAChB;wBACAvG,QAAQ1B;oBACV;gBAEJ;gBAEAwZ,4BAA4BxZ,KAAK,CAACK,IAAI,IACjCsa,aAAard,GAAG,CAAC,CAACtB,WACnBjL,KAAKwK,IAAI,CAAC0C,OAAO5C,OAAO,EAAE,UAAUW;YAG1C;YAEA,MAAM6e,WAAqC;gBACzC;oBACErS,aAAa;oBACbC,iBAAiBxK,OAAO+C,YAAY,CAACsL,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACE9D,aAAa;oBACbC,iBAAiBxK,OAAO+C,YAAY,CAACyZ,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACEjS,aAAa;oBACbC,iBAAiBxK,OAAO+C,YAAY,CAAC8Z,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACEtS,aAAa;oBACbC,iBAAiBxK,OAAO+C,YAAY,CAAC0L,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACElE,aAAa;oBACbC,iBAAiBzO,2BAA2BiE,UAAU,IAAI;gBAC5D;aACD;YACDkI,UAAUS,MAAM,CACdiU,SAASvd,GAAG,CAAC,CAACyd;gBACZ,OAAO;oBACLrS,WAAWxT;oBACXyT,SAASoS;gBACX;YACF;YAGF,MAAMjd,iCACJzC,SACAme;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAIjW,kBAAkB,CAACQ,aAAa;gBAClClO,IAAIuP,IAAI,CAAC;gBAET,MAAMlG,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAM5F,gBAAgB;wBACpBoB;wBACA4C;oBACF;gBACF;YACJ;YAEA,MAAMqB,qBAAyC,MAAM/C,aACnDxL,KAAKwK,IAAI,CAACF,SAASnI,kBAAkBG;YAGvC,MAAMuJ,oBAAuC;gBAC3CmC,SAAS;gBACT5B,QAAQ,CAAC;gBACTO,eAAe,CAAC;gBAChBsd,gBAAgB,EAAE;gBAClBC,SAASnR;YACX;YAEA,MAAMoR,qBAA+B,EAAE;YAEvC,MAAM,EAAE9N,IAAI,EAAE,GAAGnP;YAEjB,MAAMkd,wBAAwB/nB,oBAAoBgK,MAAM,CACtD,CAACxC,OACCkK,WAAW,CAAClK,KAAK,IACjBkK,WAAW,CAAClK,KAAK,CAAC8L,UAAU,CAAC;YAEjCyU,sBAAsBC,OAAO,CAAC,CAACxgB;gBAC7B,IAAI,CAACmC,SAASse,GAAG,CAACzgB,SAAS,CAACkY,0BAA0B;oBACpDrT,YAAY6L,GAAG,CAAC1Q;gBAClB;YACF;YAEA,MAAM0gB,cAAcH,sBAAsB/a,QAAQ,CAAC;YACnD,MAAMmb,sBACJ,CAACD,eAAe,CAACrI,yBAAyB,CAACH;YAE7C,MAAM0I,gBAAgB;mBAAI/b;mBAAgB1C;aAAS;YACnD,MAAM0e,iBAAiB7J,YAAYyJ,GAAG,CAACrnB;YACvC,MAAM0nB,kBAAkB9P,aAAa6P;YAErC,MAAM5iB,uBAAuB;gBAC3B8V,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAACzK,iBACAsX,CAAAA,cAAc1V,MAAM,GAAG,KACtByU,qBACAgB,uBACA5b,MAAK,GACP;gBACA,MAAMgc,uBACJzc,cAAcU,UAAU,CAAC;gBAC3B,MAAM+b,qBAAqB9b,YAAY,CAAC;oBACtC3J,uBACE;2BACKslB;2BACArc,SAASW,KAAK,CAAC1C,MAAM,CAAC,CAACxC,OAAS,CAAC4gB,cAAcpb,QAAQ,CAACxF;qBAC5D,EACDmC,UACA,IAAIwT,IACF/G,MAAMC,IAAI,CAACkI,gBAAgBzU,OAAO,IAAII,GAAG,CACvC,CAAC,CAAC1C,MAAMuC,OAAO;wBACb,OAAO;4BAACvC;4BAAMuC,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAMC,QAAQ;yBAAE;oBACtD;oBAKN,MAAMwF,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;oBAEV,MAAM2Y,eAAmC;wBACvC,GAAG3d,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7D4d,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7D/e,SAASqe,OAAO,CAAC,CAACxgB;gCAChB,IAAIvG,eAAeuG,OAAO;oCACxBsgB,mBAAmB7a,IAAI,CAACzF;oCAExB,IAAI0W,uBAAuB+J,GAAG,CAACzgB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAIwS,MAAM;4CACR0O,UAAU,CAAC,CAAC,CAAC,EAAE1O,KAAK4G,aAAa,GAAGpZ,MAAM,CAAC,GAAG;gDAC5CA;gDACAmhB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAAClhB,KAAK,GAAG;gDACjBA;gDACAmhB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAAClhB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACd+W,gBAAgByJ,OAAO,CAAC,CAACje,QAAQvC;gCAC/BuC,OAAOie,OAAO,CAAC,CAAC7d;oCACdue,UAAU,CAACve,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C;wCACAohB,UAAUze,MAAM0a,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIsC,mBAAmB;gCACrBuB,UAAU,CAAC,OAAO,GAAG;oCACnBlhB,MAAM+Q,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAI4P,qBAAqB;gCACvBO,UAAU,CAAC,OAAO,GAAG;oCACnBlhB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChDgX,YAAYwJ,OAAO,CAAC,CAACje,QAAQ6Y;gCAC3B,MAAM6B,YAAY9F,kBAAkBkK,GAAG,CAACjG;gCACxC,MAAMkG,iBAAiBrE,CAAAA,6BAAAA,UAAWU,OAAO,MAAK;gCAE9C,MAAM9C,oBAA6BoC,YAC/Bte,uBAAuB0E,OAAO+C,YAAY,CAAC0L,GAAG,EAAEmL,aAChD;gCAEJ1a,OAAOie,OAAO,CAAC,CAAC7d;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAM8a,kBAAkB,IACxB9a,MAAM8a,kBAAkB,CAACvS,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEAgW,UAAU,CAACve,MAAMC,QAAQ,CAAC,GAAG;wCAC3B5C,MAAMob;wCACNgG,UAAUze,MAAM0a,eAAe;wCAC/BkE,sBAAsB5e,MAAM2a,mBAAmB;wCAC/CkE,iBAAiBF;wCACjBG,WAAW;wCACXC,oBAAoB7G;wCACpB8G,wBAAwB,CAAChf,MAAM+a,uBAAuB;oCACxD;gCACF;4BACF;4BAEA,IAAIlL,MAAM;gCACR,KAAK,MAAMxS,QAAQ;uCACd6E;uCACA1C;uCACCwd,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCgB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMiB,QAAQzf,SAASse,GAAG,CAACzgB;oCAC3B,MAAM+c,YAAYtjB,eAAeuG;oCACjC,MAAM6hB,aAAaD,SAASlL,uBAAuB+J,GAAG,CAACzgB;oCAEvD,KAAK,MAAM8hB,UAAUtP,KAAKtQ,OAAO,CAAE;4CAMzBgf;wCALR,+DAA+D;wCAC/D,IAAIU,SAAS7E,aAAa,CAAC8E,YAAY;wCACvC,MAAMnc,aAAa,CAAC,CAAC,EAAEoc,SAAS9hB,SAAS,MAAM,KAAKA,MAAM;wCAE1DkhB,UAAU,CAACxb,WAAW,GAAG;4CACvB1F,MAAMkhB,EAAAA,mBAAAA,UAAU,CAAClhB,KAAK,qBAAhBkhB,iBAAkBlhB,IAAI,KAAIA;4CAChC+hB,SAASD;4CACTX,gBAAgBU;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOV,UAAU,CAAClhB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAOkhB;wBACT;oBACF;oBAEA,MAAMzY,SAAStS,KAAKwK,IAAI,CAACF,SAAS;oBAClC,MAAMuhB,eAAe,MAAM5Z,UACzBH,KACA;wBACEM,YAAYyY;wBACZ9Y;wBACAM,QAAQ;wBACRF,aAAa;wBACbQ;wBACAC;wBACA7D,OAAO0b;wBACPnY;wBACAwZ,eAAe;wBACf5a,YAAYlB,mBAAmB6a;oBACjC,GACA1c;oBAGF,sDAAsD;oBACtD,IAAI,CAAC0d,cAAc;oBAEnB,MAAME,kBAAkB,CAACvf;4BACKqf;wBAA5B,MAAM1D,uBAAsB0D,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CACjD1e,MAAMC,QAAQ,sBADYof,yBAEzB1D,mBAAmB;wBAEtB,kEAAkE;wBAClE,+DAA+D;wBAC/D,sBAAsB;wBACtB,IACEA,uBACA,CAAC3b,MAAM+a,uBAAuB,IAC9B/a,MAAM4a,YAAY,KAAK3e,aAAamf,SAAS,EAC7C;4BACA,OAAOnf,aAAakf,sBAAsB;wBAC5C;wBAEA,4DAA4D;wBAC5D,6BAA6B;wBAC7B,IAAI,CAACnb,MAAM4a,YAAY,EAAE;4BACvB,OAAO3e,aAAawjB,SAAS;wBAC/B;wBAEA,OAAOzf,MAAM4a,YAAY;oBAC3B;oBAEA,MAAM8E,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCP;wBADF,MAAMQ,gBACJR,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CAACiB,gCAAxBN,yBAAqCQ,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEtF,YAAYqF;gCAAmBE,QAAQ9Y;4BAAU;wBAC5D;wBAEA,IACE6Y,aAAatF,UAAU,KAAK,SAC5BsF,aAAatF,UAAU,GAAG,KAC1BsF,aAAaC,MAAM,KAAK9Y,WACxB;4BACA,OAAO;gCACLuT,YAAYsF,aAAatF,UAAU;gCACnCuF,QAAQpf,OAAOqf,UAAU;4BAC3B;wBACF;wBAEA,OAAOF;oBACT;oBAEA,IAAI1Z,eAAeiB,QAAQC,GAAG,CAAC2Y,sBAAsB,KAAK,KAAK;wBAC7DzkB,mBAAmB8jB;oBACrB;oBAEA/nB,gCAAgC;wBAC9BwG,SAAS4C,OAAO5C,OAAO;wBACvBmiB,QAAQ;4BACNxY;+BACG4X,aAAaa,2BAA2B,CAACC,MAAM;yBACnD;oBACH;oBAEA9gB,kBAAkBoe,cAAc,GAAGxR,MAAMC,IAAI,CAC3CmT,aAAae,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAM/iB,QAAQ6E,YAAa;wBAC9B,MAAMme,eAAenpB,YAAYmG,MAAMS,SAASkJ,WAAW;wBAC3D,MAAM/T,GAAGqtB,MAAM,CAACD;oBAClB;oBAEAhM,YAAYwJ,OAAO,CAAC,CAACxD,mBAAmB5B;4BAWbhE;wBAVzB,MAAMpX,OAAOiX,mBAAmBoK,GAAG,CAACjG;wBACpC,IAAI,CAACpb,MAAM,MAAM,qBAAoC,CAApC,IAAIjB,eAAe,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMke,YAAY9F,kBAAkBkK,GAAG,CAACjG;wBACxC,IAAI,CAAC6B,WAAW,MAAM,qBAA0C,CAA1C,IAAIle,eAAe,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAImkB,oBACFjG,UAAUC,UAAU,KAAK,KACzBmF,gBAAgBriB,MAAMkd,UAAU,KAAK;wBAEvC,IAAIgG,uBAAqB9L,iBAAAA,UAAUiK,GAAG,CAACrhB,0BAAdoX,eAAqB2D,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrF3D,UAAUyF,GAAG,CAAC7c,MAAM;gCAClB,GAAIoX,UAAUiK,GAAG,CAACrhB,KAAK;gCACvB+a,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAMqI,oBAAoB3lB,gBAAgB4d;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMP,oBACJ,CAACsI,qBACDxkB,uBAAuB0E,OAAO+C,YAAY,CAAC0L,GAAG,EAAEmL,aAC5C,OACAtT;wBAEN,MAAMyZ,sBACJ,uEAAuE;wBACvE/f,OAAOggB,eAAe,IAAIrkB;wBAE5B,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMskB,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAU7L,KAAK9a;4BAAc;4BACrC;gCACE2mB,MAAM;gCACN7L,KAAK;gCACLoH,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7FjE,oBACA;gCACE;oCACE0I,MAAM;oCACN7L,KAAK;oCACLoH,OAAOsE;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAM7gB,SAA6B,EAAE;wBACrC,MAAMO,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAI0gB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoB1G,kBAAmB;4BAChD,IACE0G,iBAAiBpG,mBAAmB,IACpCoG,iBAAiBpG,mBAAmB,CAACpS,MAAM,GAAG,GAC9C;gCACAsY,uBAAuB/d,IAAI,CAACie;4BAC9B,OAAO;gCACLD,qBAAqBhe,IAAI,CAACie;4BAC5B;wBACF;wBAEAF,yBAAyB9pB,sBACvB8pB,wBACA,CAACE,mBAAqBA,iBAAiB9gB,QAAQ;wBAEjD6gB,uBAAuB/pB,sBACrB+pB,sBACA,CAACC,mBAAqBA,iBAAiB9gB,QAAQ;wBAGjDoa,oBAAoB;+BACfyG;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoB1G,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAI0G,iBAAiB9gB,QAAQ,KAAKvJ,4BAA4B;gCAC5D;4BACF;4BAEA,IACEwhB,qBACA6I,iBAAiBpG,mBAAmB,IACpCoG,iBAAiBpG,mBAAmB,CAACpS,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BpI,cAAc2C,IAAI,CAACie;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChCnhB,OAAOkD,IAAI,CAACie;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAM/gB,SAASJ,OAAQ;4BAC1B,IAAI9I,eAAeuG,SAAS2C,MAAMC,QAAQ,KAAK5C,MAAM;4BACrD,IAAI2C,MAAMC,QAAQ,KAAKvJ,4BAA4B;4BAEnD,MAAM,EACJ4c,WAAW,CAAC,CAAC,EACbqI,mBAAmB,EACnBqF,YAAY,EACb,GAAG3B,aAAaG,MAAM,CAACd,GAAG,CAAC1e,MAAMC,QAAQ,KAAK,CAAC;4BAEhD,MAAM4f,eAAeH,gBACnB1f,MAAMC,QAAQ,EACdqa,UAAUC,UAAU;4BAGtB9F,UAAUyF,GAAG,CAACla,MAAMC,QAAQ,EAAE;gCAC5B,GAAIwU,UAAUiK,GAAG,CAAC1e,MAAMC,QAAQ,CAAC;gCACjC+gB;gCACArF;gCACAH,qBAAqBqE;4BACvB;4BAEA,uEAAuE;4BACvEpL,UAAUyF,GAAG,CAAC7c,MAAM;gCAClB,GAAIoX,UAAUiK,GAAG,CAACrhB,KAAK;gCACvB2jB;gCACArF;gCACAH,qBAAqBqE;4BACvB;4BAEA,IAAIA,aAAatF,UAAU,KAAK,GAAG;gCACjC,MAAM0G,kBAAkBhqB,kBAAkB+I,MAAMC,QAAQ;gCAExD,IAAIihB;gCACJ,IAAIV,mBAAmB;oCACrBU,YAAY;gCACd,OAAO;oCACLA,YAAY1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CAAC,GAAGijB,kBAAkBltB,YAAY;gCAC/D;gCAEA,IAAIqtB;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACZ,qBAAqBtR,iBAAiB;oCACzCkS,oBAAoB5tB,KAAK2tB,KAAK,CAACnjB,IAAI,CACjC,GAAGijB,kBAAkBntB,qBAAqB;gCAE9C;gCAEA,MAAMutB,OAAOjoB,YAAYka;gCAEzBjU,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;oCACzCqhB,eAAeD,KAAKE,MAAM;oCAC1BC,gBAAgBH,KAAKtZ,OAAO;oCAC5B0Z,eAAevS,kBACXgJ,oBACE/b,cAAculB,gBAAgB,GAC9BvlB,cAAcwlB,MAAM,GACtB3a;oCACJ4a,iBAAiB1J;oCACjB2J,uBAAuBlB;oCACvBmB,0BAA0BjC,aAAatF,UAAU;oCACjDwH,sBAAsBlC,aAAaC,MAAM;oCACzChgB,UAAUzC;oCACV6jB;oCACAE;oCACAY,aAAa9kB;gCACf;4BACF,OAAO;gCACLqjB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpB9L,UAAUyF,GAAG,CAACla,MAAMC,QAAQ,EAAE;oCAC5B,GAAIwU,UAAUiK,GAAG,CAAC1e,MAAMC,QAAQ,CAAC;oCACjCkY,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAACmI,qBAAqBzpB,eAAeuG,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAAC6a,mBAAmB;gCACtB/X,cAAc2C,IAAI,CAAC;oCACjB2X,QAAQ,CAAC;oCACTxa,UAAU5C;oCACVqd,iBAAiBrd;oCACjBsd,qBAAqB,EAAE;oCACvBC,cACErG,cAAcmK,GAAG,CAACjG,oBAClBxc,aAAawjB,SAAS;oCACxB3E,oBAAoB,EAAE;oCACtBC,yBAAyB;gCAC3B;4BACF;4BAEA,KAAK,MAAM/a,SAASG,cAAe;oCAGhBkf,0BAuFMrf;gCAzFvB,MAAMihB,kBAAkBhqB,kBAAkB+I,MAAMC,QAAQ;gCAExD,MAAMqT,YAAW+L,2BAAAA,aAAaG,MAAM,CAACd,GAAG,CACtC1e,MAAMC,QAAQ,sBADCof,yBAEd/L,QAAQ;gCAEX,MAAMuM,eAAeH,gBAAgB1f,MAAMC,QAAQ;gCAEnD,IAAIihB,YAA2B;gCAC/B,IAAI,CAACV,mBAAmB;oCACtBU,YAAY1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CAAC,GAAGijB,kBAAkBltB,YAAY;gCAC/D;gCAEA,IAAIqtB;gCACJ,IAAI,CAACZ,qBAAqBtR,iBAAiB;oCACzCkS,oBAAoB5tB,KAAK2tB,KAAK,CAACnjB,IAAI,CACjC,GAAGijB,kBAAkBntB,qBAAqB;gCAE9C;gCAEA,IAAI,CAAC0sB,sBAAqBlN,4BAAAA,SAAU2O,YAAY,GAAE;oCAChD,MAAMC,eAAe7S,eAAelP,aAAa,CAACqY,IAAI,CACpD,CAAC7I,IAAMA,EAAEtS,IAAI,KAAKA;oCAEpB,IAAI,CAAC6kB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAItX,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEAsX,aAAaC,yBAAyB,KAAK,EAAE;oCAC7C,KAAK,MAAMC,eAAe9O,SAAS2O,YAAY,CAAE;wCAC/CC,aAAaC,yBAAyB,CAACrf,IAAI,CACzCvG,8BAA8ByD,MAAMC,QAAQ,EAAEmiB;oCAElD;gCACF;gCAEA3N,UAAUyF,GAAG,CAACla,MAAMC,QAAQ,EAAE;oCAC5B,GAAIwU,UAAUiK,GAAG,CAAC1e,MAAMC,QAAQ,CAAC;oCACjCoiB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3CrB,cAAc9I;gCAChB;gCAEA,MAAM0C,eAAe2E,gBAAgBvf;gCAErC,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAMsiB,uBACJpK,qBAAqB0C,iBAAiB3e,aAAamf,SAAS,GACxDyE,eACA7Y;gCAEN,MAAMqB,WAAqBnM,4BACzB0e,cACA5a,MAAMC,QAAQ;gCAGhB,MAAMohB,OACJ/N,YACA4E,qBACA0C,iBAAiB3e,aAAamf,SAAS,GACnChiB,YAAYka,YACZ,CAAC;gCAEPjU,kBAAkBc,aAAa,CAACH,MAAMC,QAAQ,CAAC,GAAG;oCAChD2hB,iBAAiB1J;oCACjBuJ,eAAevS,kBACXgJ,oBACE/b,cAAculB,gBAAgB,GAC9BvlB,cAAcwlB,MAAM,GACtB3a;oCACJ6a,uBAAuBlB;oCACvBrjB,YAAY1I,oBACViF,mBAAmBmG,MAAMC,QAAQ,EAAE;wCACjC1C,iBAAiB;oCACnB,GAAGE,EAAE,CAACC,MAAM;oCAEdwjB;oCACA7Y;oCACAka,kBAAkB,EAAED,wCAAAA,qBAAsB/H,UAAU;oCACpDiI,cAAc,EAAEF,wCAAAA,qBAAsBxC,MAAM;oCAC5C2C,gBAAgBpB,KAAKE,MAAM;oCAC3BmB,iBAAiBrB,KAAKtZ,OAAO;oCAC7B+S,oBAAoBzS,WAChBrI,MAAM8a,kBAAkB,GACxB9T;oCACJ2b,qBAAqB3iB,EAAAA,6BAAAA,MAAM2a,mBAAmB,qBAAzB3a,2BAA2BuI,MAAM,IAClDlL,OACA2J;oCACJ4b,gBAAgB,CAAC1B,YACb,OACAtsB,oBACEiF,mBAAmBqnB,WAAW;wCAC5B3jB,iBAAiB;wCACjBslB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGrlB,EAAE,CAACC,MAAM;oCAElB0jB;oCACA2B,wBAAwB,CAAC3B,oBACrBpa,YACApS,oBACEiF,mBAAmBunB,mBAAmB;wCACpC7jB,iBAAiB;wCACjBslB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGrlB,EAAE,CAACC,MAAM;oCAElBskB,aAAa9kB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAM8lB,mBAAmB,OACvBC,YACA5lB,MACAmF,MACAyc,OACAiE,KACAC,oBAAoB,KAAK;wBAEzB,OAAO/E,qBACJ/b,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZE,OAAO,GAAGA,KAAK,CAAC,EAAE0gB,KAAK;4BACvB,MAAME,OAAO5vB,KAAKwK,IAAI,CAAC8H,QAAQtD;4BAC/B,MAAMqL,WAAW3W,YACf+rB,YACAnlB,SACAkJ,WACA;4BAGF,MAAMqc,eAAe7vB,KAClBwP,QAAQ,CACPxP,KAAKwK,IAAI,CAACF,SAASnI,mBACnBnC,KAAKwK,IAAI,CACPxK,KAAKwK,IAAI,CACP6P,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5BoV,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACNxjB,GAAG,CAAC,IAAM,MACV/B,IAAI,CAAC,OAEVwE,OAGHtB,OAAO,CAAC,OAAO;4BAElB,IACE,CAAC+d,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhDppB,CAAAA,oBAAoBgN,QAAQ,CAACxF,SAC7B,CAACugB,sBAAsB/a,QAAQ,CAACxF,KAAI,GAGxC;gCACAqX,aAAa,CAACrX,KAAK,GAAGgmB;4BACxB;4BAEA,MAAMG,OAAOhwB,KAAKwK,IAAI,CAACF,SAASnI,kBAAkB0tB;4BAClD,MAAMI,aACJpkB,kBAAkBoe,cAAc,CAAC5a,QAAQ,CAACxF;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACwS,QAAQsT,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMxwB,GAAGgQ,KAAK,CAACzP,KAAK0P,OAAO,CAACsgB,OAAO;oCAAErgB,WAAW;gCAAK;gCACrD,MAAMlQ,GAAGywB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAI3T,QAAQ,CAACoP,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOvK,aAAa,CAACrX,KAAK;4BAC5B;4BAEA,IAAIwS,MAAM;gCACR,IAAIsT,mBAAmB;gCAEvB,MAAMQ,YAAYtmB,SAAS,MAAM7J,KAAKowB,OAAO,CAACphB,QAAQ;gCACtD,MAAMqhB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAS/a,MAAM;gCAGjB,KAAK,MAAM4W,UAAUtP,KAAKtQ,OAAO,CAAE;oCACjC,MAAMukB,UAAU,CAAC,CAAC,EAAE3E,SAAS9hB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACE4hB,SACA5f,kBAAkBoe,cAAc,CAAC5a,QAAQ,CAACihB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsBvwB,KACzBwK,IAAI,CACH,SACAmhB,SAASwE,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BtmB,SAAS,MAAM,KAAKwmB,qBAErB3iB,OAAO,CAAC,OAAO;oCAElB,MAAM8iB,cAAcxwB,KAAKwK,IAAI,CAC3B8H,QACAqZ,SAASwE,WACTtmB,SAAS,MAAM,KAAKmF;oCAEtB,MAAMyhB,cAAczwB,KAAKwK,IAAI,CAC3BF,SACAnI,kBACAouB;oCAGF,IAAI,CAAC9E,OAAO;wCACVvK,aAAa,CAACoP,QAAQ,GAAGC;oCAC3B;oCACA,MAAM9wB,GAAGgQ,KAAK,CAACzP,KAAK0P,OAAO,CAAC+gB,cAAc;wCACxC9gB,WAAW;oCACb;oCACA,MAAMlQ,GAAGywB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAO9F,qBACJ/b,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM8gB,OAAO5vB,KAAKwK,IAAI,CACpBF,SACA,UACA,OACA;4BAEF,MAAMimB,sBAAsBvwB,KACzBwK,IAAI,CAAC,SAAS,YACdkD,OAAO,CAAC,OAAO;4BAElB,IAAInO,WAAWqwB,OAAO;gCACpB,MAAMnwB,GAAGmQ,QAAQ,CACfggB,MACA5vB,KAAKwK,IAAI,CAACF,SAAS,UAAUimB;gCAG/B,mEAAmE;gCACnE,yEAAyE;gCACzE,IAAIlU,MAAM;oCACR,KAAK,MAAMsP,UAAUtP,KAAKtQ,OAAO,CAAE;wCACjC,MAAMukB,UAAU,CAAC,CAAC,EAAE3E,OAAO,IAAI,CAAC;wCAChCzK,aAAa,CAACoP,QAAQ,GAAGC;oCAC3B;gCACF;gCAEArP,aAAa,CAAC,OAAO,GAAGqP;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAI5F,iBAAiB;wBACnB,MAAM+F;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAAC9V,eAAe,CAACC,aAAa2O,mBAAmB;4BACnD,MAAMgG,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAIhF,qBAAqB;wBACvB,MAAMgF,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAM3lB,QAAQ4gB,cAAe;wBAChC,MAAMgB,QAAQzf,SAASse,GAAG,CAACzgB;wBAC3B,MAAM8mB,sBAAsBpQ,uBAAuB+J,GAAG,CAACzgB;wBACvD,MAAM+c,YAAYtjB,eAAeuG;wBACjC,MAAM+mB,SAASlQ,eAAe4J,GAAG,CAACzgB;wBAClC,MAAMmF,OAAOvL,kBAAkBoG;wBAE/B,MAAMgnB,WAAW5P,UAAUiK,GAAG,CAACrhB;wBAC/B,MAAMinB,eAAejF,aAAakF,MAAM,CAAC7F,GAAG,CAACrhB;wBAC7C,IAAIgnB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAAS9L,aAAa,EAAE;gCAC1B8L,SAAS3I,gBAAgB,GAAG2I,SAAS9L,aAAa,CAACxY,GAAG,CACpD,CAAC8N;oCACC,MAAMsE,WAAWmS,aAAaE,eAAe,CAAC9F,GAAG,CAAC7Q;oCAClD,IAAI,OAAOsE,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAIvH,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAOuH;gCACT;4BAEJ;4BACAkS,SAAS5I,YAAY,GAAG6I,aAAaE,eAAe,CAAC9F,GAAG,CAACrhB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMonB,gBAAgB,CAAExF,CAAAA,SAAS7E,aAAa,CAAC+J,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiB3lB,MAAMA,MAAMmF,MAAMyc,OAAO;wBAClD;wBAEA,IAAImF,UAAW,CAAA,CAACnF,SAAUA,SAAS,CAAC7E,SAAS,GAAI;4BAC/C,MAAMsK,UAAU,GAAGliB,KAAK,IAAI,CAAC;4BAC7B,MAAMwgB,iBAAiB3lB,MAAMqnB,SAASA,SAASzF,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAM+D,iBAAiB3lB,MAAMqnB,SAASA,SAASzF,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAC7E,WAAW;gCACd,MAAM4I,iBAAiB3lB,MAAMA,MAAMmF,MAAMyc,OAAO;gCAEhD,IAAIpP,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMsP,UAAUtP,KAAKtQ,OAAO,CAAE;wCACjC,MAAMolB,aAAa,CAAC,CAAC,EAAExF,SAAS9hB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAMwiB,eAAeH,gBAAgBiF;wCAErCtlB,kBAAkBO,MAAM,CAAC+kB,WAAW,GAAG;4CACrC7C,0BAA0BjC,aAAatF,UAAU;4CACjDwH,sBAAsBlC,aAAaC,MAAM;4CACzC8B,iBAAiB5a;4CACjBya,eAAeza;4CACflH,UAAU;4CACVohB,WAAW1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;4CAEhB4e,mBAAmBpa;4CACnBgb,aAAa9kB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAM2iB,eAAeH,gBAAgBriB;oCAErCgC,kBAAkBO,MAAM,CAACvC,KAAK,GAAG;wCAC/BykB,0BAA0BjC,aAAatF,UAAU;wCACjDwH,sBAAsBlC,aAAaC,MAAM;wCACzC8B,iBAAiB5a;wCACjBya,eAAeza;wCACflH,UAAU;wCACVohB,WAAW1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CACxB,eACAsB,SACA,GAAGkD,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7C4e,mBAAmBpa;wCACnBgb,aAAa9kB;oCACf;gCACF;gCACA,IAAImnB,UAAU;oCACZA,SAAS7I,mBAAmB,GAAGkE,gBAAgBriB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAM2C,SAASoU,gBAAgBsK,GAAG,CAACrhB,SAAS,EAAE,CAAE;oCACnD,MAAMunB,WAAW3tB,kBAAkB+I,MAAMC,QAAQ;oCACjD,MAAM+iB,iBACJ3lB,MACA2C,MAAMC,QAAQ,EACd2kB,UACA3F,OACA,QACA;oCAEF,MAAM+D,iBACJ3lB,MACA2C,MAAMC,QAAQ,EACd2kB,UACA3F,OACA,QACA;oCAGF,IAAImF,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJ3lB,MACAqnB,SACAA,SACAzF,OACA,QACA;wCAEF,MAAM+D,iBACJ3lB,MACAqnB,SACAA,SACAzF,OACA,QACA;oCAEJ;oCAEA,MAAMY,eAAeH,gBAAgB1f,MAAMC,QAAQ;oCAEnDZ,kBAAkBO,MAAM,CAACI,MAAMC,QAAQ,CAAC,GAAG;wCACzC6hB,0BAA0BjC,aAAatF,UAAU;wCACjDwH,sBAAsBlC,aAAaC,MAAM;wCACzC8B,iBAAiB5a;wCACjBya,eAAeza;wCACflH,UAAUzC;wCACV6jB,WAAW1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CACxB,eACAsB,SACA,GAAGrI,kBAAkB+I,MAAMC,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7CmhB,mBAAmBpa;wCACnBgb,aAAa9kB;oCACf;oCAEA,IAAImnB,UAAU;wCACZA,SAAS7I,mBAAmB,GAAGqE;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAM5sB,GAAG4xB,EAAE,CAAC/e,QAAQ;wBAAE3C,WAAW;wBAAM2hB,OAAO;oBAAK;oBACnD,MAAMhmB,cAAcuS,mBAAmBqD;oBAEvC,IAAIhU,OAAO+C,YAAY,CAACshB,kBAAkB,EAAE;wBAC1C,KAAK,MAAM/kB,SAAS;+BACfqP,eAAeE,YAAY;+BAC3BF,eAAelP,aAAa;yBAChC,CAAE;4BACD,2DAA2D;4BAC3D,yDAAyD;4BACzD,qDAAqD;4BACrD,8DAA8D;4BAC9D,gDAAgD;4BAEhD,kEAAkE;4BAClE,kEAAkE;4BAClE,uDAAuD;4BACvD,IAAIH,MAAMmiB,yBAAyB,EAAE;gCACnC;4BACF;4BAEAniB,MAAMmiB,yBAAyB,GAAG;gCAChC7lB,qCACE0D,MAAM3C,IAAI,EACV,2DAA2D;gCAC3D,2DAA2D;gCAC3D,4DAA4D;gCAC5D;6BAEH;wBACH;oBACF;gBACF;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAMsE,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMxD,cAAcsQ,oBAAoBC;YAC1D;YAEA,MAAM2V,mBAAmBzsB,cAAc;YACvC,IAAI0sB,qBAAqB1sB,cAAc,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC0c,OAAOiQ,GAAG;YAEV,MAAMC,cAAc/d,QAAQ+N,MAAM,CAACD;YACnCtM,UAAUS,MAAM,CACd9R,mBAAmBkU,YAAY;gBAC7BiH,mBAAmByS,WAAW,CAAC,EAAE;gBACjCC,iBAAiBljB,YAAY8V,IAAI;gBACjCqN,sBAAsB7lB,SAASwY,IAAI;gBACnCsN,sBAAsBnR,iBAAiB6D,IAAI;gBAC3CuN,cACE9Z,WAAWlD,MAAM,GAChBrG,CAAAA,YAAY8V,IAAI,GAAGxY,SAASwY,IAAI,GAAG7D,iBAAiB6D,IAAI,AAAD;gBAC1DwN,cAAcxI;gBACdyI,oBACEjQ,CAAAA,gCAAAA,aAAc3S,QAAQ,CAAC,uBAAsB;gBAC/C6iB,eAAexd,iBAAiBK,MAAM;gBACtCod,cAAc5d,QAAQQ,MAAM;gBAC5Bqd,gBAAgB3d,UAAUM,MAAM,GAAG;gBACnCsd,qBAAqB9d,QAAQlI,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAEmO,GAAG,EAAEvV,MAAM;gBAC/Dud,sBAAsB5d,iBAAiBrI,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAEmO,GAAG,EAC9DvV,MAAM;gBACTwd,uBAAuB9d,UAAUpI,MAAM,CAAC,CAAC8P,IAAW,CAAC,CAACA,EAAEmO,GAAG,EAAEvV,MAAM;gBACnEyd,iBAAiB1Z,oBAAoB,IAAI;gBACzC2B;gBACA0F;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAInZ,iBAAiBsrB,cAAc,EAAE;gBACnC,MAAMpc,SAASpS,uBACbkD,iBAAiBsrB,cAAc,CAACC,MAAM;gBAExCtd,UAAUS,MAAM,CAACQ;gBACjBjB,UAAUS,MAAM,CACdzR,qCACE+C,iBAAiBsrB,cAAc,CAACE,6BAA6B;gBAGjE,MAAMC,kBAAkBzrB,iBAAiBsrB,cAAc,CAACG,eAAe;gBAEvE,KAAK,MAAM,CAACrR,KAAKoH,MAAM,IAAIzc,OAAOC,OAAO,CAACymB,iBAAkB;oBAC1Dxd,UAAUS,MAAM,CACd5R,uBAAuB;wBACrB;4BACEwT,aAAa8J;4BACb7J,iBAAiBiR;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAI3c,SAASwY,IAAI,GAAG,KAAK5V,QAAQ;oBAmDpB1B;gBAlDXid,mBAAmBE,OAAO,CAAC,CAACwI;oBAC1B,MAAMpF,kBAAkBhqB,kBAAkBovB;oBAC1C,MAAMnF,YAAY1tB,KAAK2tB,KAAK,CAACnjB,IAAI,CAC/B,eACAsB,SACA,GAAG2hB,gBAAgB,KAAK,CAAC;oBAG3B5hB,kBAAkBc,aAAa,CAACkmB,SAAS,GAAG;wBAC1C/oB,YAAY1I,oBACViF,mBAAmBwsB,UAAU;4BAC3B9oB,iBAAiB;wBACnB,GAAGE,EAAE,CAACC,MAAM;wBAEdkkB,iBAAiB5a;wBACjBya,eAAeza;wBACfka;wBACA7Y,UAAU2L,yBAAyB8J,GAAG,CAACuI,YACnC,OACAtS,uBAAuB+J,GAAG,CAACuI,YACzB,GAAGpF,gBAAgB,KAAK,CAAC,GACzB;wBACNsB,oBAAoBvb;wBACpBwb,gBAAgBxb;wBAChB2b,qBAAqB3b;wBACrB8T,oBAAoB9T;wBACpB4b,gBAAgBhuB,oBACdiF,mBAAmBqnB,WAAW;4BAC5B3jB,iBAAiB;4BACjBslB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGrlB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7C0jB,mBAAmBpa;wBACnB+b,wBAAwB/b;wBACxBgb,aAAa9kB;oBACf;gBACF;gBAEAvC,iBAAiB2rB,aAAa,GAAG/Z,aAAa+Z,aAAa;gBAC3D3rB,iBAAiB4rB,mBAAmB,GAClC7lB,OAAO+C,YAAY,CAAC8iB,mBAAmB;gBACzC5rB,iBAAiB6rB,2BAA2B,GAC1C9lB,OAAO+C,YAAY,CAAC+iB,2BAA2B;gBAEjD,MAAMrnB,uBAAuBrB,SAASuB;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9CvB;oBACAwB;oBACAC,OAAO,GAAEmB,eAAAA,OAAOmP,IAAI,qBAAXnP,aAAanB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAMJ,uBAAuBrB,SAAS;oBACpC0D,SAAS;oBACT5B,QAAQ,CAAC;oBACTO,eAAe,CAAC;oBAChBud,SAASnR;oBACTkR,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMhd,oBAAoB3C,SAAS4C;YACnC,MAAM5B,cAActL,KAAKwK,IAAI,CAACF,SAAS1I,gBAAgB;gBACrDoM,SAAS;gBACTilB,kBAAkB,OAAO/lB,OAAO4d,aAAa,KAAK;gBAClDoI,qBAAqBhmB,OAAOimB,aAAa,KAAK;gBAC9ClR,qBAAqBA,wBAAwB;YAC/C;YACA,MAAMxiB,GAAGqtB,MAAM,CAAC9sB,KAAKwK,IAAI,CAACF,SAAS3I,gBAAgB+d,KAAK,CAAC,CAACxI;gBACxD,IAAIA,IAAIC,IAAI,KAAK,UAAU;oBACzB,OAAOuH,QAAQ/N,OAAO;gBACxB;gBACA,OAAO+N,QAAQmL,MAAM,CAAC3S;YACxB;YAEA,IAAIN,QAAQ1J,OAAO+C,YAAY,CAAC8Z,iBAAiB,GAAG;gBAClD,MAAM5b,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMvN,qBACJuQ,KACA9R,KAAKwK,IAAI,CAACF,SAAS5I;gBAEvB;YACJ;YAEA,MAAMqc;YAEN,IAAI0T,oBAAoB;gBACtBA,mBAAmBlJ,cAAc;gBACjCkJ,qBAAqBje;YACvB;YAEA,IAAIL,eAAe;gBACjBrO,IAAIuP,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAInH,OAAOiW,MAAM,KAAK,UAAU;gBAC9B,MAAMtR,uBACJ3E,QACA4E,KACAC,oBACAC,cACA7D;YAEJ;YAEA,IAAIjB,OAAO+C,YAAY,CAACmjB,WAAW,EAAE;gBACnC,MAAM3pB,oBAAoB;oBACxBqI;oBACAxH;oBACA+oB,aAAa/kB;oBACbE;oBACAC;oBACA2kB,aAAalmB,OAAO+C,YAAY,CAACmjB,WAAW;oBAC5ChlB,UAAUA,SAASW,KAAK;oBACxBmL,aAAa7L;oBACbwN;oBACAhQ;oBACA0C;oBACAsT;oBACA7U,qBAAqByb,4BAA4BxZ,KAAK;gBACxD;YACF;YAEA,IAAI/B,OAAOiW,MAAM,KAAK,cAAc;gBAClC,MAAMjV,yBACJC,eACA7D,SACA8D,UACAC,sBACAC,uBACAma,6BACAla,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAI4iB,kBAAkBA,iBAAiBjJ,cAAc;YACrD3d,QAAQC,GAAG;YAEX,IAAI8H,aAAa;gBACfxE,cACGU,UAAU,CAAC,uBACXmF,OAAO,CAAC,IAAM1O,kBAAkB;wBAAEmP;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMpG,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7DvJ,cAAc6I,UAAU6S,WAAW;oBACjCqS,UAAUhpB;oBACVwB,SAASA;oBACTwJ;oBACAkU;oBACA1R,gBAAgB5K,OAAO4K,cAAc;oBACrCsJ;oBACAD;oBACA5S;oBACAqV,UAAU1W,OAAO+C,YAAY,CAAC2T,QAAQ;gBACxC;YAGF,MAAMzV,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMsG,UAAUmC,KAAK;YAErC,MAAMkH;QACR;IACF,EAAE,OAAO8U,GAAG;QACV,MAAMne,YAAmC/L,aAAa6hB,GAAG,CAAC;QAC1D,IAAI9V,WAAW;YACbA,UAAUS,MAAM,CACdvR,iBAAiB;gBACf2a,SAASW,uBAAuB5M;gBAChCwgB,WAAWC,yBAAyBF;gBACpCrU,mBAAmB9O,KAAKG,KAAK,CAAC,AAAC8C,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAMmgB;IACR,SAAU;QACR,kDAAkD;QAClD,MAAMptB,qBAAqButB,GAAG;QAE9B,IAAI1gB,eAAe,CAACY,QAAQC,GAAG,CAAC8f,gBAAgB,EAAE;YAChDC,yBAAyBrgB;QAC3B;QAEA,6DAA6D;QAC7D,MAAMtO;QACNmB;QAEA,IAAI8M,kBAAkBK,cAAc;YAClCjL,YAAY;gBACV4K;gBACA2gB,MAAM;gBACN9T,YAAYjO;gBACZxH,SAASiJ,aAAajJ,OAAO;gBAC7BwpB,gBAAgB9gB;gBAChB+gB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAASvO;IACP1gB,IAAIwS,KAAK,CACP,CAAC,0MAA0M,CAAC;IAE9M1D,QAAQQ,IAAI,CAAC;AACf;AAEA,SAASwf,yBAAyB1mB,MAA2B;IAC3D,IAAI8mB,aACF,CAAC,8CAA8C,CAAC,GAChD50B,KACE,CAAC,yEAAyE,CAAC;IAE/E40B,cACE,WACA50B,KACE;IAEJ40B,cACE;IAEF,IAAI,EAAC9mB,0BAAAA,OAAQ+C,YAAY,CAACgkB,0BAA0B,GAAE;QACpDD,cACE;IACJ;IAEAA,cACE;IACFA,cACE;IAEFlvB,IAAIiG,IAAI,CAACipB;AACX;AAEA,SAASpU,uBAAuB5M,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAIY,QAAQC,GAAG,CAACqgB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAST,yBAAyBvc,GAAY;IAC5C,MAAMC,OAAO7N,qBAAqB4N;IAClC,IAAIC,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAID,eAAeE,SAAS,UAAUF,OAAO,OAAOA,IAAIC,IAAI,KAAK,UAAU;QACzE,OAAOD,IAAIC,IAAI;IACjB;IAEA,IAAID,eAAeE,OAAO;QACxB,OAAOF,IAAIid,IAAI;IACjB;IAEA,OAAO;AACT", "ignoreList": [0]}